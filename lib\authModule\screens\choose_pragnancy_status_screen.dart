// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../colors.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/my_text_button.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class ChoosePragnancyStatusScreen extends StatefulWidget {
  const ChoosePragnancyStatusScreen({super.key});

  @override
  State<ChoosePragnancyStatusScreen> createState() =>
      _ChoosePragnancyStatusScreenState();
}

class _ChoosePragnancyStatusScreenState
    extends State<ChoosePragnancyStatusScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  bool isLoading = false;

  featureComingSoonDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialogBox(
            buttonsAxisAligment: MainAxisAlignment.center,
            title: language['featureComingSoon'],
            firstButton: FilledDialogButton(
              onPressed: pop,
              text: language['ok'],
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
        backgroundColor: white,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16, right: 16, top: dW * 0.1),
                child: Text.rich(
                  TextSpan(children: [
                    TextSpan(text: language['personalize']),
                    TextSpan(
                      text: language['1'],
                      style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Inter',
                          color: Color(0xff1D1E22),
                          fontSize: 12),
                    ),
                    TextSpan(text: language['step']),
                  ]),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: tS * 12,
                    color: const Color(0xff6B6C75),
                    fontFamily: 'Inter',
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 16, right: 16, top: dW * 0.03),
                child: Text(
                  language['areYouPregnant'],
                  style: Theme.of(context).textTheme.headline1!.copyWith(
                        fontSize: tS * 16,
                        color: const Color(0XFF1D1E22),
                      ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  String status = 'Pregnant';
                  push(NamedRoute.selectPragnancyWeekScreen,
                      arguments: SelectPragnancyWeekArguments(status: status));
                },
                child: Container(
                  margin: EdgeInsets.only(left: 16, right: 16, top: dW * 0.09),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xffD0D0D0),
                    ),
                  ),
                  child: Row(
                    children: [
                      Image.asset(
                        'assets/images/pregnant_graphic.png',
                        height: 196,
                      ),
                      Text(
                        language['yesIAm'],
                        style: Theme.of(context).textTheme.headline1!.copyWith(
                              fontSize: tS * 16,
                              color: const Color(0XFF84858E),
                            ),
                      ),
                    ],
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  featureComingSoonDialog();
                },
                child: Container(
                  margin: EdgeInsets.only(left: 16, right: 16, top: dW * 0.05),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xffD0D0D0),
                    ),
                  ),
                  child: Row(
                    children: [
                      Image.asset(
                        'assets/images/no_pregnant_graphic.png',
                        height: 196,
                      ),
                      Text(
                        language['noButWantTo'],
                        style: Theme.of(context).textTheme.headline1!.copyWith(
                              fontSize: tS * 14,
                              color: const Color(0XFF84858E),
                            ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                    left: 16, right: 16, bottom: 16, top: dW * 0.05),
                child: MyTextButton(
                  color: Colors.grey,
                  text: language['wantSkip'],
                  onPressed: () {
                    push(NamedRoute.bottomNavBarScreen,
                        arguments: BottomNavArgumnets());
                  },
                ),
              ),
            ],
          ),
        ));
  }
}
