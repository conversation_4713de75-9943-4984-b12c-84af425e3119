// ignore_for_file: depend_on_referenced_packages, deprecated_member_use, use_build_context_synchronously, unused_import, unused_local_variable

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:localstorage/localstorage.dart';
import 'package:nine_and_beyond/authModule/screens/verify_otp_acreen.dart';
import 'package:provider/provider.dart';

import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../commonWidgets/policy_text_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class LoginScreen extends StatefulWidget {
  // final LoginSceenArguments args;
  const LoginScreen({
    Key? key,
  }) : super(key: key);

  @override
  LoginScreenState createState() => LoginScreenState();
}

class LoginScreenState extends State<LoginScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');

  bool isLoading = false;
  bool valid = true;

  double height = 0;
  double width = 0;
  double tS = 0;
  Map language = {};

  TextEditingController mobileNo = TextEditingController();
  FocusNode mobileFocus = FocusNode();
  Future<bool> _willPopCallback() async {
    return true;
    // SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    // return true;
  }

  @override
  void initState() {
    super.initState();
  }

  getOTP() async {
    if (mobileNo.text.isEmpty ||
        (mobileNo.text.length > 10 || mobileNo.text.length < 10)) {
      setState(() {
        valid = false;
      });
      return;
    }
    setState(() {
      valid = true;
    });

    // push(NamedRoute.verifyOtpScreen,
    //     arguments: VerifyOtpArguments(
    //       mobileNo: mobileNo.text,
    //     ));

    try {
      setState(() {
        isLoading = true;
      });

      final data = await Provider.of<AuthProvider>(context, listen: false)
          .sendOTPtoUser(mobileNo.text.toString());
      if (!data["success"]) {
        showSnackbar(
            'Something went wrong, Check internet connection', Colors.red);
      } else {
        if (data['result']['type'] == 'success') {
          push(NamedRoute.verifyOtpScreen,
              arguments: VerifyOtpArguments(
                mobileNo: mobileNo.text,
                // address: widget.args.address,
              ));
        } else {
          showSnackbar(
              'Something went wrong, Check internet connection', Colors.red);
        }
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();

    mobileNo.dispose();
  }

  @override
  Widget build(BuildContext context) {
    height = MediaQuery.of(context).size.height;
    width = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    final double kbHeight = MediaQuery.of(context).viewInsets.bottom;
    bool isValid = mobileNo.text.length == 10;
    return WillPopScope(
      onWillPop: _willPopCallback,
      child: GestureDetector(
        onTap: hideKeyBoard,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: Container(
              margin: EdgeInsets.symmetric(
                  horizontal: width * 0.055, vertical: width * 0.2),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: height * 0.06,
                    ),
                    child: Image.asset(
                      'assets/images/login_graphic.png',
                      height: 124,
                      width: 168,
                    ),
                  ),
                  Text(
                    language['mobileNumber'],
                    style: Theme.of(context).textTheme.headline1!.copyWith(
                          fontSize: tS * 20,
                          color: const Color(0XFF434343),
                        ),
                  ),
                  SizedBox(height: width * 0.014),
                  Text(
                    language['mobileNumberToProceed'],
                    style: Theme.of(context).textTheme.headline3!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFF737373),
                        ),
                  ),
                  SizedBox(height: width * 0.06),
                  Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: width * 0.22,
                            child: TextFormField(
                              readOnly: true,
                              enabled: false,
                              style: Theme.of(context)
                                  .textTheme
                                  .headline1!
                                  .copyWith(
                                    fontSize: tS * 15,
                                    color: appBarTitleColor,
                                  ),
                              decoration: InputDecoration(
                                  contentPadding:
                                      const EdgeInsets.only(left: 15),
                                  hintText: '+91',
                                  hintStyle: Theme.of(context)
                                      .textTheme
                                      .headline2!
                                      .copyWith(
                                          fontSize: tS * 14,
                                          color: const Color(0xff000000)),
                                  prefixIcon: const Padding(
                                    padding: EdgeInsets.only(left: 12),
                                    child: AssetSvgIcon(
                                      'flag_icon',
                                    ),
                                  ),
                                  prefixIconConstraints:
                                      const BoxConstraints(maxHeight: 24),
                                  border: const OutlineInputBorder()),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextFormField(
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly
                              ],
                              style: Theme.of(context)
                                  .textTheme
                                  .headline1!
                                  .copyWith(
                                    fontSize: tS * 15,
                                    color: appBarTitleColor,
                                    letterSpacing: .5,
                                  ),
                              cursorColor: Theme.of(context).primaryColor,
                              decoration: InputDecoration(
                                  suffixIcon: Padding(
                                      padding: const EdgeInsets.only(right: 12),
                                      child: mobileNo.text.length == 10
                                          ? const AssetSvgIcon(
                                              'circle_check_enable',
                                            )
                                          : const AssetSvgIcon(
                                              'circle_check_disable',
                                            )),
                                  suffixIconConstraints:
                                      const BoxConstraints(maxHeight: 18),
                                  contentPadding:
                                      const EdgeInsets.only(left: 15),
                                  hintStyle: Theme.of(context)
                                      .textTheme
                                      .headline3!
                                      .copyWith(
                                          fontSize: tS * 12,
                                          color: const Color(0xff84858E)),
                                  hintText: language['enterMobileNumber'],
                                  counterStyle: const TextStyle(
                                      height: double.minPositive),
                                  counterText: "",
                                  border: const OutlineInputBorder()),
                              controller: mobileNo,
                              focusNode: mobileFocus,
                              keyboardType: TextInputType.phone,
                              maxLength: 10,
                              onChanged: (value) {
                                if (value.isNotEmpty) {
                                  setState(() {
                                    valid = true;
                                  });
                                }
                              },
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return language['enterPhoneNumber'];
                                } else if (value.length < 10) {
                                  return 'validPhoneNumber';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      // Padding(
                      //   padding: const EdgeInsets.symmetric(horizontal: 6.0),
                      //   child: Divider(
                      //     height: .5,
                      //     thickness: 1,
                      //     color: Colors.grey.shade400,
                      //   ),
                      // )
                    ],
                  ),

                  SizedBox(height: width * 0.05),
                  Stack(
                    children: [
                      GradientButton(
                        elevation: isValid ? 2 : 0,
                        isLoading: isLoading,
                        onPressed: isValid ? getOTP : () {},
                        buttonText: language['getOTP'],
                      ),
                      if (!isValid)
                        Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            top: 0,
                            child: Container(
                              color: Colors.white.withOpacity(.7),
                            )),
                    ],
                  ),

                  // SizedBox(height: width * 0.3),

                  // if (widget.args.address == null && kbHeight < 150) ...[
                  // Container(
                  //   width: width,
                  //   margin: EdgeInsets.only(top: width * 0.05),
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     children: [
                  //       Text(
                  //         language['or'],
                  //         style:
                  //             Theme.of(context).textTheme.headline3!.copyWith(
                  //                   fontSize: tS * 13,
                  //                   color: const Color(0xFF9798A3),
                  //                 ),
                  //       ),
                  //       GestureDetector(
                  //         onTap: () => push(NamedRoute.qrScannerScreen,
                  //             arguments: const QrScannerScreenArguments()),
                  //         child: Container(
                  //           margin: EdgeInsets.only(top: width * 0.04),
                  //           child: Row(
                  //             mainAxisAlignment: MainAxisAlignment.center,
                  //             children: [
                  //               Text(
                  //                 language['noAccount'],
                  //                 style: Theme.of(context)
                  //                     .textTheme
                  //                     .headline2!
                  //                     .copyWith(
                  //                       // decoration: TextDecoration.underline,
                  //                       fontSize: tS * 14,
                  //                       height: 1.3,
                  //                       color: grayColor,
                  //                     ),
                  //               ),
                  //               SizedBox(width: width * 0.015),
                  //               GradientWidget(
                  //                 gradient: linearGradient,
                  //                 child: Text(
                  //                   language['signup'],
                  //                   style: Theme.of(context)
                  //                       .textTheme
                  //                       .headline2!
                  //                       .copyWith(
                  //                         decoration:
                  //                             TextDecoration.underline,
                  //                         fontSize: tS * 15,
                  //                         color: themeColor,
                  //                       ),
                  //                 ),
                  //               ),
                  //             ],
                  //           ),
                  //         ),
                  //       ),
                  //     ],
                  //   ),
                  // ),
                  // ],
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(
                        bottom:
                            iOSCondition(height) ? width * 0.0 : width * 0.125),
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: PolicyTextWidget(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
