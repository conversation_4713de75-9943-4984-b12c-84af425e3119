// ignore_for_file: depend_on_referenced_packages, unnecessary_this

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:localstorage/localstorage.dart';
import 'package:nine_and_beyond/chatGPTModule/providers/chat_gpt_provider.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/diaryModule/providers/diary_provider.dart';
import 'package:nine_and_beyond/homeModule/providers/home_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/theme_manager.dart';
import 'package:provider/provider.dart';
import 'authModule/providers/auth_provider.dart';
import 'authModule/screens/splash_screen.dart';
import 'navigation/navigation_service.dart';
import 'navigation/navigators.dart';
import 'navigation/routes.dart';

final LocalStorage storage = LocalStorage('9&BEYOND');

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

// Future<void> backgroundHandler(RemoteMessage message) async {}

awaitStorageReady() async {
  await storage.ready;
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  // FirebaseMessaging.onBackgroundMessage(backgroundHandler);

  if (Platform.isAndroid) {
    var androidInfo = await DeviceInfoPlugin().androidInfo;
    if (androidInfo.version.release != null &&
        double.parse(androidInfo.version.release!.split('')[0]) <= 8.0) {
      HttpOverrides.global = MyHttpOverrides();
    }
  }

  return runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    myInit();
  }

  myInit() async {
    // Permission.accessMediaLocation.request();
    await this.initDynamicLinks();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      this.initDynamicLinks();
    }
  }

  processDynamicLink(PendingDynamicLinkData dynamicLink) async {
    final Uri deepLink = dynamicLink.link;
    final queryParams = deepLink.queryParameters;

    // View shared activity
    var activityId = queryParams['activityId'];
    var activityType = queryParams['activityType'];
    if (activityId != null && activityType != null) {
      push(
        NamedRoute.loadingScreen,
        arguments:
            LoadingScreenArguments(featureId: activityId, type: activityType),
      );
    }
  }

  initDynamicLinks() async {
    FirebaseDynamicLinks.instance.onLink.listen((dynamicLinkData) {
      processDynamicLink(dynamicLinkData);
    }).onError((error) {
      debugPrint('onLink error');
      debugPrint(error.message);
    });
    final PendingDynamicLinkData? dynamicLink =
        await FirebaseDynamicLinks.instance.getInitialLink();
    if (dynamicLink != null) {
      processDynamicLink(dynamicLink);
    }
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeNotifier()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => HomeProvider()),
        ChangeNotifierProvider(create: (_) => DiaryProvider()),
        ChangeNotifierProvider(create: (_) => DailyActivityProvider()),
        ChangeNotifierProvider(create: (_) => ChatGPTProvider()),
      ],
      child: Consumer<ThemeNotifier>(
        builder: (context, theme, _) => MaterialApp(
            navigatorKey: navigatorKey,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                child: child!,
              );
            },
            title: '9 & Beyond',
            theme: theme.getTheme(),
            debugShowCheckedModeBanner: false,
            initialRoute: '/',
            onGenerateRoute: generateRoute,
            routes: {
              '/': (BuildContext context) => const SplashScreen(),
              // '/': (BuildContext context) =>
              //     HomeScreen(args: HomeScreenArguments()),
            }),
      ),
    );
  }
}
