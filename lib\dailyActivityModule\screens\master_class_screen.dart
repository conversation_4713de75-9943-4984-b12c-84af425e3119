// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../widgets/activity_illustration_image.dart';
import '../widgets/master_class_widget.dart';

class MasterClassScreen extends StatefulWidget {
  final MasterClassScreenArguments args;
  const MasterClassScreen({super.key, required this.args});

  @override
  State<MasterClassScreen> createState() => _MasterClassScreenState();
}

class _MasterClassScreenState extends State<MasterClassScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  List<MasterClass> masterClasses = [];
  Map videoPercentage = {};

  fetchMasterClass() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .masterClasses
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchMasterClass(accessToken: user.accessToken, body: {});
    if (response['success']) {
    } else {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  forShowingNextVideo() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final dailyActivityProvider =
        Provider.of<DailyActivityProvider>(context, listen: false);
    int currentCount = int.tryParse(user.masterClassCount) ?? 0;
    int totalMasterClasses = dailyActivityProvider.masterClasses.length;
    if (currentCount != totalMasterClasses) {
      final response = await dailyActivityProvider
          .updateMasterClassCountInUser(accessToken: user.accessToken, body: {
        'userId': user.id,
        'masterClassCount': (currentCount + 1).toString(),
      });

      if (response['success']) {
        await authProvider
            .updateMasterClassCount(response['result']['masterClassCount']);
        fetchMasterClass();
      } else {
        showSnackbar(response['message']);
      }
    }
  }

  myInit() {
    var auth = Provider.of<AuthProvider>(context, listen: false);
    videoPercentage = auth.masterClass;
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<AuthProvider>(context, listen: false).user;
    myInit();
    fetchMasterClass();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    masterClasses = Provider.of<DailyActivityProvider>(context).masterClasses;

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(title: widget.args.title, dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            children: [
              SizedBox(height: dW * 0.03),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: masterClasses.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, i) => MasterClassWidget(
                          masterClass: masterClasses[i],
                          onVideoProgress: forShowingNextVideo,
                          user: user,
                          index: i,
                          videoPercentage:  videoPercentage,
                        ),
                      ),
                      ActivityIllustrationImage('master_class_illus.png'),
                    ],
                  ),
                ),
              )
            ],
          );
  }
}
