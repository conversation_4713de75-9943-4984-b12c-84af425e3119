// ignore_for_file: avoid_print, prefer_final_fields, avoid_function_literals_in_foreach_calls

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';

import '../../api.dart';
import '../../authModule/model/user_model.dart';
import '../../http_helper.dart';

class DailyActivityProvider with ChangeNotifier {
// Daily Activity
  List<DailyActivity> _dailyActivity = [];

  List<DailyActivity> get dailyActivity => [..._dailyActivity];

  // fetchedDailyActivity({
  //   required String accessToken,
  // }) async {
  //   try {
  //     final url = '${webApi['domain']}${endPoint['fetchDailyActivity']}';
  //     final response = await RemoteServices.httpRequest(
  //         method: 'GET', url: url, accessToken: accessToken);

  //     if (response['success']) {
  //       List<DailyActivity> fetchedDailyActivity = [];

  //       response['result'].forEach((dailyActivity) {
  //         fetchedDailyActivity
  //             .add(DailyActivity.jsonToDailyActivity(dailyActivity));
  //       });
  //       _dailyActivity = fetchedDailyActivity;
  //       notifyListeners();
  //     }
  //     return response;
  //   } catch (e) {
  //     return {
  //       'success': false,
  //       'message': 'Failed to get daily activities',
  //     };
  //   }
  // }

  fetchedDailyActivity({
    required String accessToken,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchDailyActivity']}';
      final response = await RemoteServices.httpRequest(
          method: 'GET', url: url, accessToken: accessToken);

      if (response['success']) {
        List<DailyActivity> fetchedDailyActivity = [];

        response['result'].forEach((dailyActivity) {
          final activity = DailyActivity.jsonToDailyActivity(dailyActivity);
          fetchedDailyActivity.add(activity);
        });

        _dailyActivity = fetchedDailyActivity;
        cacheImages(_dailyActivity.map((e) => e.icon).toList());
        notifyListeners();
      }

      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get daily activities',
      };
    }
  }

  // Set Preference
  List<Preferences> _preference = [];

  List<Preferences> get preference => [..._preference];

  Preferences? upcomingActivity;

  Preferences? nextUpcomingActivity;

  num? nextUpcoming;

  Timer? _timer;

  fetchPreferences({
    required String accessToken,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchPreferences']}';
      final response = await RemoteServices.httpRequest(
          method: 'GET', url: url, accessToken: accessToken);

      if (response['success']) {
        List<Preferences> fetchedPreference = [];

        response['result'].forEach((preference) {
          fetchedPreference.add(Preferences.jsonToPreferences(preference));
        });
        _preference = fetchedPreference;
        cacheImages(_preference.map((e) => e.dailyActivity.banner).toList());
        _preference.sort((p1, p2) =>
            p1.dailyActivity.position.compareTo(p2.dailyActivity.position));
        notifyListeners();
        setUpcomingActivities();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get preferences',
      };
    }
  }

  setUpcomingActivities() {
    List<num> allTimeslots = [];
    _preference.forEach((element) {
      allTimeslots.addAll(element.timeSlots);
    });
    allTimeslots.sort();
    final now = convertDateToNumber(DateTime.now());
    num? upcoming;
    nextUpcoming = null;
    for (int i = 0; i < allTimeslots.length; i++) {
      if (allTimeslots[i] >= now && upcoming == null) {
        upcoming = allTimeslots[i];
      }
      if (upcoming != null && allTimeslots[i] > upcoming) {
        nextUpcoming = allTimeslots[i];
        break;
      }
    }
    _preference.forEach((element) {
      if (upcoming != null && element.timeSlots.contains(upcoming)) {
        upcomingActivity = element;
      }
      if (nextUpcoming != null && element.timeSlots.contains(nextUpcoming)) {
        nextUpcomingActivity = element;
      }
    });
    notifyListeners();
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }
    if (upcoming != null) {
      final minutes =
          convertNumberToDate(upcoming).difference(DateTime.now()).inMinutes;
      _timer = Timer(Duration(minutes: minutes + 10), setUpcomingActivities);
    }
  }

  savePreference({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['savePreferences']}';
      final response = await RemoteServices.httpRequest(
          method: 'PUT', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        int i = _preference
            .indexWhere((element) => element.id == body['preferenceId']);
        if (body['isActive'] != null) {
          _preference[i].isActive = body['isActive'];
        } else {
          _preference[i].timeSlots = body['timeSlots'];
        }
        notifyListeners();
      }
      return response;
    } catch (e) {
      print(e);
      return {
        'success': false,
        'message': 'Failed to add preferences',
      };
    }
  }

  // Yoga
  List<Yoga> _yoga = [];

  List<Yoga> get yoga => [..._yoga];

  fetchedYoga({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchYoga']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<Yoga> fetchedYoga = [];

        response['result'].forEach((yoga) {
          fetchedYoga.add(Yoga.jsonToYoga(yoga));
        });
        _yoga = fetchedYoga;
        cacheImages(_yoga.map((e) => e.thumbnail).toList());
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get yoga',
      };
    }
  }

// Filter Yoga
  getYogaUsingTrimester(int selectedTrimester) {
    return yoga.where((e) {
      return e.trimester.contains(selectedTrimester);
    }).toList();
  }

// Chant

  List<Chant> _chants = [];

  List<Chant> get chants => [..._chants];

  fetchedChant({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchChant']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<Chant> fetchedChant = [];

        response['result'].forEach((chant) {
          fetchedChant.add(Chant.jsonToChant(chant));
        });
        _chants = fetchedChant;
        cacheImages(_chants.map((e) => e.albumArt).toList());
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get chant',
      };
    }
  }

// Filter Chant

  getChantUsingMonth(int selectedMonth) {
    final List<Chant> chantsToReturn = chants.where((e) {
      return e.month.contains(selectedMonth);
    }).toList();
    chantsToReturn.sort((c1, c2) {
      final int c1Pos = c1.position
          .firstWhere((element) => element['month'] == selectedMonth)['number'];
      final int c2Pos = c2.position
          .firstWhere((element) => element['month'] == selectedMonth)['number'];
      return c1Pos.compareTo(c2Pos);
    });
    return chantsToReturn;
  }

// Meditation

  List<Meditation> _meditation = [];

  List<Meditation> get meditation => [..._meditation];

//   fetchedMeditation({
//     required String accessToken,
//   }) async {
//     try {
//       final url = '${webApi['domain']}${endPoint['fetchMeditation']}';
//       final response = await RemoteServices.httpRequest(
//           method: 'GET', url: url, accessToken: accessToken);

//       if (response['success']) {
//         List<Meditation> fetchedMeditation = [];

//         response['result'].forEach((meditation) {
//           fetchedMeditation.add(Meditation.jsonToMeditation(meditation));
//         });
//         _meditation = fetchedMeditation;
//         cacheImages(_meditation.map((e) => e.albumArt).toList());
//         cacheImages(_meditation.map((e) => e.albumArtGif).toList());
//         notifyListeners();
//       }
//       return response;
//     } catch (e) {
//       return {
//         'success': false,
//         'message': 'Failed to get meditation',
//       };
//     }
//   }

// // Filter Meditation
//   getMeditationUsingTrimester(int selectedTrimester) {
//     final List<Meditation> meditationsToReturn = meditation.where((e) {
//       return e.trimester.contains(selectedTrimester);
//     }).toList();
//     meditationsToReturn.sort((c1, c2) {
//       final int c1Pos = c1.position.firstWhere(
//           (element) => element['trimester'] == selectedTrimester)['number'];
//       final int c2Pos = c2.position.firstWhere(
//           (element) => element['trimester'] == selectedTrimester)['number'];
//       return c1Pos.compareTo(c2Pos);
//     });
//     return meditationsToReturn;
//   }

  fetchedMeditation({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchVideoMeditation']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<Meditation> fetchedMeditation = [];

        response['result'].forEach((meditation) {
          fetchedMeditation.add(Meditation.jsonToMeditation(meditation));
        });
        _meditation = fetchedMeditation;
        cacheImages(_meditation.map((e) => e.thumbnail).toList());
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get meditation',
      };
    }
  }

// Filter Meditation
  getMeditationUsingTrimester(int selectedTrimester) {
    return meditation.where((e) {
      return e.trimester.contains(selectedTrimester);
    }).toList();
  }

  // Bedtime Story

  List<BedTimeCategory> _bedTimeCategories = [];
  List<BedTimeCategory> get bedTimeCategories => [..._bedTimeCategories];

  List<BedtimeStory> _bedtimeStory = [];

  List<BedtimeStory> get bedtimeStory => [..._bedtimeStory];

  List<BedtimeStory> _likedBedtimeStories = [];

  List<BedtimeStory> get likedBedtimeStories => [..._likedBedtimeStories];

  fetchedBedtimeStory({
    required String accessToken,
    required String query,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchBedtimeStory']}$query';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<BedtimeStory> fetchedBedtimeStory = [];

        response['result'].forEach((bedtimeStory) {
          fetchedBedtimeStory
              .add(BedtimeStory.jsonToBedtimeStory(bedtimeStory));
        });
        if (query.contains('fetchLiked')) {
          _likedBedtimeStories = fetchedBedtimeStory;
          cacheImages(_likedBedtimeStories.map((e) => e.albumArt).toList());
        } else {
          _bedtimeStory = fetchedBedtimeStory;
          List<BedTimeCategory> fetchedBedTimeCategories = [];
          cacheImages(_bedtimeStory.map((e) {
            if (fetchedBedTimeCategories
                    .indexWhere((element) => element.id == e.category.id) ==
                -1) {
              fetchedBedTimeCategories.add(e.category);
            }
            return e.albumArt;
          }).toList());

          _bedTimeCategories = fetchedBedTimeCategories;
          _bedTimeCategories.sort((a, b) => a.position.compareTo(b.position));
        }

        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get bedtime story',
      };
    }
  }

// Filter Bedtime Story
  getBedtimeStoryUsingCategory(String selectedCategory) {
    return bedtimeStory.where((e) {
      return e.category.id == selectedCategory;
    }).toList();
  }

  // likeUnlike

  likeUnlike({required Map body, required String accessToken}) async {
    try {
      final url = '${webApi['domain']}${endPoint['likeUnlike']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );
      if (response['success']) {
        if (body['like'] == false) {
          int i = _likedBedtimeStories
              .indexWhere((element) => element.id == body['bedTimeStory']);
          if (i != -1) {
            _likedBedtimeStories.removeAt(i);
          }
          int j = _bedtimeStory
              .indexWhere((element) => element.id == body['bedTimeStory']);
          if (j != -1) {
            _bedtimeStory[j].isLiked = body['like'];
          }
        }
      }
      notifyListeners();
      return response;
    } catch (e) {
      return {
        'success': false,
      };
    }
  }

// Raga Music

  List<RagaMusic> _ragaMusic = [];

  List<RagaMusic> get ragaMusic => [..._ragaMusic];

  fetchedRagaMusic({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchRagaMusic']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<RagaMusic> fetchedRagaMusic = [];

        response['result'].forEach((ragaMusic) {
          fetchedRagaMusic.add(RagaMusic.jsonToRagaMusic(ragaMusic));
        });
        _ragaMusic = fetchedRagaMusic;
        cacheImages(_ragaMusic.map((e) => e.albumArt).toList());
        notifyListeners();
      }
      return response;
    } catch (e) {
      print(e);
      return {
        'success': false,
        'message': 'Failed to get raga music',
      };
    }
  }

// Filter Raga Music
  getRagaMusicUsingTrimester(int selectedTrimester) {
    final List<RagaMusic> ragaMusicToReturn = ragaMusic.where((e) {
      return e.trimester.contains(selectedTrimester);
    }).toList();
    ragaMusicToReturn.sort((c1, c2) {
      final int c1Pos = c1.position.firstWhere(
          (element) => element['trimester'] == selectedTrimester)['number'];
      final int c2Pos = c2.position.firstWhere(
          (element) => element['trimester'] == selectedTrimester)['number'];
      return c1Pos.compareTo(c2Pos);
    });
    return ragaMusicToReturn;
  }

// Diet

  List<Diet> _diet = [];

  List<Diet> get diet => [..._diet];

  List imageUrls = [];

//   fetchedDiet({
//     required String accessToken,
//   }) async {
//     try {
//       final url = '${webApi['domain']}${endPoint['fetchDiet']}';
//       final response = await RemoteServices.httpRequest(
//           method: 'GET', url: url, accessToken: accessToken);

//       if (response['success']) {
//         List<Diet> fetchedDiet = [];

//         response['result'].forEach((diet) {
//           fetchedDiet.add(Diet.jsonToDiet(diet));
//         });
//         _diet = fetchedDiet;
//         _diet.forEach((element) {
//           imageUrls.add(element.pictureUrl);
//           element.products.forEach((element) {
//             imageUrls.add(element.productPictureUrl);
//           });
//         });
//         notifyListeners();
//       }
//       return response;
//     } catch (e) {
//       print(e);
//       return {
//         'success': false,
//         'message': 'Failed to get diet',
//       };
//     }
//   }

// // Filter Diet
//   getDietUsingTrimesterWithDay(int selectedTrimester) {
//     return diet.where((e) {
//       return e.trimester.contains(selectedTrimester);
//     }).toList();
//   }

  fetchedDiet({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchDiet']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<Diet> fetchedDiet = [];

        response['result'].forEach((diet) {
          fetchedDiet.add(Diet.jsonToDiet(diet));
        });
        _diet = fetchedDiet;
        cacheImages(_diet.map((e) => e.thumbnail).toList());
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get diet',
      };
    }
  }

// Filter Diet
  getDietUsingTrimesterWithDay(int selectedTrimester) {
    return diet.where((e) {
      return e.trimester.contains(selectedTrimester);
    }).toList();
  }

  // getChantUsingMonth(int selectedMonth) {
  //   return chants.where((e) {
  //     return e.month == selectedMonth;
  //   }).toList();
  // }

  fetchSingleActivityById(
      {required String accessToken,
      required String query,
      required String activityType}) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['fetchSingleActivityById']}$query';
      final response = await RemoteServices.httpRequest(
          method: 'GET', url: url, accessToken: accessToken);

      if (response['success']) {
        if (activityType == 'Yoga') {
          Yoga fetchedYoga = Yoga.jsonToYoga(response['result']);
          response['activity'] = fetchedYoga;
        } else if (activityType == 'Chant') {
          Chant fetchedChant = Chant.jsonToChant(response['result']);
          response['activity'] = fetchedChant;
        } else if (activityType == 'Meditation') {
          Meditation fetchedMeditation =
              Meditation.jsonToMeditation(response['result']);
          response['activity'] = fetchedMeditation;
        } else if (activityType == 'Bedtime Story') {
          BedtimeStory fetchedBedtimeStory =
              BedtimeStory.jsonToBedtimeStory(response['result']);
          response['activity'] = fetchedBedtimeStory;
        } else if (activityType == 'Raga Music') {
          RagaMusic fetchedRagaMusic =
              RagaMusic.jsonToRagaMusic(response['result']);
          response['activity'] = fetchedRagaMusic;
        }

        notifyListeners();
      }
      return response;
    } catch (e) {
      print(e);
      return {
        'success': false,
        'message': 'Failed to get activity',
      };
    }
  }

  createSubscription({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['createSubscription']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        accessToken: accessToken,
        body: body,
      );

      if (response['success']) {
        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failed to create subscription'};
    }
  }

  updateMasterClassCountInUser({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['updateMasterClassCountInUser']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to update master class count in user',
      };
    }
  }

  List<MasterClass> _masterClasses = [];
  List<MasterClass> get masterClasses => [..._masterClasses];

  fetchMasterClass({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchMasterClass']}';
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        List<MasterClass> fetchedMasterClasses = [];

        response['result'].forEach((diet) {
          fetchedMasterClasses.add(MasterClass.jsonToMasterClass(diet));
        });
        _masterClasses = fetchedMasterClasses;
        cacheImages(_diet.map((e) => e.thumbnail).toList());
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get master cleasses',
      };
    }
  }
}
