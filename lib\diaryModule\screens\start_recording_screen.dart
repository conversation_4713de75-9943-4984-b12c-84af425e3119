// ignore_for_file: deprecated_member_use, avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart' as audio_players;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/commonWidgets/circular_loader.dart';
import 'package:nine_and_beyond/diaryModule/providers/diary_provider.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/diary_background.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../model/diary_model.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_recorder_platform_interface.dart';

class StartRecordingScreen extends StatefulWidget {
  const StartRecordingScreen({super.key});

  @override
  State<StartRecordingScreen> createState() => _StartRecordingScreenState();
}

class _StartRecordingScreenState extends State<StartRecordingScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  TextTheme get textTheme => Theme.of(context).textTheme;
  TextEditingController nameController = TextEditingController();
  String name = '';

  late User user;
  bool isLoading = false;
  bool isSaving = false;

// For player
  Duration currentPos = Duration.zero;
  Duration playerDuration = Duration.zero;
  final player = AudioPlayer();
  bool isPlaying = false;
  bool isPaused = false;
  bool isInitializingPlayer = false;
  int newCount = 1;

// For recorder
  String? recordedAudio;
  int totalDuration = 0;
  String statusText = '';
  String? recordFilePath;
  bool isRecordingStarted = false;
  int recordDuration = 0;
  Timer? _timer;

  List recordings = [];
  Recording? recording;

  FlutterSoundRecorder? _recorder;
  bool _recorderIsInited = false;

  saveRecording() async {
    if (recordFilePath == null) {
      return;
    }

    final diaryProvider = Provider.of<DiaryProvider>(context, listen: false);

    if (_recorder!.isRecording) {
      await _recorder!.stopRecorder();
    }

    _timer?.cancel();
    isRecordingStarted = false;

    setState(() {
      isSaving = true;
    });

    Map<String, String> files = {};
    files['audio_${recordings.length + 1}.wav'] = recordFilePath!;

    if (recording == null) {
      final response = await diaryProvider.saveRecording(
        body: {
          'duration': getAudioDurationText(),
          'name': name,
        },
        files: files,
        accessToken: user.accessToken,
      );

      setState(() {
        isSaving = false;
      });

      pop();

      if (!response['success']) {
        showSnackbar(language[response['message']]);
      }
    }
  }

  void startTimer() {
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (Timer timer) {
        setState(() {
          recordDuration++;
          seconds++;
        });
      },
    );
  }

  int seconds = 0;
  int minutes = 0;

  getAudioDurationText() {
    minutes = (recordDuration / 60).floor();
    if (seconds >= 60) seconds = 0;
    return '${minutes < 10 ? '0$minutes' : minutes} : ${seconds < 10 ? '0$seconds' : seconds}';
  }

  Future<bool> checkPermission() async {
    if (!await Permission.microphone.isGranted) {
      PermissionStatus status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        return false;
      }
    }
    return true;
  }

  // For recorder

  void startRecord() async {
    bool hasPermission = await checkPermission();
    if (hasPermission) {
      try {
        statusText = "Recording...";
        recordFilePath = await getFilePath();
        isRecordingStarted = true;

        await _recorder!.startRecorder(
          toFile: recordFilePath,
          codec: Codec.pcm16WAV,
          audioSource: AudioSource.microphone,
        );

        recordDuration = 0;
        seconds = 0;
        startTimer();
      } catch (e) {
        print("Error starting recording: $e");
        statusText = "Error starting recording";
        isRecordingStarted = false;
      }
    } else {
      statusText = "No microphone permission";
    }
    setState(() {});
  }

  void pauseRecord() async {
    try {
      if (_recorder != null) {
        if (_recorder!.isPaused) {
          await _recorder!.resumeRecorder();
          statusText = "Recording...";
          startTimer();
          recordedAudio = recordFilePath;
          isRecordingStarted = true;
        } else {
          await _recorder!.pauseRecorder();
          statusText = "Recording pause...";
          _timer?.cancel();
          recordedAudio = recordFilePath;
          isRecordingStarted = false;
        }
        setState(() {});
      }
    } catch (e) {
      print("Error pausing/resuming recording: $e");
    }
  }

  void stopRecord() async {
    try {
      if (_recorder != null && _recorder!.isRecording) {
        await _recorder!.stopRecorder();

        if (recordFilePath != null) {
          final file = File(recordFilePath!);
          if (await file.exists()) {
            await file.delete();
          }
        }

        statusText = "Record complete";
        isRecordingStarted = false;
        _timer?.cancel();
        _timer = null;
        recordedAudio = recordFilePath;
        currentPos = Duration.zero;
        setState(() {});
      }
    } catch (e) {
      print("Error stopping recording: $e");
    }
  }

  // void resumeRecord() {
  //   bool s = RecordMp3.instance.resume();
  //   if (s) {
  //     statusText = "Recording...";
  //     setState(() {});
  //   }
  // }

  Future<String> getFilePath() async {
    Directory storageDirectory = await getApplicationDocumentsDirectory();
    String sdPath = "${storageDirectory.path}/record";
    var d = Directory(sdPath);
    if (!d.existsSync()) {
      d.createSync(recursive: true);
    }
    await storage.ready;
    final countJson = await storage.getItem('recordCount');
    if (countJson != null) {
      final count = json.decode(countJson);
      if (count != null) {
        newCount = count + 1;
      }
    }
    storage.setItem('recordCount', newCount.toString());
    return "$sdPath/audio${newCount.toString()}.wav";
  }

  resetAudio() {
    statusText = '';
    recordDuration = 0;
    seconds = 0;
    recordedAudio = null;
    pop();
    setState(() {});
  }

  confirmAudio() {
    pop(context);
    if (recordedAudio != null) {
      // dataPop(context, recordedAudio!);
    } else {
      pop(context);
    }
  }

  // For provider

  seekToDuration(double d) async // v is between 0.0 and 2000 (milliseconds)
  {
    await player.seek(Duration(milliseconds: d.toInt()));
  }

  play() async {
    try {
      final bytes = File(recordedAudio!).readAsBytesSync();
      await player.play(BytesSource(bytes));
      setState(() {});
    } catch (e) {
      print(e);
      showSnackbar('Failed to play audio note', Colors.red);
    }
  }

  pausePlayer() async {
    await player.pause();
    setState(() {});
  }

  resumePlayer() async {
    await player.resume();
    setState(() {});
  }

  initAudioPlayer() {
    if (Platform.isIOS) {
      final AudioContext audioContext = AudioContext(
        iOS: AudioContextIOS(
          defaultToSpeaker: true,
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.defaultToSpeaker,
            AVAudioSessionOptions.mixWithOthers,
          ],
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.sonification,
          usageType: AndroidUsageType.assistanceSonification,
          audioFocus: AndroidAudioFocus.none,
        ),
      );
      AudioPlayer.global.setGlobalAudioContext(audioContext);
    }
    player.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          isPlaying = state == audio_players.PlayerState.playing;
          isPaused = state == audio_players.PlayerState.paused;
        });
      }
    });

    player.onDurationChanged.listen((newDuration) {
      if (mounted) {
        setState(() {
          playerDuration = newDuration;
        });
      }
    });

    player.onPositionChanged.listen((newPos) {
      if (mounted) {
        setState(() {
          currentPos = newPos;
        });
      }
    });

    player.onPlayerComplete.listen((event) {
      if (mounted) {
        setState(() {
          isPlaying = false;
          currentPos = Duration.zero;
        });
      }
    });
  }

  releasePlayer() async {
    await player.release();
  }

  // audioPlayerWidget() {
  //   return Slider(
  //     value: currentPos.inMilliseconds.toDouble(),
  //     onChanged: seekToDuration,
  //     max: playerDuration.inMilliseconds.toDouble(),
  //   );
  // }

  renameDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  language['renameAudio'],
                  style: textTheme.headline2!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0xFF1D1E22),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: dW * 0.03),
                CustomTextFieldWithLabel(
                  hintText: language['enterAudioName'],
                  controller: nameController,
                  borderColor: greyBorderColor,
                ),
                SizedBox(height: dW * 0.09),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    DialogTextButton(
                      onPressed: () {
                        pop();
                      },
                      text: language['cancel'],
                    ),
                    SizedBox(width: dW * 0.08),
                    FilledDialogButton(
                      onPressed: () {
                        saveRecordingNameLocally();
                      },
                      text: language['rename'],
                    ),
                  ],
                )
              ],
            ),
          )),
    );
  }

  void saveRecordingNameLocally() {
    String newName = nameController.text;
    storage.setItem('name', newName);
    setState(() {
      name = newName;
    });
    pop();
  }

  discardDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialogBox(
            title: language['discardRecording'],
            firstButton: DialogTextButton(
              onPressed: () {
                pop();
                pop();
              },
              text: language['discard'],
            ),
            secondButton: FilledDialogButton(
              onPressed: () {
                saveRecording();
                pop();
              },
              text: language['save'],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();
    _recorder = FlutterSoundRecorder();
    _initializeRecorder();
    user = Provider.of<AuthProvider>(context, listen: false).user;
    initAudioPlayer();
  }

  Future<void> _initializeRecorder() async {
    try {
      await Permission.microphone.request();

      await _recorder!.openRecorder();

      _recorderIsInited = true;

      startRecord();
    } catch (e) {
      print("Error initializing recorder: $e");
      _recorderIsInited = false;
    }
  }

  @override
  void dispose() {
    if (_recorderIsInited) {
      _recorder!.closeRecorder();
    }
    _recorder = null;
    if (_timer != null) _timer!.cancel();
    releasePlayer();
    player.onPlayerStateChanged.drain();
    player.onDurationChanged.drain();
    player.onPositionChanged.drain();
    player.onPlayerComplete.drain();
    nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Stack(
      children: [
        DiaryBackground(),
        WillPopScope(
          onWillPop: () async {
            discardDialog();
            return false;
          },
          child: Scaffold(
            backgroundColor: Colors.transparent,
            body: SingleChildScrollView(
              physics: const ScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    color: const Color(0xff8EBEF4).withOpacity(0.4),
                    width: dW,
                    height: dW * (iOSCondition(dH) ? 0.32 : 0.25),
                    padding: EdgeInsets.only(
                      left: 16,
                      top: dW * 0.065,
                      right: 16,
                    ),
                    child: SafeArea(
                      child: Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                              renameDialog();
                            },
                            child: Container(
                              color: transparentColor,
                              child: Row(
                                children: [
                                  Text(
                                    name.isEmpty ? 'New_Audio1' : name,
                                    style: textTheme.headline1!.copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0XFF363636),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Container(
                                    color: transparentColor,
                                    child: const AssetSvgIcon(
                                      'edit_pen',
                                      color: Color(0XFF363636),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Spacer(),
                          GestureDetector(
                            onTap: () {
                              discardDialog();
                            },
                            child: const AssetSvgIcon(
                              'close_cross',
                              color: Color(0XFF363636),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: dW * 0.05),
                  Container(
                    height: dW * 1.3,
                    width: dW,
                    margin: const EdgeInsets.only(left: 8, right: 8),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                      color: const Color(0xffFFFFFF).withOpacity(0.6),
                    ),
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                              left: 21, right: 21, top: dW * 0.2),
                          child: const AssetSvgIcon(
                            'microphone',
                            color: Color(0XFF363636),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: 21,
                              right: 21,
                              top: dW * 0.1,
                              bottom: dW * 0.1),
                          child: Text(
                            getAudioDurationText(),
                            textAlign: TextAlign.center,
                            style: textTheme.headline1!.copyWith(
                              fontSize: tS * 48,
                              color: const Color(0XFF363636),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: dW * 0.35,
                    width: dW,
                    margin:
                        EdgeInsets.only(left: dW * 0.022, right: dW * 0.022),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                      border: Border.all(
                        width: 1,
                        color: const Color(0xffFFFFFF).withOpacity(0.5),
                      ),
                      color: const Color(0xffFFFFFF).withOpacity(0.6),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Padding(
                        //   padding: EdgeInsets.only(left: dW * 0.1),
                        //   child: Column(
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     children: [

                        //       GestureDetector(
                        //         onTap: () {
                        //           if (!isPlaying) {
                        //             stopRecord();
                        //             startRecord();
                        //           }
                        //         },
                        //         child: AssetSvgIcon(
                        //           'restart',
                        //           color: const Color(0XFF000000),
                        //           height: dW * 0.06,
                        //         ),
                        //       ),
                        //       const SizedBox(height: 10),
                        //       Text(
                        //         language['startAgain'],
                        //         style: textTheme.headline2!.copyWith(
                        //           fontSize: tS * 12,
                        //           color: const Color(0XFFF4F4F4),
                        //         ),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                        // GestureDetector(
                        //   onTap: () {
                        //     pauseRecord();
                        //   },
                        //   child: Container(
                        //       width: dW * 0.17,
                        //       height: dW * 0.17,
                        //       alignment: Alignment.center,
                        //       decoration: BoxDecoration(
                        //           color: Colors.white,
                        //           shape: BoxShape.circle,
                        //           boxShadow: [
                        //             BoxShadow(
                        //                 offset: const Offset(0, 6),
                        //                 color: Colors.grey.withOpacity(0.25),
                        //                 spreadRadius: 2,
                        //                 blurRadius: 5)
                        //           ]),
                        //       child: isRecordingStarted
                        //           ? Container(
                        //               height: dW * 0.17,
                        //               width: dW * 0.17,
                        //               alignment: Alignment.center,
                        //               decoration: BoxDecoration(
                        //                 color: const Color(0xffFFFFFF),
                        //                 borderRadius:
                        //                     BorderRadius.circular(108),
                        //               ),
                        //               child: const AssetSvgIcon('pause'),
                        //             )
                        //           : Container(
                        //               width: dW * 0.12,
                        //               height: dW * 0.12,
                        //               alignment: Alignment.center,
                        //               decoration: BoxDecoration(
                        //                 color: isPlaying
                        //                     ? Colors.red.withOpacity(0.2)
                        //                     : Colors.red,
                        //                 shape: BoxShape.circle,
                        //               ),
                        //             )),
                        // ),

                        GestureDetector(
                          onTap: () {
                            pauseRecord();
                          },
                          child: Container(
                              width: dW * 0.17,
                              height: dW * 0.17,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                        offset: const Offset(0, 6),
                                        color: Colors.grey.withOpacity(0.25),
                                        spreadRadius: 2,
                                        blurRadius: 5)
                                  ]),
                              child: isRecordingStarted
                                  ? Container(
                                      height: dW * 0.17,
                                      width: dW * 0.17,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: const Color(0xffFFFFFF),
                                        borderRadius:
                                            BorderRadius.circular(108),
                                      ),
                                      child: const AssetSvgIcon('pause'),
                                    )
                                  : Container(
                                      width: dW * 0.12,
                                      height: dW * 0.12,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: isPlaying
                                            ? Colors.red.withOpacity(0.2)
                                            : Colors.red,
                                        shape: BoxShape.circle,
                                      ),
                                    )),
                        ),

                        Padding(
                          padding: EdgeInsets.only(
                              left: dW * 0.175, right: dW * 0.03),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  saveRecording();
                                },
                                child: const AssetSvgIcon('stop'),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                language['stopSave'],
                                style: textTheme.headline2!.copyWith(
                                  fontSize: tS * 12,
                                  color: const Color(0XFF363636),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (isSaving)
          Container(
            color: white.withOpacity(0.3),
            height: dH,
            width: dW,
            child: const Center(
              child: CircularLoader(),
            ),
          )
      ],
    );
  }
}
