// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/dotted_seperator.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../widgets/question_charge_widget.dart';

class RechargeWalletScreen extends StatefulWidget {
  const RechargeWalletScreen({Key? key}) : super(key: key);

  @override
  RechargeWalletScreenState createState() => RechargeWalletScreenState();
}

class RechargeWalletScreenState extends State<RechargeWalletScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late TextEditingController _amountController;
  num minimumRecharge = 50;

  List<num> rechargeOptions = [50, 100, 150, 200, 300, 500, 600, 800, 1000];
  num selectedRecharge = 0;

  validateAndNavigate() {
    if (selectedRecharge == 0 && _amountController.text.isEmpty) {
      showSnackbar(language['plsEnterAmt']);
      return;
    } else if (selectedRecharge == 0 &&
        num.parse(_amountController.text.trim()) < 50) {
      showSnackbar(language['plEntrMinAmt'] +
          ' \u20b9 $minimumRecharge ' +
          language['toRchrge']);
      return;
    }

    push(NamedRoute.rechargeWalletSummaryScreen,
        arguments: RechargeWalletSummaryScreenArguments(
            amount: selectedRecharge != 0
                ? selectedRecharge
                : num.parse(_amountController.text.trim())));
  }

  InputBorder get textFormBorder => UnderlineInputBorder(
        borderSide: const BorderSide(color: Color(0xFFD6EAFF), width: 1.4),
        borderRadius: BorderRadius.circular(4),
      );

  Widget get dottedLine => SizedBox(
      width: dW * 0.4,
      child: const DottedSeperator(
        color: Color(0xFF8EBEF4),
        height: 1.2,
      ));

  num get rechargeAmt {
    return selectedRecharge != 0
        ? selectedRecharge
        : _amountController.text.isEmpty
            ? 0
            : num.parse(_amountController.text.trim());
  }

  @override
  void dispose() {
    super.dispose();

    _amountController.dispose();
  }

  @override
  void initState() {
    super.initState();

    _amountController = TextEditingController();
    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    minimumRecharge =
        Provider.of<AuthProvider>(context, listen: false).minimumRecharge;

    return Scaffold(
      appBar: CustomAppBar(title: language['addMoreCredToWal'], dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return GestureDetector(
      onTap: hideKeyBoard,
      child: SizedBox(
        height: dH,
        width: dW,
        child: isLoading
            ? const Center(child: CircularLoader())
            : Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      padding: screenHorizontalPadding(dW),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SizedBox(height: dW * 0.05),
                          QuestionChargeWidget(),
                          Container(
                            margin: EdgeInsets.only(top: dW * 0.05),
                            padding: EdgeInsets.only(
                              bottom: dW * 0.06,
                              left: dW * 0.1,
                              right: dW * 0.1,
                            ),
                            decoration: BoxDecoration(
                              color: white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                width: 1,
                                color: const Color(0xFFE4E9FF),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Stack(
                                  children: [
                                    TextField(
                                      controller: _amountController,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp('[0-9]'))
                                      ],
                                      textAlign: TextAlign.center,
                                      style: Theme.of(context)
                                          .textTheme
                                          .headline2!
                                          .copyWith(
                                            fontSize: tS * 14,
                                            color: lightBlack,
                                          ),
                                      cursorColor: themeColor,
                                      decoration: InputDecoration(
                                        hintText: language['entrAmtToRchrge'],
                                        hintStyle: Theme.of(context)
                                            .textTheme
                                            .headline3!
                                            .copyWith(
                                              fontSize: tS * 12,
                                              color: const Color(0xFFBFC0C8),
                                            ),
                                        contentPadding:
                                            EdgeInsets.only(top: dW * 0.035),
                                        border: textFormBorder,
                                        focusedBorder: textFormBorder,
                                        enabledBorder: textFormBorder,
                                        errorBorder: textFormBorder,
                                        disabledBorder: textFormBorder,
                                        focusedErrorBorder: textFormBorder,
                                        counterText: '',
                                      ),
                                      maxLength: 5,
                                      maxLines: 1,
                                      textInputAction: TextInputAction.done,
                                      keyboardType: TextInputType.number,
                                      onChanged: (_) => setState(() {
                                        selectedRecharge = 0;
                                      }),
                                    ),
                                    if (_amountController.text.isNotEmpty)
                                      Positioned(
                                        bottom: 8.75,
                                        left: dW * 0.245,
                                        child: const Text('\u20b9'),
                                      )
                                  ],
                                ),
                                Padding(
                                  padding: EdgeInsets.only(top: dW * 0.057),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        language['note'] + ': ',
                                        style: Theme.of(context)
                                            .textTheme
                                            .headline3!
                                            .copyWith(
                                              fontSize: tS * 10,
                                              color: const Color(0xFFDB4133),
                                            ),
                                      ),
                                      Text(
                                        language['plEntrMinAmt'] +
                                            ' \u20b9 $minimumRecharge ' +
                                            language['toRchrge'],
                                        style: Theme.of(context)
                                            .textTheme
                                            .headline3!
                                            .copyWith(
                                              fontSize: tS * 10,
                                              color: const Color(0xFF84858E),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(top: dW * 0.03),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        language['yourBal'] + ': ',
                                        style: Theme.of(context)
                                            .textTheme
                                            .headline1!
                                            .copyWith(
                                              fontSize: tS * 10,
                                              color: lightBlack,
                                            ),
                                      ),
                                      Text(
                                        '\u20b9 ${convertAmountString(user.gptCredits.toDouble())}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .headline1!
                                            .copyWith(
                                              fontSize: tS * 10,
                                              color: const Color(0xFF35B52B),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(vertical: dW * 0.05),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                dottedLine,
                                Text(
                                  (language['or']).toLowerCase(),
                                  style: Theme.of(context)
                                      .textTheme
                                      .headline2!
                                      .copyWith(
                                        fontSize: tS * 12,
                                        color: const Color(0xFF84858E),
                                      ),
                                ),
                                dottedLine,
                              ],
                            ),
                          ),
                          Wrap(
                            alignment: WrapAlignment.spaceBetween,
                            runSpacing: 12,
                            children: rechargeOptions
                                .map<Widget>((ro) => RechargeOptionWidget(
                                      ro: ro,
                                      isSelected: ro == selectedRecharge,
                                      selectRecharge: () {
                                        setState(() {
                                          if (ro == selectedRecharge) {
                                            selectedRecharge = 0;
                                            _amountController.text = '';
                                          } else {
                                            selectedRecharge = ro;
                                            _amountController.text =
                                                ro.toString();
                                            hideKeyBoard();
                                          }
                                        });
                                      },
                                    ))
                                .toList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  BottomAlignedWidget(
                      dW: dW,
                      dH: dH,
                      topWidget: Container(
                        padding: EdgeInsets.only(
                          top: dW * 0.012,
                          bottom: dW * 0.005,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              language['rchrgeAmt'] + ' : ',
                              style: Theme.of(context)
                                  .textTheme
                                  .headline3!
                                  .copyWith(
                                    fontSize: tS * 12,
                                    color: const Color(0xFF6B6C75),
                                  ),
                            ),
                            Text(
                              '\u20b9$rechargeAmt',
                              style: Theme.of(context)
                                  .textTheme
                                  .headline2!
                                  .copyWith(
                                    fontSize: tS * 12,
                                    color: lightBlack,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: dW * 0.02),
                        child: SizedBox(
                          height: dW * 0.14,
                          child: CustomGradientButton(
                            isLoading: false,
                            buttonText: language['proceed'],
                            onPressed: validateAndNavigate,
                          ),
                        ),
                      )),
                ],
              ),
      ),
    );
  }
}

class RechargeOptionWidget extends StatelessWidget {
  final num ro;
  final bool isSelected;
  final Function selectRecharge;
  RechargeOptionWidget({
    super.key,
    required this.ro,
    required this.isSelected,
    required this.selectRecharge,
  });

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return GestureDetector(
      onTap: () => selectRecharge(),
      child: Container(
        width: dW * 0.275,
        decoration: isSelected
            ? BoxDecoration(
                color: const Color(0xFFF7F3FF),
                border: Border.all(width: 2, color: const Color(0xFF975EFF)),
                borderRadius: BorderRadius.circular(4),
              )
            : BoxDecoration(
                color: const Color(0xFFF7F8FC),
                border: Border.all(width: 4, color: white),
                borderRadius: BorderRadius.circular(4),
              ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: isSelected ? 17 : 15),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset('assets/images/credit.png', width: 13, height: 13),
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                  '\u20b9 $ro',
                  style: Theme.of(context).textTheme.headline1!.copyWith(
                        fontSize: tS * 12,
                        color: blackColor,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
