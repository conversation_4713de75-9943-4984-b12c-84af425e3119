PODS:
  - audioplay<PERSON>_da<PERSON>win (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/CoreOnly (10.12.0):
    - FirebaseCore (= 10.12.0)
  - Firebase/Messaging (10.12.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.12.0)
  - firebase_core (2.15.0):
    - Firebase/CoreOnly (~> 10.12.0)
    - FlutterMacOS
  - firebase_messaging (14.6.5):
    - Firebase/CoreOnly (~> 10.12.0)
    - Firebase/Messaging (~> 10.12.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseCore (10.12.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.12.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.12.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.12.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - GoogleDataTransport (9.2.3):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.4):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.4):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.4):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.4):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.4)"
  - GoogleUtilities/Reachability (7.11.4):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.4):
    - GoogleUtilities/Logger
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - package_info_plus_macos (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.3.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus_macos (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus_macos/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_macos (from `Flutter/ephemeral/.symlinks/plugins/wakelock_macos/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus_macos/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_macos/macos

SPEC CHECKSUMS:
  audioplayers_darwin: dcad41de4fbd0099cb3749f7ab3b0cb8f70b810c
  file_selector_macos: 468fb6b81fac7c0e88d71317f3eec34c3b008ff9
  Firebase: 07150e75d142fb9399f6777fa56a187b17f833a0
  firebase_core: ff59797157ca9adda4440071643761b41fcd03b3
  firebase_messaging: d489df2f5cf5eb4b1ffb0b920f1d8c6b911f6166
  FirebaseCore: f86a1394906b97ac445ae49c92552a9425831bed
  FirebaseCoreInternal: 950500ad8a08963657f6d8c67b579740c06d6aa1
  FirebaseInstallations: 7b99ef103f013624444c614397038219c45f8e63
  FirebaseMessaging: bb2c4f6422a753038fe137d90ae7c1af57251316
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  GoogleDataTransport: f0308f5905a745f94fb91fea9c6cbaf3831cb1bd
  GoogleUtilities: c63691989bf362ba0505507da00eeb326192e83e
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  package_info_plus_macos: f010621b07802a241d96d01876d6705f15e77c1c
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: d2691c7dd33ed713bf3544850a623080ec693d95
  wakelock_macos: bc3f2a9bd8d2e6c89fee1e1822e7ddac3bd004a9

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.11.3
