// ignore_for_file: deprecated_member_use, avoid_print, no_leading_underscores_for_local_identifiers, must_be_immutable, use_build_context_synchronously

import 'dart:async';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/commonWidgets/circular_loader.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dynamic_Link_api.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../common_functions.dart';
import '../../homeModule/widgets/custom_video_player.dart';
import '../providers/daily_activity_provider.dart';
import 'setting_bottom_sheet.dart';

class AudioPlayerWidget extends StatefulWidget {
  final Chant? chant;
  final Meditation? meditation;
  final RagaMusic? ragaMusic;
  final int currentIndex;
  bool minimised;
  final Function? toggleAudioPlayer;
  final int? selectedPeriod;

  AudioPlayerWidget({
    super.key,
    this.chant,
    this.meditation,
    this.ragaMusic,
    required this.currentIndex,
    this.minimised = false,
    this.toggleAudioPlayer,
    this.selectedPeriod,
  });

  @override
  State<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget>
    with TickerProviderStateMixin {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late String albumArt;
  late String audioUrl;
  late String name;

  late TextEditingController _setCounterController;
  late TextEditingController _setTimerController;

  bool counterColor = false;
  bool timerColor = false;
  String selectedValueforCounter = '';
  String selectedValueforTimer = '';

  bool isLoading = false;

  Timer? _timer;

  int counter = 0;
  int? requiredCount;

  List<Chant> chants = [];
  List<Meditation> meditations = [];
  List<RagaMusic> ragaMusic = [];

  List setCounterOptions = [
    '108 times',
    '200 times',
    '308 times',
  ];

  List setTimerOptions = [
    '10 minutes',
    '15 minutes',
    '30 minutes',
    '45 minutes',
    '1 hour',
    '1.30 hours',
  ];

  final player = AudioPlayer();
  Duration currentPos = Duration.zero;
  Duration duration = Duration.zero;
  int currentIndex = 0;
  bool isPlaying = false;
  bool isPaused = false;

  AudioVersion? selectedAudioVersion;
  RagaMusic? selectedRagaMusic;

  seekToDuration(double d) async {
    await player.seek(Duration(milliseconds: d.toInt()));
    currentPos = Duration(milliseconds: d.toInt());
    setState(() {});
  }

  play({bool firstTime = false}) async {
    try {
      final chants = Provider.of<DailyActivityProvider>(context, listen: false)
          .getChantUsingMonth(widget.selectedPeriod!);
      final meditations =
          Provider.of<DailyActivityProvider>(context, listen: false)
              .getMeditationUsingTrimester(widget.selectedPeriod!);
      final ragaMusic =
          Provider.of<DailyActivityProvider>(context, listen: false)
              .getRagaMusicUsingTrimester(widget.selectedPeriod!);

      if (widget.chant != null && currentIndex < chants.length) {
        name = firstTime ? widget.chant!.name : chants[currentIndex].name;
        albumArt =
            firstTime ? widget.chant!.albumArt : chants[currentIndex].albumArt;
        audioUrl = firstTime ? widget.chant!.audio : chants[currentIndex].audio;
        await player.play(UrlSource(audioUrl));
      }
      // else if (widget.meditation != null &&
      //     currentIndex < meditations.length) {
      //   name = firstTime
      //       ? widget.meditation!.name
      //       : meditations[currentIndex].name;
      //   albumArt = firstTime
      //       ? widget.meditation!.albumArtGif
      //       : meditations[currentIndex].albumArtGif;
      //   audioUrl = firstTime
      //       ? widget.meditation!.audio
      //       : meditations[currentIndex].audio;
      //   await player.play(UrlSource(audioUrl));
      // }
      else if (widget.ragaMusic != null && currentIndex < ragaMusic.length) {
        selectedRagaMusic =
            firstTime ? widget.ragaMusic! : ragaMusic[currentIndex];

        selectedAudioVersion = selectedRagaMusic!.audioVersions.isNotEmpty
            ? ragaMusic[currentIndex].audioVersions[0]
            : null;

        name = selectedRagaMusic!.name;

        if (selectedAudioVersion != null) {
          name += ' - ${selectedAudioVersion!.duration}';
        }

        albumArt = selectedRagaMusic!.albumArt;
        audioUrl =
            selectedAudioVersion != null ? selectedAudioVersion!.audioUrl : '';
        await player.play(UrlSource(audioUrl));
      }

      isPaused = false;
      setState(() {});
    } catch (e) {
      print(e);
      showSnackbar(language['failedPlay'], Colors.red);
    }
  }

  pause() async {
    await player.pause();

    isPaused = true;
    setState(() {});
  }

  resume() async {
    await player.resume();
    isPaused = false;
    setState(() {});
  }

  initAudioPlayer() {
    if (Platform.isIOS) {
      final AudioContext audioContext = AudioContext(
        iOS: AudioContextIOS(
          defaultToSpeaker: true,
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.defaultToSpeaker,
            AVAudioSessionOptions.mixWithOthers,
          ],
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.sonification,
          usageType: AndroidUsageType.assistanceSonification,
          audioFocus: AndroidAudioFocus.none,
        ),
      );
      AudioPlayer.global.setGlobalAudioContext(audioContext);
    }

    play(firstTime: true);
    isLoading = true;
    player.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          isPlaying = state == PlayerState.playing;
        });
      }
      isLoading = false;
    });

    player.onDurationChanged.listen((newDuration) {
      if (mounted) {
        setState(() {
          duration = newDuration;
        });
      }
    });

    player.onPositionChanged.listen((newPos) {
      if (mounted) {
        setState(() {
          currentPos = newPos;
        });
      }
    });

    player.onPlayerComplete.listen((event) {
      if (requiredCount != null && counter < requiredCount!) {
        play();
        counter++;
      } else if (_timer != null && _timer!.isActive) {
        play();
      } else {
        setState(() {
          isPlaying = false;
          currentPos = Duration.zero;
        });
      }
    });
  }

  releasePlayer() async {
    await player.release();
  }

  setCounterSheet() {
    // hideKeyBoard();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      // isDismissible: false,
      constraints: BoxConstraints(maxHeight: dH * 0.4),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => SettingBottomSheet(
        title: language['setCounter'],
        selectedOption: selectedValueforCounter,
        options: setCounterOptions,
        isRagaMusic: widget.ragaMusic != null,
        customSetCounter: true,
      ),
    ).then((value) {
      if (value != null) {
        selectedValueforCounter = value;
        requiredCount = int.parse(value.split(' ')[0]);
        setState(() {
          counterColor = true;
          timerColor = false;
          if (!setCounterOptions.contains(value)) {
            setCounterOptions.insert(0, value);
          }
        });
        showSnackbar('${language['setCounter']} : $selectedValueforCounter',
            const Color(0xff975EFF));
        if (_timer != null && _timer!.isActive) {
          _timer!.cancel();
        }
        selectedValueforTimer = "";
      }
    });
  }

  setTimer(String time) {
    late Duration _duration;
    final split = time.split(" ");
    if (split[1].contains("minute")) {
      _duration = Duration(minutes: int.parse(split[0]));
    } else if (split[1].contains("hour")) {
      final hourSplit = split[0].split(".");
      if (hourSplit.length == 1) {
        _duration = Duration(hours: int.parse(split[0]));
      } else {
        _duration = Duration(
            minutes: int.parse(hourSplit[1]) + (int.parse(hourSplit[0]) * 60));
      }
    }
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }
    _timer = Timer(_duration, () {
      pause();
      _timer!.cancel();
    });
  }

  setTimerSheet() {
    // hideKeyBoard();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      // isDismissible: false,
      constraints: BoxConstraints(maxHeight: dH * 0.5),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => SettingBottomSheet(
        title: language['setTimer'],
        selectedOption: selectedValueforTimer,
        options: setTimerOptions,
        isRagaMusic: widget.ragaMusic != null,
        ragaMusic: selectedRagaMusic,
        selectedDuration:
            selectedAudioVersion == null ? '' : selectedAudioVersion!.duration,
      ),
    ).then((value) async {
      if (value != null) {
        if (widget.ragaMusic != null) {
          selectedAudioVersion = value;
          setState(() {
            isLoading = true;
            showSnackbar(
                '${language['setTimer']} : ${selectedAudioVersion!.duration}',
                const Color(0xff975EFF));
          });
          if (selectedAudioVersion != null) {
            name =
                '${selectedRagaMusic!.name} - ${selectedAudioVersion!.duration}';
          }

          albumArt = selectedRagaMusic!.albumArt;
          audioUrl = selectedAudioVersion != null
              ? selectedAudioVersion!.audioUrl
              : '';
          await player.play(UrlSource(audioUrl));
        } else {
          selectedValueforTimer = value;
          setTimer(value);
          setState(() {
            timerColor = true;
            counterColor = false;
          });
          showSnackbar('${language['setTimer']} : $selectedValueforTimer',
              const Color(0xff975EFF));
          counter = 0;
          selectedValueforCounter = "";
        }
      }
    });
  }

  setData() {
    if (widget.chant != null) {
      albumArt = widget.chant!.albumArt;
      audioUrl = widget.chant!.audio;
      name = widget.chant!.name;
    }
    // else if (widget.meditation != null) {
    //   albumArt = widget.meditation!.albumArtGif;
    //   audioUrl = widget.meditation!.audio;
    //   name = widget.meditation!.name;
    // }
    else if (widget.ragaMusic != null) {
      albumArt = widget.ragaMusic!.albumArt;
      audioUrl = widget.ragaMusic!.audioVersions[0].audioUrl;
      name = widget.ragaMusic!.name;
    }
  }

  checkAndShare() {
    if (widget.chant != null) {
      createActivityLink(chants[currentIndex].id, 'Chant');
    } else if (widget.meditation != null) {
      createActivityLink(meditations[currentIndex].id, 'Meditation');
    } else if (widget.ragaMusic != null) {
      createActivityLink(ragaMusic[currentIndex].id, 'Raga Music');
    }
  }

  myInit() {
    currentIndex = widget.currentIndex;
    setData();
    initAudioPlayer();
    _setCounterController = TextEditingController();
    _setTimerController = TextEditingController();

    if (widget.chant != null || widget.ragaMusic != null) {
      selectedValueforCounter = '108 times';
      requiredCount = 108;
      counterColor = true;
    }
  }

  @override
  void didUpdateWidget(covariant AudioPlayerWidget oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);

    if (widget.chant != null && oldWidget.chant!.id != widget.chant!.id) {
      myInit();
    } else if (widget.meditation != null &&
        oldWidget.meditation!.id != widget.meditation!.id) {
      myInit();
    } else if (widget.ragaMusic != null &&
        oldWidget.ragaMusic!.id != widget.ragaMusic!.id) {
      myInit();
    }
  }

  @override
  void initState() {
    super.initState();
    // user = Provider.of<AuthProvider>(context, listen: false).user;
    myInit();
  }

  @override
  void dispose() {
    super.dispose();
    releasePlayer();
    player.onPlayerStateChanged.drain();
    player.onDurationChanged.drain();
    player.onPositionChanged.drain();
    player.onPlayerComplete.drain();
    _setCounterController.dispose();
    _setTimerController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    int currentMinutes = currentPos.inMinutes;
    int currentSeconds = currentPos.inSeconds % 60;

    int durationMinutes = duration.inMinutes;
    int durationSeconds = duration.inSeconds % 60;

    chants = Provider.of<DailyActivityProvider>(context).chants;
    // meditations = Provider.of<DailyActivityProvider>(context).meditation;
    ragaMusic = Provider.of<DailyActivityProvider>(context).ragaMusic;

    return widget.minimised
        ? GestureDetector(
            onTap: () {
              if (widget.toggleAudioPlayer != null) {
                widget.toggleAudioPlayer!(false);
              }
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              color: const Color(0XFFD6EAFF),
              height: dH * 0.14,
              width: dW,
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(3),
                    child: CachedImageWidget(
                      albumArt,
                      boxFit: BoxFit.cover,
                      width: dW * 0.25,
                      height: dW * 0.28,
                    ),
                  ),
                  SizedBox(width: dW * 0.04),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: dW * 0.5,
                        child: Text(
                          name,
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF1D1E22),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(height: dW * 0.02),
                      Row(
                        children: [
                          Text(
                            '$currentMinutes.${currentSeconds.toString().padLeft(2, '0')}/',
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF515259),
                            ),
                          ),
                          Text(
                            '$durationMinutes.${durationSeconds.toString().padLeft(2, '0')}',
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF515259),
                            ),
                          ),
                          SizedBox(width: dW * 0.01),
                          Text(
                            language['minC'],
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0xFF515259),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          GestureDetector(
                              onTap: () => isPlaying
                                  ? pause()
                                  : isPaused
                                      ? resume()
                                      : play(),
                              child: AssetSvgIcon(
                                isPlaying ? 'pause' : 'play',
                                color: const Color(0XFF1D1E22),
                                height: 20,
                              )),
                          SizedBox(width: dW * 0.04),
                          GestureDetector(
                            onTap: () {
                              if (widget.toggleAudioPlayer != null) {
                                widget.toggleAudioPlayer!(false, true);
                              }
                            },
                            child: Padding(
                              padding: EdgeInsets.only(right: dW * 0.04),
                              child: const Icon(Icons.clear,
                                  color: Colors.black, size: 20),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        : WillPopScope(
            onWillPop: () async {
              if (widget.toggleAudioPlayer != null) {
                widget.toggleAudioPlayer!(true);
              }
              return false;
            },
            child: Scaffold(
                body: SizedBox(
              height: dH,
              width: dW,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: dW * 0.15),
                  Padding(
                    padding: EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () {
                            if (widget.toggleAudioPlayer != null) {
                              widget.toggleAudioPlayer!(true);
                            }
                          },
                          child: Container(
                              padding: EdgeInsets.only(right: dW * 0.1),
                              color: transparentColor,
                              child: const Icon(Icons.arrow_back)),
                        ),
                        GestureDetector(
                          onTap: checkAndShare,
                          child: Container(
                              color: transparentColor,
                              child: const AssetSvgIcon('share')),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: dW * 0.07),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(3),
                    child: CachedImageWidget(
                      albumArt,
                      boxFit: BoxFit.cover,
                      width: dW * 0.9,
                      height: dW * 0.9,
                    ),
                  ),
                  SizedBox(height: dW * 0.17),
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
                    child: Text(
                      name,
                      style: textTheme.headline1!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0XFF1D1E22),
                      ),
                    ),
                  ),
                  Column(
                    children: [
                      SizedBox(height: dW * 0.05),
                      Slider(
                        activeColor: const Color(0xff975EFF),
                        inactiveColor: const Color(0xffF4F4F4),
                        value: currentPos.inMilliseconds
                            .toDouble()
                            .clamp(0, duration.inMilliseconds.toDouble()),
                        onChanged: seekToDuration,
                        max: duration.inMilliseconds.toDouble(),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '$currentMinutes:${currentSeconds.toString().padLeft(2, '0')}',
                              style: textTheme.headline3!.copyWith(
                                fontSize: tS * 14,
                                color: const Color(0XFF515259),
                              ),
                            ),
                            Text(
                              '$durationMinutes:${durationSeconds.toString().padLeft(2, '0')}',
                              style: textTheme.headline3!.copyWith(
                                fontSize: tS * 14,
                                color: const Color(0XFF515259),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.06),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          widget.chant != null
                              // || widget.ragaMusic != null
                              ? Column(
                                  children: [
                                    if (selectedValueforCounter.isNotEmpty)
                                      SizedBox(height: dW * 0.05),
                                    GestureDetector(
                                      onTap: () {
                                        setCounterSheet();
                                      },
                                      child: AssetSvgIcon(
                                        'counter',
                                        //  height: dW * 0.06,
                                        width: dW * 0.065,
                                        color: counterColor
                                            ? const Color(0XFFFF328B)
                                            : null,
                                      ),
                                    ),
                                    if (selectedValueforCounter.isNotEmpty)
                                      Padding(
                                        padding:
                                            EdgeInsets.only(top: dW * 0.01),
                                        child: Text(
                                          selectedValueforCounter.replaceAll(
                                              RegExp(r'[^0-9]'), ''),
                                          style: textTheme.headline1!.copyWith(
                                            fontSize: tS * 12,
                                            color: const Color(0XFFFF328B),
                                          ),
                                        ),
                                      ),
                                  ],
                                )
                              : SizedBox(
                                  width: dW * 0.065,
                                ),
                          GestureDetector(
                            onTap: () async {
                              // if (isLoading == false) {
                              //   if (currentIndex > 0) {
                              //     currentIndex--;
                              //     setState(() {
                              //       isLoading = true;
                              //     });
                              //     await play();
                              //     setState(() {
                              //       isLoading = false;
                              //     });
                              //   }
                              // }
                              if (isLoading == false) {
                                if (widget.chant != null) {
                                  final filteredChants =
                                      Provider.of<DailyActivityProvider>(
                                              context,
                                              listen: false)
                                          .getChantUsingMonth(
                                              widget.selectedPeriod!);

                                  for (int c = currentIndex - 1;
                                      c < filteredChants.length;
                                      c--) {
                                    if (filteredChants[c].audio != '') {
                                      currentIndex = c;
                                      setState(() {
                                        isLoading = true;
                                      });
                                      await play();
                                      setState(() {
                                        isLoading = false;
                                      });
                                      break;
                                    }
                                  }
                                }

                                if (widget.ragaMusic != null) {
                                  final filteredRagaMusic =
                                      Provider.of<DailyActivityProvider>(
                                              context,
                                              listen: false)
                                          .getRagaMusicUsingTrimester(
                                              widget.selectedPeriod!);
                                  for (int r = currentIndex - 1;
                                      r < filteredRagaMusic.length;
                                      r--) {
                                    if (filteredRagaMusic[r].audio != '') {
                                      currentIndex = r;
                                      setState(() {
                                        isLoading = true;
                                      });
                                      await play();
                                      setState(() {
                                        isLoading = false;
                                      });
                                      break;
                                    }
                                  }
                                }
                              }
                            },
                            child: isLoading
                                ? AssetSvgIcon(
                                    'previous',
                                    color: blackColor.withOpacity(0.3),
                                  )
                                : const AssetSvgIcon('previous'),
                          ),
                          Container(
                            height: dW * 0.15,
                            width: dW * 0.15,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(colors: gradientColors),
                              borderRadius: BorderRadius.circular(108),
                            ),
                            child: GestureDetector(
                                onTap: () => isPlaying
                                    ? pause()
                                    : isPaused
                                        ? resume()
                                        : play(),
                                child: isLoading
                                    ? circularForButton(dW * 0.05,
                                        color: white, sW: 2.3)
                                    : AssetSvgIcon(
                                        isPlaying ? 'pause' : 'play',
                                        color: const Color(0XFFFFFFFF),
                                      )),
                          ),
                          GestureDetector(
                              onTap: () async {
                                if (isLoading == false) {
                                  if (widget.chant != null
                                      //&&
                                      // currentIndex < chants.length - 1
                                      // ||
                                      //chants[currentIndex + 1].audio != ''

                                      ) {
                                    final filteredChants =
                                        Provider.of<DailyActivityProvider>(
                                                context,
                                                listen: false)
                                            .getChantUsingMonth(
                                                widget.selectedPeriod!);
                                    if (currentIndex <
                                        filteredChants.length - 1) {
                                      for (int c = currentIndex + 1;
                                          c < filteredChants.length;
                                          c++) {
                                        if (filteredChants[c].audio != '') {
                                          currentIndex = c;
                                          setState(() {
                                            isLoading = true;
                                          });
                                          await play();
                                          setState(() {
                                            isLoading = false;
                                          });
                                          break;
                                        }
                                      }
                                    }

                                    // currentIndex++;
                                    // setState(() {
                                    //   isLoading = true;
                                    // });
                                    // await play();
                                    // setState(() {
                                    //   isLoading = false;
                                    // });
                                  }
                                  if (widget.meditation != null &&
                                      currentIndex < meditations.length - 1) {
                                    currentIndex++;
                                    setState(() {
                                      isLoading = true;
                                    });
                                    await play();
                                    setState(() {
                                      isLoading = false;
                                    });
                                  }
                                  if (widget.ragaMusic != null
                                      // &&
                                      //     currentIndex < ragaMusic.length - 1
                                      ) {
                                    final filteredRagaMusic =
                                        Provider.of<DailyActivityProvider>(
                                                context,
                                                listen: false)
                                            .getRagaMusicUsingTrimester(
                                                widget.selectedPeriod!);
                                    if (currentIndex <
                                        filteredRagaMusic.length - 1) {
                                      for (int r = currentIndex + 1;
                                          r < filteredRagaMusic.length;
                                          r++) {
                                        if (filteredRagaMusic[r].audio != '') {
                                          currentIndex = r;
                                          setState(() {
                                            isLoading = true;
                                          });
                                          await play();
                                          setState(() {
                                            isLoading = false;
                                          });
                                          break;
                                        }
                                      }
                                    }

                                    // currentIndex++;
                                    // setState(() {
                                    //   isLoading = true;
                                    // });
                                    // await play();
                                    // setState(() {
                                    //   isLoading = false;
                                    // });
                                  }
                                }
                              },
                              child: isLoading
                                  ? AssetSvgIcon(
                                      'next',
                                      color: blackColor.withOpacity(0.3),
                                    )
                                  : const AssetSvgIcon('next')),
                          widget.meditation != null ||
                                  // widget.chant != null ||
                                  widget.ragaMusic != null
                              ? Column(
                                  children: [
                                    if (selectedValueforTimer.isNotEmpty)
                                      SizedBox(height: dW * 0.05),
                                    if (selectedAudioVersion != null)
                                      SizedBox(height: dW * 0.05),
                                    if (selectedAudioVersion == null)
                                      GestureDetector(
                                        onTap: () {
                                          setTimerSheet();
                                        },
                                        child: AssetSvgIcon(
                                          'clock',
                                          // height: dW * 0.06,
                                          width: dW * 0.065,
                                          color: timerColor
                                              ? const Color(0XFFFF328B)
                                              : const Color(0XFF292D32),
                                        ),
                                      ),
                                    if (selectedAudioVersion != null)
                                      GestureDetector(
                                        onTap: () {
                                          setTimerSheet();
                                        },
                                        child: const AssetSvgIcon('clock',
                                            color: Color(0XFFFF328B)),
                                      ),
                                    if (selectedValueforTimer.isNotEmpty)
                                      Padding(
                                        padding:
                                            EdgeInsets.only(top: dW * 0.01),
                                        child: Text(
                                          selectedValueforTimer.replaceAll(
                                              RegExp(r'[^0-9]'), ''),
                                          style: textTheme.headline1!.copyWith(
                                            fontSize: tS * 12,
                                            color: const Color(0XFFFF328B),
                                          ),
                                        ),
                                      ),
                                    if (selectedAudioVersion != null)
                                      Padding(
                                        padding:
                                            EdgeInsets.only(top: dW * 0.01),
                                        child: Text(
                                          selectedAudioVersion!.duration,
                                          style: textTheme.headline1!.copyWith(
                                            fontSize: tS * 12,
                                            color: const Color(0XFFFF328B),
                                          ),
                                        ),
                                      ),
                                  ],
                                )
                              : SizedBox(width: dW * 0.065)
                        ],
                      ),
                      // Row(
                      //   children: [
                      //     Container(
                      //       width: dW * 0.2,
                      //       height: dW * 0.2,
                      //       child: widget.chant != null
                      //           // || widget.ragaMusic != null
                      //           ? Column(
                      //               children: [
                      //                 if (selectedValueforCounter.isNotEmpty)
                      //                   SizedBox(height: dW * 0.05),
                      //                 GestureDetector(
                      //                   onTap: () {
                      //                     setCounterSheet();
                      //                   },
                      //                   child: AssetSvgIcon(
                      //                     'counter',
                      //                     color: counterColor
                      //                         ? const Color(0XFFFF328B)
                      //                         : null,
                      //                   ),
                      //                 ),
                      //                 if (selectedValueforCounter.isNotEmpty)
                      //                   Padding(
                      //                     padding:
                      //                         EdgeInsets.only(top: dW * 0.01),
                      //                     child: Text(
                      //                       selectedValueforCounter.replaceAll(
                      //                           RegExp(r'[^0-9]'), ''),
                      //                       style:
                      //                           textTheme.headline1!.copyWith(
                      //                         fontSize: tS * 12,
                      //                         color: const Color(0XFFFF328B),
                      //                       ),
                      //                     ),
                      //                   ),
                      //               ],
                      //             )
                      //           : const SizedBox.shrink(),
                      //     ),
                      //     Container(
                      //       padding: EdgeInsets.all(22),
                      //       width: dW * 0.2,
                      //       height: dW * 0.2,
                      //       child: GestureDetector(
                      //           onTap: () async {
                      //             if (isLoading == false) {
                      //               if (currentIndex > 0) {
                      //                 currentIndex--;
                      //                 setState(() {
                      //                   isLoading = true;
                      //                 });
                      //                 await play();
                      //                 setState(() {
                      //                   isLoading = false;
                      //                 });
                      //               }
                      //             }
                      //           },
                      //           child: isLoading
                      //               ? AssetSvgIcon(
                      //                   'previous',
                      //                   color: blackColor.withOpacity(0.3),
                      //                 )
                      //               : const AssetSvgIcon('previous')),
                      //     ),
                      //     Container(
                      //       height: dW * 0.2,
                      //       width: dW * 0.2,
                      //       alignment: Alignment.center,
                      //       decoration: BoxDecoration(
                      //         gradient: LinearGradient(colors: gradientColors),
                      //         borderRadius: BorderRadius.circular(108),
                      //       ),
                      //       child: GestureDetector(
                      //           onTap: () => isPlaying
                      //               ? pause()
                      //               : isPaused
                      //                   ? resume()
                      //                   : play(),
                      //           child: isLoading
                      //               ? circularForButton(dW * 0.05,
                      //                   color: white, sW: 2.3)
                      //               : AssetSvgIcon(
                      //                   isPlaying ? 'pause' : 'play',
                      //                   color: const Color(0XFFFFFFFF),
                      //                 )),
                      //     ),
                      //     Container(
                      //       padding: EdgeInsets.all(22),
                      //       width: dW * 0.2,
                      //       height: dW * 0.2,
                      //       child: GestureDetector(
                      //           onTap: () async {
                      //             if (isLoading == false) {
                      //               if (widget.chant != null &&
                      //                   currentIndex < chants.length - 1) {
                      //                 currentIndex++;
                      //                 setState(() {
                      //                   isLoading = true;
                      //                 });
                      //                 await play();
                      //                 setState(() {
                      //                   isLoading = false;
                      //                 });
                      //               }
                      //               if (widget.meditation != null &&
                      //                   currentIndex < meditations.length - 1) {
                      //                 currentIndex++;
                      //                 setState(() {
                      //                   isLoading = true;
                      //                 });
                      //                 await play();
                      //                 setState(() {
                      //                   isLoading = false;
                      //                 });
                      //               }
                      //               if (widget.ragaMusic != null &&
                      //                   currentIndex < ragaMusic.length - 1) {
                      //                 currentIndex++;
                      //                 setState(() {
                      //                   isLoading = true;
                      //                 });
                      //                 await play();
                      //                 setState(() {
                      //                   isLoading = false;
                      //                 });
                      //               }
                      //             }
                      //           },
                      //           child: isLoading
                      //               ? AssetSvgIcon(
                      //                   'next',
                      //                   color: blackColor.withOpacity(0.3),
                      //                 )
                      //               : const AssetSvgIcon('next')),
                      //     ),
                      //     Container(
                      //         width: dW * 0.2,
                      //         height: dW * 0.2,
                      //         child: widget.meditation != null ||
                      //                 // widget.chant != null ||
                      //                 widget.ragaMusic != null
                      //             ? Column(
                      //                 children: [
                      //                   if (selectedValueforTimer.isNotEmpty)
                      //                     SizedBox(height: dW * 0.05),
                      //                   if (selectedAudioVersion != null)
                      //                     SizedBox(height: dW * 0.05),
                      //                   if (selectedAudioVersion == null)
                      //                     GestureDetector(
                      //                       onTap: () {
                      //                         setTimerSheet();
                      //                       },
                      //                       child: AssetSvgIcon(
                      //                         'clock',
                      //                         color: timerColor
                      //                             ? const Color(0XFFFF328B)
                      //                             : const Color(0XFF292D32),
                      //                       ),
                      //                     ),
                      //                   if (selectedAudioVersion != null)
                      //                     GestureDetector(
                      //                       onTap: () {
                      //                         setTimerSheet();
                      //                       },
                      //                       child: const AssetSvgIcon('clock',
                      //                           color: Color(0XFFFF328B)),
                      //                     ),
                      //                   if (selectedValueforTimer.isNotEmpty)
                      //                     Padding(
                      //                       padding:
                      //                           EdgeInsets.only(top: dW * 0.01),
                      //                       child: Text(
                      //                         selectedValueforTimer.replaceAll(
                      //                             RegExp(r'[^0-9]'), ''),
                      //                         style:
                      //                             textTheme.headline1!.copyWith(
                      //                           fontSize: tS * 12,
                      //                           color: const Color(0XFFFF328B),
                      //                         ),
                      //                       ),
                      //                     ),
                      //                   if (selectedAudioVersion != null)
                      //                     Padding(
                      //                       padding:
                      //                           EdgeInsets.only(top: dW * 0.01),
                      //                       child: Text(
                      //                         selectedAudioVersion!.duration,
                      //                         style:
                      //                             textTheme.headline1!.copyWith(
                      //                           fontSize: tS * 12,
                      //                           color: const Color(0XFFFF328B),
                      //                         ),
                      //                       ),
                      //                     ),
                      //                 ],
                      //               )
                      //             : const SizedBox.shrink()),
                      //   ],
                      // ),
                    ],
                  ),
                ],
              ),
            )),
          );
  }
}
