// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../common_functions.dart';

class TestimonialWidgetShimmer extends StatelessWidget {
  TestimonialWidgetShimmer({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: dW * 0.6,
          margin: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
          padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.03),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          height: dW * 0.2,
          width: dW * 0.2,
          margin: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.06),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(108),
              color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          margin: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
          padding: EdgeInsets.only(top: dW * 0.045, bottom: dW * 0.045),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: dW * 0.5,
          margin: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
          padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.03),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: dW * 0.4,
          margin: EdgeInsets.only(
              left: dW * 0.04,
              right: dW * 0.04,
              top: dW * 0.04,
              bottom: dW * 0.04),
          padding: EdgeInsets.only(top: dW * 0.015, bottom: dW * 0.015),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
    ]);
  }
}
