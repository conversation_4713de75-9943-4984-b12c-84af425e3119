// ignore_for_file: deprecated_member_use, must_be_immutable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/scrolling_page_indicator.dart';
import '../../common_functions.dart';
import '../models/article_model.dart';

class FactsWidget extends StatefulWidget {
  final List<Facts> facts;
  const FactsWidget({super.key, required this.facts});

  @override
  State<FactsWidget> createState() => _FactsWidgetState();
}

class _FactsWidgetState extends State<FactsWidget> {
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      padding: EdgeInsets.only(bottom: dW * 0.08),
      color: const Color(0XFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: dW * 0.07,
                bottom: dW * 0.05,
                left: dW * 0.04,
                right: dW * 0.04),
            child: Text(
              // language['facts'],
              language['mythsAbtPregnancy'],
              style: Theme.of(context).textTheme.headline1!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0XFF1D1E22),
                  ),
            ),
          ),
          SizedBox(
            height: dW * 0.85,
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.horizontal,
              itemCount: widget.facts.length,
              itemBuilder: (context, index) {
                final e = widget.facts[index];
                return Padding(
                  padding: EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: CachedImageWidget(
                          e.imageUrl,
                          boxFit: BoxFit.cover,
                          width: dW,
                          height: dW * 0.4,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.03),
                        child: Text(
                          e.title,
                          style:
                              Theme.of(context).textTheme.headline1!.copyWith(
                                    fontSize: tS * 12,
                                    color: const Color(0XFF1D1E22),
                                  ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.035),
                        child: Text(
                          e.description,
                          maxLines: 5,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context)
                              .textTheme
                              .headline1!
                              .copyWith(
                                  fontSize: tS * 11,
                                  color: const Color(0XFF6B6C75),
                                  height: 1.7),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          Container(
            alignment: Alignment.center,
            child: ScrollingPageIndicator(
              controller: _pageController,
              itemCount: widget.facts.length,
              dotColor: const Color(0xffDBDBE3),
              dotSelectedColor: blackColor,
              dotSize: 7,
              dotSelectedSize: 7,
            ),
          ),
        ],
      ),
    );
  }
}
