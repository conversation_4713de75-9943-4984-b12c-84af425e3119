// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/commonWidgets/empty_list_widget.dart';
import 'package:provider/provider.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/baby_tracker_date_widget.dart';
import '../../common_functions.dart';
import '../models/baby_tracker_model.dart';
import '../providers/home_provider.dart';

class BabyTrackerScreen extends StatefulWidget {
  const BabyTrackerScreen({Key? key}) : super(key: key);

  @override
  BabyTrackerScreenState createState() => BabyTrackerScreenState();
}

class BabyTrackerScreenState extends State<BabyTrackerScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  bool fetchingBabyTracker = false;
  BabyTracker? babyData;

  bool isGeneratingDates = true;
  late PositionalDate selectedDate;
  late num selectedWeek;
  late DateTime firstDate;
  late DateTime lastDate;

  late List<PositionalDate> listOfDates;
  late List<num> listOfWeeks;

  List<BabyTracker> babyTrackers = [];

  final ItemScrollController dateScrollController = ItemScrollController();
  final ItemPositionsListener datePositionsListener =
      ItemPositionsListener.create();

  final ItemScrollController weekScrollController = ItemScrollController();
  final ItemPositionsListener weekPositionsListener =
      ItemPositionsListener.create();

  scrollDates() {
    Future.delayed(const Duration(seconds: 0)).then((value) {
      dateScrollController.scrollTo(
        index: selectedDate.position < 3 ? 0 : selectedDate.position - 3,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOutCubic,
      );
    });
  }

  scrollWeeks() {
    Future.delayed(const Duration(seconds: 0)).then((value) {
      weekScrollController.scrollTo(
        index: ((selectedWeek + 1) < 3 ? 0 : (selectedWeek + 1) - 3).toInt(),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOutCubic,
      );
    });
  }

  selectDate(PositionalDate posDate, [refreshWeek = true]) {
    setState(() => selectedDate = posDate);

    int days = selectedDate.date.difference(user.conceptionDate!).inDays;
    if (refreshWeek) {
      num newWeek = days / 7;
      if (newWeek < 40 && newWeek != 0 && days % 7 != 0) {
        newWeek -= 1;
      }
      selectWeek((newWeek).ceil(), false);
    }

    scrollDates();
    // fetchData();
  }

  fetchBabyTrackerWeek() async {
    if (Provider.of<HomeProvider>(context, listen: false)
            .getBabyDataByWeek(selectedWeek) ==
        null) {
      setState(() => fetchingBabyTracker = true);
      final response = await Provider.of<HomeProvider>(context, listen: false)
          .fetchBabyTrackerWeek(
        accessToken: user.accessToken,
        weeks: [selectedWeek],
      );
      if (!response['success']) {
        showSnackbar(language[response['message']]);
      }

      setState(() => fetchingBabyTracker = false);
    }
  }

  selectWeek(num index, [refreshDate = true]) async {
    final newDate = user.conceptionDate!.add(Duration(days: 7 * index.toInt()));

    selectedWeek = index + 1;

    if (refreshDate) {
      int i = listOfDates.indexWhere((element) =>
          element.date.toIso8601String() == newDate.toIso8601String());

      if (i != -1) {
        final pod = listOfDates[i];
        selectDate(pod, false);
      }
    }

    setState(() {});

    final babyDataCheck = Provider.of<HomeProvider>(context, listen: false)
        .getBabyDataByWeek(selectedWeek);

    if (babyDataCheck != null) {
      setState(() => babyData = babyDataCheck);
    } else {
      await fetchBabyTrackerWeek();
    }

    scrollWeeks();
  }

  setData() async {
    PositionalDate? tempSelectedDate;
    selectedWeek = user.pregnancyWeek;
    babyData = Provider.of<HomeProvider>(context, listen: false)
        .getBabyDataByWeek(selectedWeek)!;

    firstDate = user.conceptionDate!;
    lastDate = user.conceptionDate!.add(const Duration(days: 40 * 7));

    listOfDates = List.generate(40 * 7, (day) {
      final genDate = PositionalDate(
        position: day,
        date: firstDate.add(Duration(days: day)),
      );
      if (isSameDay(
          DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day),
          genDate.date)) {
        tempSelectedDate =
            PositionalDate(date: genDate.date, position: genDate.position);
      }
      return genDate;
    });

    listOfWeeks = List.generate(40, (week) => week);
    if (tempSelectedDate == null) {
      if (DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .isAfter(listOfDates.last.date)) {
        selectedDate = listOfDates.last;
      } else if (DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .isBefore(listOfDates.first.date)) {
        selectedDate = listOfDates.first;
      }
    } else {
      selectedDate = tempSelectedDate!;
    }

    setState(() => isGeneratingDates = false);
    scrollDates();
    scrollWeeks();
  }

  weightLendthTextWidget(String svg, String text) => Row(
        children: [
          SizedBox(width: 15, child: AssetSvgIcon(svg)),
          Padding(
            padding: const EdgeInsets.only(left: 5),
            child: Text(
              text,
              style: textTheme.headline2!.copyWith(
                fontSize: tS * 12,
                color: lightBlack,
              ),
            ),
          ),
        ],
      );

  pickDate() async {
    hideKeyBoard();

    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate.date,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) => Theme(
          data: ThemeData(
            accentColor: themeColor,
            colorScheme: ColorScheme.light(primary: themeColor),
          ),
          child: child!),
    );

    if (pickedDate != null) {
      int i = listOfDates
          .indexWhere((element) => isSameDay(element.date, pickedDate));
      if (i != -1) {
        selectDate(listOfDates[i]);
      }
    }
  }

  fetchAllBabyTrackerData() async {
    List<num> allWeeks = List.generate(40, (num index) => index + 1).toList();
    allWeeks.remove(selectedWeek);

    Provider.of<HomeProvider>(context, listen: false).fetchBabyTrackerWeek(
      accessToken: user.accessToken,
      weeks: allWeeks,
    );
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    setData();
    fetchAllBabyTrackerData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    babyTrackers = Provider.of<HomeProvider>(context).babyTrackers;
    int i = babyTrackers.indexWhere((ele) => ele.week == selectedWeek);

    if (i != -1) {
      babyData = babyTrackers[i];
    } else {
      babyData = null;
    }

    return Scaffold(
      // backgroundColor: trackerGradientColors[0],

      appBar: CustomAppBar(
        title: language['babyTracker'],
        dW: dW,
        bgColor: const Color(0xFFFCEDF4),
        actions: [
          GestureDetector(
            onTap: pickDate,
            child: Container(
              color: transparentColor,
              child: Row(
                children: [
                  const AssetSvgIcon('bt_calender'),
                  const SizedBox(width: 8),
                  Text(
                    DateFormat('MMM').format(selectedDate.date),
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 12,
                      color: const Color(0xFF975EFF),
                    ),
                  ),
                  const SizedBox(width: 20),
                ],
              ),
            ),
          ),
        ],
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return Container(
      decoration: const BoxDecoration(
          gradient: LinearGradient(
        colors: [
          Color(0xFFFCEDF4),
          Color(0xFFFCEDF4),
          Color(0xFFE5F0FC),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      )),
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : SingleChildScrollView(
              // padding: screenHorizontalPadding(dW),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: dW * 0.05),
                  SizedBox(
                    height: dW * 0.135,
                    child: !isGeneratingDates
                        ? ScrollablePositionedList.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: listOfDates.length,
                            itemScrollController: dateScrollController,
                            itemPositionsListener: datePositionsListener,
                            physics: const ClampingScrollPhysics(),
                            itemBuilder: (context, index) => GestureDetector(
                              onTap: () => selectDate(listOfDates[index]),
                              child: Container(
                                margin: EdgeInsets.only(
                                    left: index == 0 ? dW * 0.035 : 0),
                                child: BabyTrackerDateWidget(
                                  date: listOfDates[index].date,
                                  isSelected: selectedDate.date
                                          .toIso8601String() ==
                                      listOfDates[index].date.toIso8601String(),
                                  dW: dW,
                                ),
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                  SizedBox(height: dW * 0.04),
                  Container(
                    margin: EdgeInsets.only(top: dW * 0.07, bottom: dW * 0.08),
                    alignment: Alignment.center,
                    width: dW * 0.7,
                    height: dW * 0.7,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(colors: [
                        Color(0xFFFFFFFF),
                        Color(0xFFF4D0D0),
                      ]),
                    ),
                    child: Center(
                      child: babyData != null
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: CachedImageWidget(
                                babyData!.weeklyImage,
                                placeholder: const SizedBox.shrink(),
                                scale: 3.5,
                              ),
                            )
                          : fetchingBabyTracker
                              ? const SizedBox.shrink()
                              : EmptyListWidget(
                                  text: language['noDataAvailble'],
                                  topPadding: 0,
                                  textColor: lightBlack,
                                ),
                    ),
                  ),
                  SizedBox(
                    height: 32,
                    child: !isGeneratingDates
                        ? ScrollablePositionedList.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: listOfWeeks.length,
                            itemScrollController: weekScrollController,
                            itemPositionsListener: weekPositionsListener,
                            physics: const ClampingScrollPhysics(),
                            itemBuilder: (context, index) {
                              bool isSelected = index + 1 == selectedWeek;
                              return GestureDetector(
                                onTap: () => selectWeek(listOfWeeks[index]),
                                child: Container(
                                  margin: EdgeInsets.only(
                                      left: index == 0 ? dW * 0.035 : 0),
                                  child: Container(
                                    margin: EdgeInsets.only(right: dW * 0.03),
                                    decoration: BoxDecoration(
                                      color: white,
                                      borderRadius: BorderRadius.circular(4),
                                      gradient: !isSelected
                                          ? null
                                          : LinearGradient(
                                              colors: [
                                                const Color(0xFFCE1B69),
                                                const Color(0xFFFF328B),
                                                const Color(0xFFFF328B)
                                                    .withOpacity(0.71),
                                              ],
                                              begin: Alignment.topRight,
                                              end: Alignment.bottomLeft,
                                            ),
                                    ),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: dW * 0.05),
                                    child: Center(
                                      child: Text(
                                        '${language['week']} ${(index + 1).toString().padLeft(2, '0')}',
                                        style: textTheme.headline2!.copyWith(
                                          fontSize: tS * 14,
                                          color: !isSelected
                                              ? const Color(0xFF84858E)
                                              : white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                        : const SizedBox.shrink(),
                  ),
                  if (babyData != null &&
                      !babyData!.hideSize &&
                      !fetchingBabyTracker)
                    Container(
                      margin: EdgeInsets.only(
                        top: dW * 0.05,
                        left: dW * 0.03,
                        right: dW * 0.03,
                      ),
                      decoration: BoxDecoration(
                        color: white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: dW * 0.05,
                        horizontal: dW * 0.04,
                      ),
                      child: Row(
                          // crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                color:
                                    const Color(0xFF9933CC).withOpacity(0.16),
                                width: 64,
                                height: 60,
                                child: CachedImageWidget(
                                  babyData!.sizeImage,
                                  boxFit: BoxFit.scaleDown,
                                ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: dW * 0.03),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    language['yourBabySizeLike'],
                                    style: textTheme.headline3!.copyWith(
                                      fontSize: tS * 10,
                                      color: const Color(0xFF646464),
                                    ),
                                  ),
                                  SizedBox(height: dW * 0.025),
                                  SizedBox(
                                    width: dW * 0.35,
                                    child: Text(
                                      babyData!.sizeDescription,
                                      textAlign: TextAlign.left,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: textTheme.headline2!.copyWith(
                                        fontSize: tS * 14,
                                        color: const Color(0xFF37383F),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(),
                            Container(
                              height: 60,
                              padding: EdgeInsets.only(
                                left: dW * 0.04,
                                right: dW * 0.045,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF4F6FF),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceAround,
                                children: [
                                  weightLendthTextWidget(
                                      'weight', babyData!.weight),
                                  weightLendthTextWidget(
                                      'length', babyData!.length),
                                ],
                              ),
                            ),
                          ]),
                    ),
                  if (babyData != null && babyData!.content != '')
                    Container(
                      margin: EdgeInsets.only(
                        top: dW * 0.05,
                        left: dW * 0.03,
                        right: dW * 0.03,
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: dW * 0.04,
                        horizontal: dW * 0.03,
                      ),
                      decoration: BoxDecoration(
                        color: white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Html(data: babyData!.content),
                    ),
                  SizedBox(height: dW * 0.2),
                ],
              ),
            ),
    );
  }
}
