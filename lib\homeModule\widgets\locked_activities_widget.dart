// ignore_for_file: must_be_immutable, deprecated_member_use
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../common_functions.dart';

class LockedActivitiesWidget extends StatelessWidget {
  LockedActivitiesWidget({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Column(
      children: [
        Stack(
          children: [
            Container(
              width: dW,
              margin: EdgeInsets.only(
                  top: dW * 0.05, left: dW * 0.04, right: dW * 0.04),
              padding: EdgeInsets.only(
                  top: dW * 0.05,
                  bottom: dW * 0.05,
                  left: dW * 0.04,
                  right: dW * 0.04),
              decoration: BoxDecoration(
                color: const Color(0XFFFFFFFF),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                    blurRadius: 15,
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(-4, 4),
                    spreadRadius: 0,
                    blurRadius: 15,
                  ),
                ],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Image.asset(
                    'assets/images/locked_activity_blur_graphic.png',
                    height: dW * 0.2,
                  ),
                  SizedBox(width: dW * 0.04),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        language['dailyStructured'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0xFF1D1E22),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: dW * 0.05),
                        child: Text(
                          language['toGet'],
                          style: textTheme.headline3!.copyWith(
                            fontSize: tS * 10,
                            color: const Color(0xFF84858E),
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
            Positioned(
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              child: ClipRRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 2.5,
                    sigmaY: 2.5,
                  ),
                  child: Container(
                    width: dW,
                    margin: EdgeInsets.only(
                        top: dW * 0.075, left: dW * 0.04, right: dW * 0.04),
                    padding: EdgeInsets.only(
                        top: dW * 0.05,
                        bottom: dW * 0.05,
                        left: dW * 0.04,
                        right: dW * 0.04),
                    decoration: BoxDecoration(
                      // color: const Color(0XFFFFFFFF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const AssetSvgIcon('lock'),
                        Padding(
                          padding: EdgeInsets.only(top: dW * 0.025),
                          child: Text(
                            language['toGetStarted'],
                            textAlign: TextAlign.center,
                            style: textTheme.headline1!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0xFF1D1E22),
                              height: 1.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
