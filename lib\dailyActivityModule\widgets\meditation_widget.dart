// // ignore_for_file: deprecated_member_use, unused_element

// import 'package:flutter/material.dart';
// import 'package:nine_and_beyond/colors.dart';
// import 'package:nine_and_beyond/dailyActivityModule/widgets/option_bottom_sheet.dart';
// import 'package:provider/provider.dart';
// import '../../authModule/providers/auth_provider.dart';
// import '../model/daily_activity_model.dart';
// import '../providers/daily_activity_provider.dart';

// class MeditationWidget extends StatefulWidget {
//   final Meditation meditation;
//   final String activityTitle;
//   final int? selectedPeriod;
//   final int? currentIndex;
//   final bool? minimised;
//   final Function? toggleAudioPlayer;
//   final Function? playAudio;
//   const MeditationWidget(
//       {super.key,
//       required this.meditation,
//       required this.activityTitle,
//       this.selectedPeriod,
//       this.currentIndex,
//       this.minimised,
//       this.toggleAudioPlayer,
//       this.playAudio});

//   @override
//   State<MeditationWidget> createState() => _MeditationWidgetState();
// }

// class _MeditationWidgetState extends State<MeditationWidget> {
//   //
//   Map language = {};
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;
//   TextTheme get textTheme => Theme.of(context).textTheme;

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;
//     language = Provider.of<AuthProvider>(context).selectedLanguage;

//     final meditation =
//         Provider.of<DailyActivityProvider>(context, listen: false).meditation;

//     return Padding(
//       padding: EdgeInsets.only(left: dW * 0.04),
//       child: Column(
//         children: [
//           Container(
//             color: transparentColor,
//             child: Row(
//               children: [
//                 // ClipRRect(
//                 //   borderRadius: BorderRadius.circular(3),
//                 //   child: CachedImageWidget(
//                 //     widget.meditation.albumArt,
//                 //     boxFit: BoxFit.cover,
//                 //     width: dW * 0.175,
//                 //     height: dW * 0.175,
//                 //   ),
//                 // ),
//                 SizedBox(width: dW * 0.035),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       widget.meditation.name,
//                       style: textTheme.headline1!.copyWith(
//                         fontSize: tS * 12,
//                         color: const Color(0XFF1D1E22),
//                       ),
//                     ),
//                     SizedBox(height: dW * 0.03),
//                     Text(
//                       widget.meditation.duration,
//                       style: textTheme.headline2!.copyWith(
//                         fontSize: tS * 10,
//                         color: const Color(0XFF84858E),
//                       ),
//                     ),
//                   ],
//                 ),
//                 const Spacer(),
//                 IconButton(
//                   onPressed: () {
//                     showModalBottomSheet(
//                       context: context,
//                       isScrollControlled: true,
//                       shape: const RoundedRectangleBorder(
//                         borderRadius: BorderRadius.only(
//                           topLeft: Radius.circular(15.0),
//                           topRight: Radius.circular(15.0),
//                         ),
//                       ),
//                       builder: (BuildContext context) => OptionBottomSheet(
//                         playAudio: widget.playAudio,
//                         //  meditation: widget.meditation,
//                         currentIndex: widget.currentIndex!,
//                         activityTitle: widget.activityTitle,
//                         selectedPeriod: widget.selectedPeriod,
//                         minimised: widget.minimised,
//                         toggleAudioPlayer: widget.toggleAudioPlayer,
//                       ),
//                     );
//                   },
//                   icon: const Icon(
//                     Icons.more_vert_rounded,
//                     size: 22,
//                     color: Color(0xff1D1E22),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           SizedBox(height: dW * 0.04),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';

class MeditationWidget extends StatefulWidget {
  final Meditation meditation;
  final int? selectedTrimester;
  const MeditationWidget(
      {super.key, required this.meditation, this.selectedTrimester});

  @override
  State<MeditationWidget> createState() => _MeditationWidgetState();
}

class _MeditationWidgetState extends State<MeditationWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final meditation =
        Provider.of<DailyActivityProvider>(context, listen: false).meditation;

    return Padding(
      padding: EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              if (widget.meditation.video.isNotEmpty) {
                push(
                  NamedRoute.meditationDetailsScreen,
                  arguments: MeditationDetailsArguments(
                    trimester: widget.selectedTrimester.toString(),
                    meditation: meditation,
                    selectedMeditation: widget.meditation,
                  ),
                );
              } else {
                buySubscriptionDialog();
              }
            },
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: CachedImageWidget(
                    widget.meditation.thumbnail,
                    boxFit: BoxFit.cover,
                    width: dW,
                    height: dW * 0.5,
                  ),
                ),
                Positioned(
                  top: dW * 0.175,
                  left: 0,
                  right: dW * 0.03,
                  child: Column(
                    children: [
                      const AssetSvgIcon('play_in_circle_yoga'),
                      SizedBox(height: dW * 0.02),
                      Text(
                        language['start'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFFFFFFFF),
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.meditation.video.isEmpty)
                  Positioned(
                    top: dW * 0.03,
                    right: dW * 0.03,
                    child: const AssetSvgIcon(
                      's_lock',
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.04),
          Text(
            widget.meditation.name,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 14,
              color: const Color(0XFF1D1E22),
            ),
          ),
          SizedBox(height: dW * 0.025),
          Text(
            widget.meditation.duration,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 12,
              color: const Color(0XFF6B6C75),
            ),
          ),
          SizedBox(height: dW * 0.07),
        ],
      ),
    );
  }
}
