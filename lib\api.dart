var webApi = {'domain': 'https://prod.api.9andbeyond.com'}; //PROD
//var webApi = {'domain': 'https://devapi.9andbeyond.com'}; // NEW DEV PROD
//var webApi = {'domain': 'http://43.205.30.95:2095'}; //DEV PROD
// var webApi = {'domain': 'http://10.5.0.130:2095'}; // Atharv Hotspot
// var webApi = {'domain': 'http://192.168.1.37:2095'}; // Atharv Home
// var webApi = {'domain': 'http://172.20.10.5:2095'}; // Atharv Hotspot
//var webApi = {'domain': 'http://192.168.196.27:2095'}; // Salman Lucknow
//var webApi = {'domain': 'http://13.201.86.111:2095'}; // DEV

var endPoint = {
  // App Config
  'searchLocationFromGoogle': '/api/appConfig/searchLocationFromGoogle',
  'fetchCommonAppConfig': '/api/appConfig/fetchCommonAppConfig',
  'getAppConfigs': '/api/appConfig/getAppConfigs',

  // Banner
  'fetchBanners': '/api/banner/fetchBanners',

  // Authentication/
  'sendOTPtoUser': '/api/auth/sendOTPtoUser',
  'verifyOTPofUser': '/api/auth/verifyOTPofUser',
  'resendOTPtoUser': '/api/auth/resendOTPtoUser',
  'login': '/api/user/login',
  'register': '/api/user/register',
  'editProfile': '/api/user/editProfile',
  'refreshUser': '/api/user/refreshUser',
  'getAwsSignedUrl': '/api/user/getAwsSignedUrl',
  'deleteAccount': '/api/user/deleteAccount',

  // Diary
  'saveRecording': '/api/recording/saveRecording',
  'fetchRecordings': '/api/recording/fetchRecordings',
  'updateRecording': '/api/recording/updateRecording',

  // Daily Activity
  'fetchDailyActivity': '/api/dailyActivity/fetchDailyActivity',

  // Yoga
  // 'fetchYoga': '/api/yoga/fetchYoga',
  'fetchYoga': '/api/yoga/fetchYogaForSubscriptionUser',

  // Chant
  // 'fetchChant': '/api/chant/fetchChant',
  'fetchChant': '/api/chant/fetchChantForSubscriptionUser',

  // Meditation
  'fetchMeditation': '/api/meditation/fetchMeditation',
  //'fetchVideoMeditation': '/api/meditation/fetchVideoMeditation',
  'fetchVideoMeditation':
      '/api/meditation/fetchVideoMeditationForSubscriptionUser',

  // Bedtime Story
  //'fetchBedtimeStory': '/api/bedtimeStory/fetchBedtimeStory',
  'fetchBedtimeStory': '/api/bedtimeStory/fetchBedtimeStoryForSubscriptionUser',

  // Like Unlike
  'likeUnlike': '/api/like/likeUnlike',

  // Raga Music
  // 'fetchRagaMusic': '/api/ragaMusic/fetchRagaMusic',
  'fetchRagaMusic': '/api/ragaMusic/fetchRagaMusicForSubscriptionUser',

  // Diet
  // 'fetchDiet': '/api/diet/fetchDiet',
  'fetchDiet': '/api/diet/fetchDietForSubscriptionUser',

  // Preferences
  'fetchPreferences': '/api/preference/fetchPreferences',

  // Edit Preferences
  'savePreferences': '/api/preference/savePreferences',

  // Chat GPT
  'createMessage': '/api/chatgpt/createMessage',
  'fetchSession': '/api/chatgpt/fetchSession',
  'fetchAllSessions': '/api/chatgpt/fetchAllSessions',
  'buyGptCredits': '/api/chatgpt/buyGptCredits',

  // Transaction
  'fetchCreditHistory': '/api/transaction/fetchCreditHistory',
  'updateTransactionStatusToFailed':
      '/api/transaction/updateTransactionStatusToFailed',

  // Articles
  'fetchGarbhSanskarArticles': '/api/articles/fetchGarbhSanskarArticles',

  // Recommended Articles
  'fetchRecommendedArticles': '/api/articles/fetchRecommendedArticles',

  // Baby Tracker
  'fetchBabyTrackerWeek': '/api/babytracker/fetchBabyTrackerWeek',

  // Facts
  'fetchFacts': '/api/facts/fetchFacts',

  // Testimonial
  'fetchTestimonials': '/api/testimonial/fetchTestimonials',

  // Faq
  'fetchFaqs': '/api/faq/fetchFaqs',

  // Lesser Known
  'fetchLessorKnown': '/api/lesserKnown/fetchLessorKnown',

  // Upcoming Prefrence
  'fetchUpcomingPreferences': '/api/preference/fetchUpcomingPreferences',

  // Single daily activity
  'fetchSingleActivityById': '/api/dailyActivity/fetchSingleActivityById',

  //Subscription
  'createSubscription': '/api/subscription/createSubscription',

  // Master Class
  'updateMasterClassCountInUser':
      '/api/masterClass/updateMasterClassCountInUser',
  'fetchMasterClass': '/api/masterClass/fetchMasterClass',
};
