// ignore_for_file: deprecated_member_use, must_be_immutable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';

class UpcomingActivityWidget extends StatefulWidget {
  const UpcomingActivityWidget({super.key});

  @override
  State<UpcomingActivityWidget> createState() => UpcomingActivityWidgetState();
}

class UpcomingActivityWidgetState extends State<UpcomingActivityWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  late Map dailyActivityIds;

  Map subscriptionDialog = {};

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    subscriptionDialog['title'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    subscriptionDialog['subTitle'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    dailyActivityIds =
        Provider.of<AuthProvider>(context, listen: false).dailyActivityIds;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    subscriptionDialog = Provider.of<AuthProvider>(context).subscriptionDialog;

    final upcomingActivity =
        Provider.of<DailyActivityProvider>(context).upcomingActivity;

    final nextUpcomingActivity =
        Provider.of<DailyActivityProvider>(context).nextUpcomingActivity;

    num? nextUpcomingTime =
        Provider.of<DailyActivityProvider>(context).nextUpcoming;

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.06),
          child: GestureDetector(
            onTap: () {
              if (user.plan == null) {
                if (subscriptionDialog['active'] == true) {
                  buySubscriptionDialog();
                } else {
                  push(NamedRoute.subscriptionScreen);
                }
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['yoga']) {
                push(
                  NamedRoute.yogaScreen,
                  arguments: YogaArguments(
                    title: language['yoga'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['chants']) {
                push(
                  NamedRoute.chantsScreen,
                  arguments: ChantsArguments(
                    title: language['chants'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['meditation']) {
                push(
                  NamedRoute.meditationScreen,
                  arguments: MeditationArguments(
                    title: language['meditation'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['bedtimeStories']) {
                push(
                  NamedRoute.bedtimeStoryScreen,
                  arguments: BedtimeStoryArguments(
                    title: language['bedtimeStories'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['raga']) {
                push(
                  NamedRoute.ragaMusicScreen,
                  arguments: RagaMusicArguments(
                    title: language['ragaMusic'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['earlyMorning']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['breakfast']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['midMorning']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['lunch']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['eveningSnack']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['dinner']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              } else if (upcomingActivity.dailyActivity.id ==
                  dailyActivityIds['bedTime']) {
                push(
                  NamedRoute.dietScreen,
                  arguments: DietArguments(
                    title: language['diet'],
                  ),
                );
              }
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedImageWidget(
                upcomingActivity!.dailyActivity.banner,
                boxFit: BoxFit.cover,
                height: dW * 0.5,
                width: dW,
              ),
            ),
          ),
        ),
        if (nextUpcomingActivity != null)
          Container(
            margin: EdgeInsets.only(
                left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
            padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.03),
            decoration: BoxDecoration(
                color: const Color(0XFFD6EAFF),
                borderRadius: BorderRadius.circular(4)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const AssetSvgIcon('upcoming_activity_clock'),
                SizedBox(width: dW * 0.01),
                Text(
                  '${language['nextUpcomingActivity']}${nextUpcomingActivity.dailyActivity.title} at ${convertToTime(nextUpcomingTime!)}',
                  style: textTheme.headline3!.copyWith(
                      fontSize: tS * 10, color: const Color(0XFF1D1E22)),
                ),
              ],
            ),
          )
      ],
    );
  }
}
