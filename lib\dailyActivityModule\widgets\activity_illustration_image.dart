// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';

class ActivityIllustrationImage extends StatelessWidget {
  final String image;
  ActivityIllustrationImage(this.image, {super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    return Container(
      width: dW,
      alignment: Alignment.center,
      margin: EdgeInsets.only(
        top: dW * 0.15,
        bottom: dW * 0.1,
      ),
      // padding: EdgeInsets.symmetric(horizontal: dW * 0.15),
      child: Image.asset(
        'assets/images/$image',
        width: dW * 0.85,
      ),
    );
  }
}
