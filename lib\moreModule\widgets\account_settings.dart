// ignore_for_file: deprecated_member_use, must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';

class AccountSettings extends StatelessWidget {
  final String title;
  final Function function;
  final String svg;
  final bool showBorder;
  final String? status;

  AccountSettings({
    Key? key,
    required this.title,
    required this.function,
    required this.svg,
    this.showBorder = true,
    this.status,
  }) : super(key: key);

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return InkWell(
      highlightColor: Theme.of(context).primaryColor.withOpacity(0.2),
      splashColor: Colors.transparent,
      enableFeedback: true,
      onTap: () {
        function();
      },
      child: Container(
        decoration: showBorder
            ? const BoxDecoration(
                color: Colors.transparent,
                border: Border(
                  bottom: BorderSide(
                    color: Color(0XFFDBDBE3),
                    width: 0.5,
                  ),
                ),
              )
            : null,
        child: Padding(
          padding:
              EdgeInsets.symmetric(horizontal: dW * 0.04, vertical: dW * 0.01),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFFF6F6F6),
                ),
                child: GradientWidget(
                  gradient: linearGradient,
                  child: AssetSvgIcon(svg, height: 18),
                ),
              ),
              SizedBox(width: dW * 0.05),
              Container(
                width: dW * 0.67,
                padding: EdgeInsets.only(
                  top: dW * 0.04,
                  bottom: dW * 0.04,
                ),
                child: Row(children: [
                  Text(
                    title,
                    style: textTheme.headline3!.copyWith(
                      fontSize: tS * 12,
                      color: const Color(0XFF1D1E22),
                    ),
                  ),
                  if (status != null)
                    Text(
                      status!,
                      style: textTheme.headline1!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0XFF1D1E22),
                      ),
                    ),
                  const Spacer(),
                  const AssetSvgIcon(
                    'arrow_forward_ios',
                    color: Color(0XFFBFC0C8),
                  ),
                ]),
              )
            ],
          ),
        ),
      ),
    );
  }
}
