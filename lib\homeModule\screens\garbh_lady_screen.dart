// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';

class GarbhLadyScreen extends StatefulWidget {
  const GarbhLadyScreen({Key? key}) : super(key: key);

  @override
  GarbhLadyScreenState createState() => GarbhLadyScreenState();
}

class GarbhLadyScreenState extends State<GarbhLadyScreen>
    with TickerProviderStateMixin {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late AnimationController fadeAnimationController;

  fetchData() async {
    setState(() => isLoading = true);
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    fadeAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchData();
  }

  @override
  void dispose() {
    fadeAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: fadeAnimationController,
        curve: Curves.easeIn,
      ),
    );
    fadeAnimationController.forward();

    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : FadeTransition(
              opacity: fadeAnimation,
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: dW * 0.05),
                        Image.asset('assets/images/garbh_lady.png'),
                        SizedBox(height: dW * 0.12),
                        Text(
                          language['wlcmToYrGrbSnskrJrny'],
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 14,
                            color: lightBlack,
                          ),
                        ),
                        SizedBox(height: dW * 0.035),
                        Text(
                          language['wlcmToJrnySubTxt'],
                          textAlign: TextAlign.center,
                          style: textTheme.headline3!.copyWith(
                            height: 1.8,
                            fontSize: tS * 11,
                            color: const Color(0xFF515259),
                          ),
                        ),
                      ],
                    ),
                  ),
                  BottomAlignedWidget(
                      dW: dW,
                      dH: dH,
                      bkgColor: transparentColor,
                      child: Padding(
                        padding: EdgeInsets.only(bottom: dW * 0.05),
                        child: GradientButton(
                          isLoading: false,
                          buttonText: language['next'],
                          onPressed: () =>
                              push(NamedRoute.welcomeToJourneyScreen),
                        ),
                      )),
                ],
              ),
            ),
    );
  }
}
