// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:better_player/better_player.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/homeModule/widgets/video_scrubber.dart';

class CustomPlayerControl extends StatefulWidget {
  CustomPlayerControl({
    super.key,
    required this.controller,
    this.toggleContentVisibility,
  });

  final BetterPlayerController controller;
  final Function? toggleContentVisibility;

  @override
  State<CustomPlayerControl> createState() => _CustomPlayerControlState();
}

class _CustomPlayerControlState extends State<CustomPlayerControl> {
  Timer? _timer;

  bool visible = false;

  quickSeek(int value) async {
    visible = true;
    widget.controller.setControlsVisibility(true);
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }
    Duration? currentPosition = await widget.controller.videoPlayerController!.position;
    if (currentPosition != null) {
      Duration targetPosition = currentPosition + Duration(seconds: value);
      widget.controller.seekTo(targetPosition);

      if (widget.controller.isPlaying()!) {
        _timer = Timer(const Duration(seconds: 4), () {
          visible = false;
          widget.controller.setControlsVisibility(false);
          _timer!.cancel();
        });
      }
    }
  }

  void pausePlay() {
    visible = true;
    widget.controller.setControlsVisibility(true);

    if (widget.controller.isPlaying()!) {
      widget.controller.pause();
      if (_timer != null && _timer!.isActive) {
        _timer!.cancel();
      }
    } else {
      widget.controller.play();
      Future.delayed(const Duration(milliseconds: 500), () {
        visible = false;
        widget.controller.setControlsVisibility(false);
      });
    }
  }

  void _controlVisibility() {
    if (_timer != null && _timer!.isActive) {
      visible = false;
      widget.controller.setControlsVisibility(false);
      _timer!.cancel();
    } else {
      visible = true;
      widget.controller.setControlsVisibility(true);
      _timer = Timer(const Duration(seconds: 4), () {
        visible = false;
        widget.controller.setControlsVisibility(false);
        _timer!.cancel();
      });
    }
  }

  String _formatDuration(Duration? duration) {
    if (duration != null) {
      String minutes = duration.inMinutes.toString().padLeft(2, '0');
      String seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
      return '$minutes:$seconds';
    } else {
      return '00:00';
    }
  }

  setAllOrientations() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: SystemUiOverlay.values);
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  setLanscape() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  // @override
  // void dispose() {
  //   super.dispose();

  //   setAllOrientations();
  // }

  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(seconds: 1), _controlVisibility);
    // setLanscape();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _controlVisibility,
      child: StreamBuilder(
        initialData: false,
        stream: widget.controller.controlsVisibilityStream,
        builder: (context, snapshot) {
          return Stack(
            children: [
              Visibility(
                visible: snapshot.data!,
                child: Positioned(
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(onTap: () => quickSeek(-10), child: const AssetSvgIcon('quick_backward', width: 25)),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 50),
                          child: FloatingActionButton(
                            onPressed: pausePlay,
                            elevation: 0,
                            backgroundColor: transparentColor,
                            child: Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: white,
                                    width: 2.6,
                                  )),
                              child: widget.controller.isPlaying()!
                                  ? const Icon(
                                      Icons.pause_sharp,
                                      color: Colors.white,
                                      size: 35,
                                    )
                                  : const Icon(
                                      Icons.play_arrow_sharp,
                                      color: Colors.white,
                                      size: 40,
                                    ),
                            ),
                          ),
                        ),
                        GestureDetector(onTap: () => quickSeek(10), child: const AssetSvgIcon('quick_forward', width: 25)),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                left: 10,
                right: 10,
                bottom: 8,
                child: ValueListenableBuilder(
                  valueListenable: widget.controller.videoPlayerController!,
                  builder: (context, value, child) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Row(
                            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.only(left: 6),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(50),
                                  shape: BoxShape.rectangle,
                                ),
                                child: Text(_formatDuration(value.position),
                                    style: Theme.of(context).textTheme.headline3!.copyWith(
                                          fontSize: 12,
                                          color: !snapshot.data! ? transparentColor : const Color(0xFFF4F4F4),
                                        )),
                              ),
                              const Spacer(),
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(50),
                                  shape: BoxShape.rectangle,
                                ),
                                child: Text(_formatDuration(value.duration),
                                    style: Theme.of(context).textTheme.headline3!.copyWith(
                                          fontSize: 12,
                                          color: !snapshot.data! ? transparentColor : const Color(0xFFF4F4F4),
                                        )),
                              ),
                              // if (visible)
                              //   GestureDetector(
                              //       onTap: () async {
                              //         if (widget.toggleContentVisibility !=
                              //             null) {
                              //           widget.toggleContentVisibility!();
                              //         }
                              //         if (widget.controller.isFullScreen) {
                              //           widget.controller.exitFullScreen();
                              //         } else {
                              //           widget.controller.enterFullScreen();
                              //           // widget.controller.enterFullScreen();
                              //         }
                              //       },
                              //       child: Icon(
                              //         widget.controller.isFullScreen
                              //             ? Icons.fullscreen_exit_rounded
                              //             : Icons.fullscreen_rounded,
                              //         size: 25,
                              //         color: white,
                              //       )),
                            ],
                          ),
                        ),
                        if (widget.controller.isVideoInitialized() ?? false)
                          VideoScrubber(
                            controller: widget.controller,
                            playerValue: value,
                            visible: snapshot.data!,
                            timer: _timer,
                          )
                      ],
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
