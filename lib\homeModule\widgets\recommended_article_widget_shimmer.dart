// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../common_functions.dart';

class RecommendedArticleWidgetShimmer extends StatelessWidget {
  RecommendedArticleWidgetShimmer({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: baseColorShimmer,
          highlightColor: highlightColorShimmer,
          child: Container(
            width: dW * 0.5,
            margin: EdgeInsets.only(
              top: dW * 0.12,
              left: dW * 0.04,
              right: dW * 0.04,
            ),
            padding: EdgeInsets.only(
                top: dW * 0.06, left: dW * 0.04, right: dW * 0.04),
            decoration: BoxDecoration(color: containerDecorationColorShimmer),
          ),
        ),
        Row(
          children: [
            ShimmerCommonWidget(dW: dW),
            Expanded(
              child: ShimmerCommonWidget(dW: dW),
            ),
          ],
        ),
      ],
    );
  }
}

class ShimmerCommonWidget extends StatelessWidget {
  const ShimmerCommonWidget({
    super.key,
    required this.dW,
  });

  final double dW;

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: dW * 0.5,
          height: dW * 0.403,
          margin: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: dW * 0.3,
          margin: EdgeInsets.only(
            top: dW * 0.03,
            left: dW * 0.04,
            right: dW * 0.04,
          ),
          padding: EdgeInsets.only(top: dW * 0.02),
          decoration: BoxDecoration(color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: dW * 0.5,
          margin: EdgeInsets.only(
            top: dW * 0.03,
            left: dW * 0.04,
            right: dW * 0.04,
          ),
          padding: EdgeInsets.only(top: dW * 0.03),
          decoration: BoxDecoration(color: containerDecorationColorShimmer),
        ),
      ),
    ]);
  }
}
