// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/chatGPTModule/models/chat_gpt_session_model.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/dotted_seperator.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';

class ChatGPTSessionWidget extends StatelessWidget {
  final ChatGPTSession session;
  final bool isFirst;
  final bool isLast;
  ChatGPTSessionWidget({
    super.key,
    required this.session,
    this.isFirst = false,
    this.isLast = false,
  });

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      width: dW,
      padding: EdgeInsets.only(
        bottom: dW * 0.035,
        left: dW * 0.03,
        right: dW * 0.03,
        top: dW * (isFirst ? 0.03 : 0),
      ),
      decoration: BoxDecoration(
        color: white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          if (isFirst)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              offset: const Offset(-1, 0),
              spreadRadius: 0,
              blurRadius: 5,
            )
          else if (isLast)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              offset: const Offset(0, 1),
              spreadRadius: 0,
              blurRadius: 10,
            )
          else
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              offset: const Offset(0, 0),
              spreadRadius: 0,
              blurRadius: 5,
            )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isFirst)
            Padding(
              padding: EdgeInsets.only(bottom: dW * 0.035),
              child: const DottedSeperator(color: Color(0xFFDBDBE3)),
            ),
          Text(
            session.question,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.headline1!.copyWith(
                  fontSize: tS * 12,
                  color: lightBlack,
                ),
          ),
          SizedBox(height: dW * 0.02),
          Text(
            session.answer,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.headline3!.copyWith(
                  fontSize: tS * 12,
                  color: const Color(0xFF6B6C75),
                ),
          ),
        ],
      ),
    );
  }
}
