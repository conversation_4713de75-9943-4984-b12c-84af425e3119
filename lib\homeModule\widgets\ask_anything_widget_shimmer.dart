// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';

class AskAnythingWidgetShimmer extends StatelessWidget {
  AskAnythingWidgetShimmer({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          width: 70,
          height: 70,
          margin: EdgeInsets.only(
            top: dW * 0.06,
            bottom: dW * 0.06,
            left: dW * 0.09,
            right: dW * 0.04,
          ),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: containerDecorationColorShimmer),
        ),
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: baseColorShimmer,
            highlightColor: highlightColorShimmer,
            child: Container(
              width: dW * 0.5,
              margin: EdgeInsets.only(top: dW * 0.08),
              padding: EdgeInsets.only(
                top: dW * 0.04,
                left: dW * 0.04,
                right: dW * 0.04,
              ),
              decoration: BoxDecoration(color: containerDecorationColorShimmer),
            ),
          ),
          Shimmer.fromColors(
            baseColor: baseColorShimmer,
            highlightColor: highlightColorShimmer,
            child: Container(
              width: dW * 0.3,
              margin: EdgeInsets.only(top: dW * 0.08),
              padding: EdgeInsets.only(
                top: dW * 0.025,
                left: dW * 0.05,
                right: dW * 0.05,
              ),
              decoration: BoxDecoration(color: containerDecorationColorShimmer),
            ),
          ),
        ],
      ),
    ]);
  }
}
