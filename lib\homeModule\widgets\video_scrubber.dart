import 'dart:async';

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';

class VideoScrubber extends StatefulWidget {
  const VideoScrubber({
    required this.playerValue,
    required this.controller,
    required this.visible,
    required this.timer,
    super.key,
  });
  final VideoPlayerValue playerValue;
  final BetterPlayerController controller;
  final bool visible;
  final Timer? timer;

  @override
  VideoScrubberState createState() => VideoScrubberState();
}

class VideoScrubberState extends State<VideoScrubber> {
  double _value = 0.0;

  Timer? seekTimer;
  bool seeking = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant VideoScrubber oldWidget) {
    super.didUpdateWidget(oldWidget);
    int position = oldWidget.playerValue.position.inSeconds;
    int duration = oldWidget.playerValue.duration?.inSeconds ?? 0;
    setState(() {
      _value = position / duration;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        thumbShape: CustomThumbShape(), // Custom thumb shape
        overlayShape: SliderComponentShape.noOverlay,
      ),
      child: Slider(
        value: _value,
        activeColor:
            widget.visible ? const Color(0xFF975EFF) : transparentColor,
        inactiveColor: widget.visible ? Colors.grey : transparentColor,
        min: 0.0,
        max: 1.0,
        onChanged: (newValue) {
          setState(() {
            _value = newValue;
          });
          if (widget.timer != null && widget.timer!.isActive) {
            widget.timer!.cancel();
          }
          widget.controller.setControlsVisibility(true);
          if (seekTimer != null && seekTimer!.isActive) {
            seekTimer!.cancel();
          }

          final newProgress = Duration(
              milliseconds: (_value *
                      widget.controller.videoPlayerController!.value.duration!
                          .inMilliseconds)
                  .toInt());
          widget.controller.seekTo(newProgress).then((value) {
            seekTimer = Timer(const Duration(seconds: 4), () {
              widget.controller.setControlsVisibility(false);
            });
          });
        },
      ),
    );
  }
}

class CustomThumbShape extends SliderComponentShape {
  const CustomThumbShape({this.thumbRadius = 6.0});
  final double thumbRadius;

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final canvas = context.canvas;
    final fillPaint = Paint()
      ..color = sliderTheme.thumbColor!
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, thumbRadius, fillPaint);
  }
}
