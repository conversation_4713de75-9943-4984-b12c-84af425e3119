// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../commonWidgets/scrolling_page_indicator.dart';
import '../models/article_model.dart';

class TestimonialWidget extends StatefulWidget {
  List<Testimonial> testimonials;

  TestimonialWidget({Key? key, required this.testimonials}) : super(key: key);

  @override
  TestimonialWidgetState createState() => TestimonialWidgetState();
}

class TestimonialWidgetState extends State<TestimonialWidget> {
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(context).textTheme;

  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      padding: EdgeInsets.only(bottom: dW * 0.08),
      color: const Color(0XFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: dW * 0.07,
                bottom: dW * 0.05,
                left: dW * 0.04,
                right: dW * 0.04),
            child: Text(
              language['testimonialsBy'],
              style: Theme.of(context).textTheme.headline1!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0XFF1D1E22),
                  ),
            ),
          ),
          SizedBox(
            height: dW * 0.7,
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.horizontal,
              itemCount: widget.testimonials.length,
              itemBuilder: (context, index) {
                final e = widget.testimonials[index];
                return Stack(
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                          bottom: dW * 0.08, left: dW * 0.04, right: dW * 0.04),
                      decoration: BoxDecoration(
                        color: const Color(0XFFD9E9FB).withOpacity(0.5),
                        borderRadius: BorderRadius.circular(7),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(dW * 0.05),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(100),
                              child: CachedImageWidget(
                                e.imageUrl,
                                boxFit: BoxFit.cover,
                                width: 60,
                                height: 60,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: dW * 0.035),
                              child: Text(
                                e.description,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context)
                                    .textTheme
                                    .headline3!
                                    .copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0XFF1D1E22),
                                      height: 1.75,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: dW * 0.035),
                              child: GradientWidget(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xffCE1B69),
                                    Color(0xffFF328B),
                                  ],
                                ),
                                child: Text(
                                  e.name,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: dW * 0.02),
                              child: Text(
                                e.source,
                                style: Theme.of(context)
                                    .textTheme
                                    .headline3!
                                    .copyWith(
                                      fontSize: tS * 8,
                                      color: const Color(0XFF84858E),
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      right: dW * 0.04,
                      top: 0,
                      child: Image.asset(
                        'assets/images/testimonial_graphic.png',
                        height: dW * 0.26,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          Container(
            alignment: Alignment.center,
            child: ScrollingPageIndicator(
              controller: _pageController,
              itemCount: widget.testimonials.length,
              dotColor: const Color(0xffDBDBE3),
              dotSelectedColor: blackColor,
              dotSize: 7,
              dotSelectedSize: 7,
            ),
          ),
        ],
      ),
    );
  }
}
