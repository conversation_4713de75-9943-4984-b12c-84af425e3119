// ignore_for_file: depend_on_referenced_packages, deprecated_member_use, use_build_context_synchronously, unused_local_variable, prefer_is_empty, avoid_print, unused_element

import 'dart:async';
import 'dart:io';

import 'package:otp_autofill/otp_autofill.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../colors.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/gradient_button.dart';
import '../../commonWidgets/policy_text_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class VerifyOtpScreen extends StatefulWidget {
  final VerifyOtpArguments args;
  const VerifyOtpScreen({
    super.key,
    required this.args,
  });

  @override
  VerifyOtpScreenState createState() => VerifyOtpScreenState();
}

class VerifyOtpScreenState extends State<VerifyOtpScreen> {
  final TextEditingController _otpController = TextEditingController();
  late OTPInteractor _otpInteractor;
  late OTPTextEditController controller;

  bool isMobileNoVerified = true;
  bool _isLoading = false;
  FocusNode otpFocusNode = FocusNode();
  bool isReadyToResend = false;
  bool inCorrect = false;

  bool isUsernameAlreadyExist = false;
  int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 30;
  Map language = {};
  // int endTime = DateTime.now().millisecondsSinceEpoch + 1000 * 5;

  late Timer _timer;
  int _start = 30;
  int otpSentCount = 1;

  void startTimer() {
    _start = 30;
    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        if (_start == 0) {
          setState(() {
            timer.cancel();
            isReadyToResend = true;
          });
        } else {
          setState(() {
            _start--;
          });
        }
      },
    );
  }

  Future<void> verifyOTP() async {
    if (_otpController.text.isEmpty) {
      setState(() {
        inCorrect = true;
      });
      showSnackbar(language['emptyOtp'], Colors.red);
      return;
    } else if (_otpController.text.length < 6) {
      setState(() {
        inCorrect = true;
      });
      showSnackbar(language['invalidOtp'], Colors.red);

      return;
    }
    final otp = _otpController.text;

    setState(() {
      inCorrect = false;
      _isLoading = true;
    });

    // push(NamedRoute.registerUserScreen,
    //     arguments: RegistrationArguments(
    //         // mobileNo: mobileNo.text,
    //         ));

    final data = await Provider.of<AuthProvider>(context, listen: false)
        .verifyOTPofUser(widget.args.mobileNo.toString(), otp);
    if (data == 'success') {
      final response = await Provider.of<AuthProvider>(context, listen: false)
          .login(query: '?phone=${widget.args.mobileNo}');

      if (response['success'] && response['login']) {
        final user = Provider.of<AuthProvider>(context, listen: false).user;
        if (user.status == '') {
          push(NamedRoute.personalizePromptScreen);
        } else {
          pushAndRemoveUntil(NamedRoute.bottomNavBarScreen,
              arguments: BottomNavArgumnets());
        }
      } else if (!response['success']) {
        showSnackbar(language['somethingWentWrong']);
      } else if (!response['login']) {
        pushAndRemoveUntil(
          NamedRoute.registerUserScreen,
          arguments: RegistrationArguments(
            mobileNo: widget.args.mobileNo,
          ),
        );
      }

      //
    } else {
      showSnackbar('Incorrect OTP', Colors.red);

      setState(() {
        inCorrect = true;
      });
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  resendOTPToUser() async {
    try {
      setState(() {
        otpSentCount += 1;
        if (otpSentCount > 3) {
          isReadyToResend = false;
        }
      });
      if (isReadyToResend) {
        setState(() {
          _timer.cancel();
          isReadyToResend = false;
          startTimer();
        });
        final data = await Provider.of<AuthProvider>(context, listen: false)
            .resendOTPtoUser(widget.args.mobileNo.toString(), 'text');
        if (data != "success") {
          showSnackbar(language['somethingWentWrong'], Colors.red);
        }
      }
    } catch (e) {
      print(e);
    }
  }

  Future<bool> _willPopCallback() async {
    // SystemChannels.platform.invokeMethod('SystemNavigator.pop');
    Navigator.of(context).pop();
    return true;
  }

  @override
  void dispose() {
    super.dispose();

    _timer.cancel();
  }

  @override
  void initState() {
    super.initState();

    startTimer();
    if (Platform.isAndroid) {
      _otpInteractor = OTPInteractor();
      _otpInteractor
          .getAppSignature()
          .then((value) => print('signature - $value'));

      controller = OTPTextEditController(
        codeLength: 6,
        onCodeReceive: (code) {
          print('Your Application receive code - $code');
          _otpController.text = code;
        },
        otpInteractor: _otpInteractor,
      )..startListenUserConsent(
          (code) {
            final exp = RegExp(r'(\d{6})');
            return exp.stringMatch(code ?? '') ?? '';
          },
          strategies: [],
        );
    }
  }

  void onEnd() {
    setState(() {
      isReadyToResend = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    // _isLoading = false;
    final height = MediaQuery.of(context).size.height;
    final width = MediaQuery.of(context).size.width;
    final tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    bool isValid = _otpController.text.length == 6;

    final double kbHeight = MediaQuery.of(context).viewInsets.bottom;

    return GestureDetector(
      onTap: hideKeyBoard,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Container(
            height: height,
            padding: EdgeInsets.symmetric(
                horizontal: width * 0.05, vertical: width * 0.2),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    bottom: height * 0.06,
                  ),
                  child: Image.asset(
                    'assets/images/verify_otp_graphic.png',
                    height: 124,
                    width: 168,
                  ),
                ),
                Text(
                  language["verification"],
                  style: Theme.of(context).textTheme.headline1!.copyWith(
                        fontSize: tS * 20,
                        color: appBarTitleColor,
                      ),
                ),
                SizedBox(height: width * 0.015),
                Container(
                  alignment: Alignment.topLeft,
                  child: Text(
                    language['enterCode'],
                    style: Theme.of(context).textTheme.headline3!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0xff737373),
                        ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: width * 0.015),
                  alignment: Alignment.topLeft,
                  child: Text(
                    "+91 ${widget.args.mobileNo}",
                    style: Theme.of(context).textTheme.headline3!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0xff737373),
                        ),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(height: width * 0.07),
                Container(
                  // height: width * 0.12,
                  width: width * 0.87,
                  margin: const EdgeInsets.only(left: 5),
                  child: PinCodeTextField(
                    appContext: context,
                    backgroundColor: Colors.transparent,
                    enablePinAutofill: true,
                    pastedTextStyle: TextStyle(
                      color: Colors.black,
                      fontSize: tS * 22,
                      fontWeight: FontWeight.w600,
                    ),
                    length: 6,
                    controller: _otpController,
                    autoDismissKeyboard: true,
                    cursorWidth: 1.5,
                    cursorHeight: width * 0.05,
                    cursorColor: Colors.black,
                    focusNode: otpFocusNode,
                    enableActiveFill: true,
                    animationType: AnimationType.fade,
                    keyboardType: TextInputType.number,
                    textStyle: TextStyle(
                      color: Colors.black,
                      fontSize: tS * 20,
                      letterSpacing: 0,
                      fontWeight: FontWeight.w500,
                    ),
                    pinTheme: PinTheme(
                      fieldHeight: width * 0.12,
                      fieldWidth: width * 0.12,
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(8),
                      borderWidth: 0.2,
                      activeColor: Colors.black,
                      activeFillColor: Colors.transparent,
                      selectedColor: Colors.black,
                      selectedFillColor: Colors.transparent,
                      disabledColor: Colors.black,
                      inactiveColor: Colors.black,
                      inactiveFillColor: Colors.transparent,
                    ),
                    onChanged: (val) {
                      if (val.length > 0) {
                        setState(() {
                          inCorrect = false;
                        });
                      }
                    },
                    errorTextMargin: const EdgeInsets.only(top: 45),
                    validator: (value) {
                      if (value!.length == 0) {
                        return language['emptyOtp'];
                      }
                      return null;
                    },
                  ),
                ),
                Stack(
                  children: [
                    GradientButton(
                      elevation: isValid ? 2 : 0,
                      isLoading: _isLoading,
                      onPressed: isValid ? verifyOTP : () {},
                      buttonText: language['verifyOtp'],
                    ),
                    if (!isValid)
                      Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          top: 0,
                          child: Container(
                            color: Colors.white.withOpacity(.7),
                          )),
                  ],
                ),
                SizedBox(height: width * 0.06),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                        alignment: Alignment.bottomCenter,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              padding:
                                  EdgeInsets.symmetric(vertical: width * 0.024),
                              child: InkWell(
                                onTap: !isReadyToResend
                                    ? null
                                    : otpSentCount > 3
                                        ? () {
                                            showSnackbar(
                                                language['outOfAttempts'],
                                                Colors.red);
                                            return;
                                          }
                                        : () {
                                            resendOTPToUser();
                                          },
                                child: RichText(
                                  textAlign: TextAlign.center,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: language['didntGetOtp'],
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: tS * 12,
                                          color: const Color(0xff434343),
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                      TextSpan(
                                        text: language['resend'],
                                        style: TextStyle(
                                          fontSize: tS * 12,
                                          fontWeight: FontWeight.w500,
                                          color: const Color(0xffCE1B69),
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                      if (_start != 0 && otpSentCount < 3)
                                        TextSpan(
                                          text: _start == 0
                                              ? '0:00'
                                              : _start > 9
                                                  ? " ${language['in']} : 0:$_start"
                                                  : " ${language['in']} : 0:0$_start",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: tS * 12,
                                            color: const Color(0xff434343),
                                            fontFamily: 'Inter',
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                  ],
                ),
                // if (widget.args.address == null && kbHeight < 150) ...[
                //   Container(
                //     width: width,
                //     margin: EdgeInsets.only(top: width * 0.035),
                //     child: Column(
                //       crossAxisAlignment: CrossAxisAlignment.center,
                //       children: [
                //         Text(
                //           language['or'],
                //           style:
                //               Theme.of(context).textTheme.headline3!.copyWith(
                //                     fontSize: tS * 13,
                //                     color: const Color(0xFF9798A3),
                //                   ),
                //         ),
                //         GestureDetector(
                //           onTap: () => push(NamedRoute.qrScannerScreen,
                //               arguments: const QrScannerScreenArguments()),
                //           child: Container(
                //             margin: EdgeInsets.only(top: width * 0.04),
                //             child: Row(
                //               mainAxisAlignment: MainAxisAlignment.center,
                //               children: [
                //                 Text(
                //                   language['noAccount'],
                //                   style: Theme.of(context)
                //                       .textTheme
                //                       .headline2!
                //                       .copyWith(
                //                         // decoration: TextDecoration.underline,
                //                         fontSize: tS * 14,
                //                         height: 1.3,
                //                         color: grayColor,
                //                       ),
                //                 ),
                //                 SizedBox(width: width * 0.015),
                //                 GradientWidget(
                //                   gradient: linearGradient,
                //                   child: Text(
                //                     language['signup'],
                //                     style: Theme.of(context)
                //                         .textTheme
                //                         .headline2!
                //                         .copyWith(
                //                           decoration: TextDecoration.underline,
                //                           fontSize: tS * 15,
                //                           color: themeColor,
                //                         ),
                //                   ),
                //                 ),
                //               ],
                //             ),
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ],
                const Spacer(),
                Padding(
                  padding: EdgeInsets.only(
                      bottom:
                          iOSCondition(height) ? width * 0.0 : width * 0.125),
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: PolicyTextWidget(),
                  ),
                ),
                // if (otpSentCount > 1)
                //   Align(
                //     alignment: Alignment.center,
                //     child: Container(
                //       margin: EdgeInsets.only(top: width * 0.04),
                //       child: Text(
                //         'A new OTP has been sent\n($otpSentCount out of 3 attempts)',
                //         style: TextStyle(
                //           fontSize: tS * 13,
                //           fontWeight: FontWeight.w500,
                //           color: Theme.of(context).primaryColor,
                //         ),
                //         textAlign: TextAlign.center,
                //       ),
                //     ),
                //   ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
