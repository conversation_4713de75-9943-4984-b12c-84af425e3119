// ignore_for_file: unrelated_type_equality_checks

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';

class PregnancyGuideOnDietScreen extends StatefulWidget {
  const PregnancyGuideOnDietScreen({Key? key}) : super(key: key);

  @override
  State<PregnancyGuideOnDietScreen> createState() =>
      _PregnancyGuideOnDietScreenState();
}

class _PregnancyGuideOnDietScreenState
    extends State<PregnancyGuideOnDietScreen> {
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  bool isLoading = false;

  String contentText = '';

  fetchContent() async {
    setState(() => isLoading = true);

    final response = await Provider.of<AuthProvider>(context, listen: false)
        .getAppConfig(['PregnancyGuideDiet']);

    if (response['success']) {
      contentText = response['result'][0]['value'];
    } else {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    fetchContent();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Pregnancy Guide on Diet',
        dW: dW,
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: dW * 0.05),
                Padding(
                  padding: EdgeInsets.only(left: dW * 0.03, right: dW * 0.03),
                  child: Html(data: contentText),
                ),
                SizedBox(height: dW * 0.1),
              ],
            ),
          );
  }
}
