// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/dailyActivityModule/screens/bedtime_story_audio_screen.dart';
import 'package:nine_and_beyond/dailyActivityModule/screens/bedtime_story_html_content_screen.dart';
import 'package:nine_and_beyond/dailyActivityModule/widgets/bedtime_story_widget.dart';
import 'package:provider/provider.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/activity_illustration_image.dart';
import 'liked_story_screen.dart';

class BedtimeStoryScreen extends StatefulWidget {
  final BedtimeStoryArguments args;
  const BedtimeStoryScreen({super.key, required this.args});

  @override
  State<BedtimeStoryScreen> createState() => _BedtimeStoryScreenState();
}

class _BedtimeStoryScreenState extends State<BedtimeStoryScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = true;

  int currentIndex = 0;
  bool playingAudio = false;
  bool playingHtml = false;
  bool showLikedStoryScreen = false;

  late BedtimeStory playingBedtimeStory;

  bool minimised = false;

  List<BedTimeCategory> storyCategories = [];

  late BedTimeCategory selectedStoryCategory;

  List<BedtimeStory> bedtimeStories = [];

  final ItemScrollController categoryScrollController = ItemScrollController();
  final ItemPositionsListener categoryPositionsListener =
      ItemPositionsListener.create();

  scrollCategories() {
    Future.delayed(const Duration(seconds: 0)).then((value) {
      categoryScrollController.scrollTo(
        index: ((selectedStoryCategory.position + 1) < 3
                ? 0
                : (selectedStoryCategory.position + 1) - 3)
            .toInt(),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOutCubic,
      );
    });
  }

  playFromAnotherScreen(BedtimeStory btStory, [bool fromLiked = false]) {
    setState(() {
      playingBedtimeStory = btStory;
      if (btStory.type == 'Listen') {
        playingAudio = true;
        playingHtml = false;
      } else if (btStory.type == 'Read') {
        playingHtml = true;
        playingAudio = false;
        minimised = false;
      }
      if (fromLiked) {
        currentIndex =
            Provider.of<DailyActivityProvider>(context, listen: false)
                .likedBedtimeStories
                .indexWhere((element) => element.id == btStory.id);
      } else {
        currentIndex =
            bedtimeStories.indexWhere((element) => element.id == btStory.id);
      }
    });
  }

  toggleAudioPlayer(bool val, [bool killPlayer = false]) {
    if (killPlayer) {
      setState(() {
        playingAudio = false;
        playingHtml = false;
        minimised = false;
      });
      return;
    }
    setState(() {
      minimised = val;
    });
  }

  fetchBedtimeStory() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .bedtimeStory
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedBedtimeStory(
      accessToken: user.accessToken,
      query: '',
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    } else {
      setState(() {
        selectedStoryCategory =
            Provider.of<DailyActivityProvider>(context, listen: false)
                .bedTimeCategories[0];
      });
    }

    setState(() => isLoading = false);

    if (widget.args.bedtimeStory != null) {
      bedtimeStories =
          Provider.of<DailyActivityProvider>(context, listen: false)
              .bedtimeStory;

      int i = bedtimeStories
          .indexWhere((element) => element.id == widget.args.bedtimeStory!.id);
      if (i != -1) {
        if (widget.args.bedtimeStory!.type == 'Listen') {
          push(NamedRoute.bedtimeStoryAudioScreen,
              arguments: BedtimeStoryAudioArguments(
                currentIndex: i,
                bedtimeStory: bedtimeStories[i],
              ));
        } else if (widget.args.bedtimeStory!.type == 'Read') {
          push(NamedRoute.bedtimeStoryHtmlContentScreen,
              arguments: BedtimeStoryHtmlContentArguments(
                currentIndex: i,
                bedtimeStory: bedtimeStories[i],
              ));
        }
      }
    }
  }

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchBedtimeStory();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    storyCategories =
        Provider.of<DailyActivityProvider>(context).bedTimeCategories;

    if (!isLoading) {
      bedtimeStories = Provider.of<DailyActivityProvider>(context)
          .getBedtimeStoryUsingCategory(selectedStoryCategory.id);
    }

    return WillPopScope(
      onWillPop: () async {
        if (!minimised && (playingHtml || playingAudio)) {
          setState(() {
            minimised = true;
          });
          return false;
        } else if (showLikedStoryScreen) {
          setState(() {
            showLikedStoryScreen = false;
          });
          return false;
        }
        return true;
      },
      child: Stack(
        alignment: AlignmentDirectional.bottomCenter,
        children: [
          SizedBox(
            height: dH,
            width: dW,
            child: Scaffold(
              backgroundColor: const Color(0XFFF7F8FC),
              appBar: CustomAppBar(
                title: widget.args.title,
                dW: dW,
                actions: [
                  Padding(
                    padding: EdgeInsets.only(right: dW * 0.04),
                    child: Theme(
                      data: Theme.of(context).copyWith(
                        highlightColor: Colors.transparent,
                        splashColor: Colors.transparent,
                      ),
                      child: PopupMenuButton(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(maxWidth: dW * 0.275),
                        itemBuilder: (BuildContext bc) => [
                          PopupMenuItem(
                            value: 0,
                            height: dW * 0.07,
                            enabled: false,
                            child: GestureDetector(
                              onTap: () {
                                pop();
                                setState(() {
                                  showLikedStoryScreen = true;
                                });
                              },
                              child: Container(
                                margin: EdgeInsets.only(
                                    bottom: dW * 0.02, top: dW * 0.02),
                                child: Text(
                                  language['likedStory'],
                                  style: textTheme.headline2!.copyWith(
                                    fontSize: tS * 12,
                                    color: const Color(0XFF1D1E22),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                        onSelected: (value) {
                          ////////
                        },
                        child: const Icon(
                          Icons.more_vert_rounded,
                          size: 22,
                          color: Color(0xff1D1E22),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              body: iOSCondition(dH)
                  ? screenBody()
                  : SafeArea(child: screenBody()),
            ),
          ),
          if (showLikedStoryScreen)
            LikedStoryScreen(
                playOrView: playFromAnotherScreen,
                minimised: minimised,
                toggleAudioPlayer: toggleAudioPlayer,
                hideScreen: () {
                  setState(() {
                    showLikedStoryScreen = false;
                  });
                }),
          if (playingAudio)
            BedtimeStoryAudioScreen(
              args: BedtimeStoryAudioArguments(
                bedtimeStory: playingBedtimeStory,
                currentIndex: currentIndex,
                minimised: minimised,
                toggleAudioPlayer: toggleAudioPlayer,
                selectedPeriod: selectedStoryCategory.id,
                playOrView: playFromAnotherScreen,
              ),
            ),
          if (playingHtml)
            BedtimeStoryHtmlContentScreen(
                args: BedtimeStoryHtmlContentArguments(
                    bedtimeStory: playingBedtimeStory,
                    currentIndex: currentIndex,
                    minimised: minimised,
                    toggleAudioPlayer: toggleAudioPlayer,
                    selectedPeriod: selectedStoryCategory.id,
                    playOrView: playFromAnotherScreen)),
        ],
      ),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            children: [
              SizedBox(height: dW * 0.04),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      SizedBox(
                        height: dW * 0.08,
                        child: ScrollablePositionedList.builder(
                          scrollDirection: Axis.horizontal,
                          padding: EdgeInsets.only(right: 15),
                          itemCount: storyCategories.length,
                          itemScrollController: categoryScrollController,
                          itemPositionsListener: categoryPositionsListener,
                          physics: const ClampingScrollPhysics(),
                          itemBuilder: (context, i) => GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedStoryCategory = storyCategories[i];
                              });
                              scrollCategories();
                            },
                            child: Container(
                              margin: EdgeInsets.only(left: dW * 0.03),
                              width: dW * 0.25,
                              decoration: BoxDecoration(
                                color: storyCategories[i].id ==
                                        selectedStoryCategory.id
                                    ? const Color(0XFF975EFF)
                                    : const Color(0XFFF4F4F4),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Center(
                                child: Text(
                                  language[storyCategories[i]
                                          .name
                                          .toLowerCase()] ??
                                      storyCategories[i].name.toLowerCase(),
                                  style: textTheme.headline2!.copyWith(
                                    fontSize: tS * 12,
                                    color: storyCategories[i].id ==
                                            selectedStoryCategory.id
                                        ? const Color(0XFFFFFFFF)
                                        : const Color(0XFF000000),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        // child: ListView(
                        //   scrollDirection: Axis.horizontal,
                        //   children: [
                        //     Row(
                        //       children: [
                        //         ...storyCategories.map(
                        //           (i) {
                        //             final bedtimeStoriesForCategory =
                        //                 Provider.of<DailyActivityProvider>(
                        //                         context)
                        //                     .getBedtimeStoryUsingCategory(i);
                        //             if (bedtimeStoriesForCategory.isNotEmpty) {
                        //               return GestureDetector(
                        //                 onTap: () {
                        //                   setState(() {
                        //                     selectedStoryType = i;
                        //                   });
                        //                 },
                        //                 child: Container(
                        //                   margin:
                        //                       EdgeInsets.only(left: dW * 0.03),
                        //                   width: dW * 0.25,
                        //                   decoration: BoxDecoration(
                        //                     color: selectedStoryType == i
                        //                         ? const Color(0XFF975EFF)
                        //                         : const Color(0XFFF4F4F4),
                        //                     borderRadius:
                        //                         BorderRadius.circular(20),
                        //                   ),
                        //                   child: Center(
                        //                     child: Text(
                        //                       language[i],
                        //                       style:
                        //                           textTheme.headline2!.copyWith(
                        //                         fontSize: tS * 12,
                        //                         color: selectedStoryType == i
                        //                             ? const Color(0XFFFFFFFF)
                        //                             : const Color(0XFF000000),
                        //                         overflow: TextOverflow.ellipsis,
                        //                       ),
                        //                     ),
                        //                   ),
                        //                 ),
                        //               );
                        //             } else {
                        //               return Container();
                        //             }
                        //           },
                        //         ),
                        //       ],
                        //     ),

                        //   ],
                        // ),
                      ),
                      SizedBox(height: dW * 0.08),
                      Padding(
                        padding:
                            EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
                        child: Row(
                          children: [
                            const AssetSvgIcon(
                              'more_yoga',
                              color: Color(0XFFFF328B),
                            ),
                            SizedBox(width: dW * 0.03),
                            Text(
                              language['recommendedStories'],
                              style: textTheme.headline2!.copyWith(
                                fontSize: tS * 12,
                                color: const Color(0XFF1D1E22),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.05),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: bedtimeStories.length,
                        padding: EdgeInsets.only(bottom: dW * 0.1),
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, i) => GestureDetector(
                          onTap: () {
                            setState(() {
                              if (bedtimeStories[i].audio.isNotEmpty ||
                                  bedtimeStories[i].htmlContent.isNotEmpty) {
                                playingBedtimeStory = bedtimeStories[i];
                                if (bedtimeStories[i].type == 'Listen') {
                                  playingAudio = true;
                                  playingHtml = false;
                                } else if (bedtimeStories[i].type == 'Read') {
                                  playingHtml = true;
                                  playingAudio = false;
                                  minimised = false;
                                }
                                currentIndex = i;
                              } else {
                                buySubscriptionDialog();
                              }
                            });
                          },
                          child: BedtimeStoryWidget(
                            playOrView: ([_, __]) {
                              setState(() {
                                playingBedtimeStory = bedtimeStories[i];
                                if (bedtimeStories[i].type == 'Listen') {
                                  playingAudio = true;
                                  playingHtml = false;
                                } else if (bedtimeStories[i].type == 'Read') {
                                  playingHtml = true;
                                  playingAudio = false;
                                  minimised = false;
                                }
                                currentIndex = i;
                              });
                            },
                            bedtimeStory: bedtimeStories[i],
                            activityTitle: widget.args.title,
                            minimised: minimised,
                            toggleAudioPlayer: toggleAudioPlayer,
                          ),
                        ),
                      ),
                      ActivityIllustrationImage('bedtime_illus.png'),
                      SizedBox(height: dW * 0.25),
                    ],
                  ),
                ),
              ),
            ],
          );
  }
}
