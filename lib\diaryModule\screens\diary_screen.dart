// ignore_for_file: deprecated_member_use, unused_local_variable, prefer_final_fields
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/commonWidgets/diary_background.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_widget.dart';
import 'package:nine_and_beyond/diaryModule/providers/diary_provider.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/empty_list_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/diary_model.dart';
import '../widgets/recording_widget.dart';

class DiaryScreen extends StatefulWidget {
  final Function changeIndex;
  const DiaryScreen({super.key, required this.changeIndex});

  @override
  State<DiaryScreen> createState() => _DiaryScreenState();
}

class _DiaryScreenState extends State<DiaryScreen>
    with TickerProviderStateMixin {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  TextTheme get textTheme => Theme.of(context).textTheme;
  bool isLoading = false;

  ScrollController _scrollController = ScrollController();
  bool lazyLoading = false;

  List<Recording> recordings = [];

  int? selectedMonth;
  String getMonthName(int monthNumber) {
    switch (monthNumber) {
      case 1:
        return 'Jan';
      case 2:
        return 'Feb';
      case 3:
        return 'Mar';
      case 4:
        return 'Apr';
      case 5:
        return 'May';
      case 6:
        return 'Jun';
      case 7:
        return 'Jul';
      case 8:
        return 'Aug';
      case 9:
        return 'Sep';
      case 10:
        return 'Oct';
      case 11:
        return 'Nov';
      case 12:
        return 'Dec';
      default:
        return '';
    }
  }

  int? selectedYear;
  final int last50Years = 50;
  List<int>? yearsList;

  getCurrentDateMonth() {
    var now = DateTime.now();
    var formatter = DateFormat('dd MMMM');
    var formattedDate = formatter.format(now);
    return formattedDate.toLowerCase();
  }

  getCurrentDay() {
    var now = DateTime.now();
    var formatter = DateFormat('EEEE');
    return formatter.format(now);
  }

  applyFilterDialog() async {
    selectedMonth = selectedMonth ?? DateTime.now().month;
    selectedYear = selectedYear ?? DateTime.now().year;
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: dW * 0.025),
                Stack(
                  children: [
                    Positioned(
                      top: dW * 0.055,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: dW * 0.12,
                        width: dW,
                        color: const Color(0xffF7F8FC),
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                            width: dW * 0.25,
                            height: dW * 0.3,
                            child: CupertinoPicker(
                              selectionOverlay: const SizedBox.shrink(),
                              itemExtent: 50,
                              scrollController: FixedExtentScrollController(
                                  initialItem: selectedMonth! - 1),
                              onSelectedItemChanged: (int value) {
                                setState(() {
                                  selectedMonth = value + 1;
                                });
                              },
                              children: List<Widget>.generate(
                                12,
                                (index) => Text(
                                  getMonthName(index + 1),
                                  style: textTheme.headline1!.copyWith(
                                    fontSize: tS * 16,
                                    color: const Color(0XFF1D1E22),
                                  ),
                                ),
                              ),
                            )),
                        SizedBox(
                          width: dW * 0.2,
                          height: dW * 0.3,
                          child: CupertinoPicker(
                            selectionOverlay: const SizedBox.shrink(),
                            itemExtent: 50,
                            scrollController: FixedExtentScrollController(
                                initialItem: yearsList!.indexOf(selectedYear!)),
                            onSelectedItemChanged: (int value) {
                              setState(() {
                                selectedYear = yearsList![value];
                              });
                            },
                            children: List<Widget>.generate(
                              yearsList!.length,
                              (index) => Text(
                                yearsList![index].toString(),
                                style: textTheme.headline1!.copyWith(
                                  fontSize: tS * 16,
                                  color: const Color(0XFF1D1E22),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: dW * 0.1),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: dW * 0.05),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    DialogTextButton(
                      onPressed: () {
                        pop();
                        selectedMonth = selectedYear = null;
                        init();
                      },
                      text: language['reset'],
                    ),
                    SizedBox(width: dW * 0.08),
                    FilledDialogButton(
                      onPressed: () {
                        pop();
                        init();
                      },
                      text: language['applyFilter'],
                    ),
                  ],
                )
              ],
            ),
          )),
    );
  }

  generateYearsList() {
    int currentYear = DateTime.now().year;

    List<int> years = [];
    for (int i = currentYear - last50Years; i <= currentYear; i++) {
      years.add(i);
    }
    return years;
  }

  fetchRecordings([bool refresh = false]) async {
    DateTime? startDate, endDate;
    String query = '';
    if (selectedYear != null && selectedMonth != null) {
      startDate = DateTime(selectedYear!, selectedMonth!, 1, 00, 00, 00);
      endDate = DateTime(selectedYear!, selectedMonth! + 1, 1, 23, 59, 59)
          .subtract(const Duration(days: 1));
      query = 'startDate=${startDate.toString()}&endDate=${endDate.toString()}';
    }

    final response = await Provider.of<DiaryProvider>(context, listen: false)
        .fetchRecordings(
      query: query,
      accessToken: user.accessToken,
      refresh: refresh,
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
  }

  lazyLoad() async {
    // setState(() => lazyLoading = true);
    await fetchRecordings();
    // setState(() => lazyLoading = false);
  }

  init() async {
    setState(() => isLoading = true);
    await fetchRecordings(true);
    setState(() => isLoading = false);
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      lazyLoad();
    }

    return false;
  }

  @override
  void initState() {
    super.initState();
    yearsList = generateYearsList();
    user = Provider.of<AuthProvider>(context, listen: false).user;
    init();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    final double dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    recordings = Provider.of<DiaryProvider>(context).recordings;

    return
        // isLoading
        //     ? const CircularLoader()
        //     :
        Stack(
      children: [
        DiaryBackground(),
        Scaffold(
            backgroundColor: Colors.transparent,
            body: Column(
              children: [
                Container(
                  height: dW * 0.25,
                  color: const Color(0xff8EBEF4).withOpacity(0.4),
                  width: dW,
                  padding: EdgeInsets.only(left: 12, top: dW * 0.065),
                  child: SafeArea(
                    child: Text(
                      language['voiceDiary'],
                      style: textTheme.headline1!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0XFF000000),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: NotificationListener<ScrollNotification>(
                    onNotification: _handleScrollNotification,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      physics: const BouncingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 16, top: dW * 0.065, bottom: dW * 0.065),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  getCurrentDateMonth(),
                                  style: textTheme.headline3!.copyWith(
                                    fontSize: tS * 42,
                                    color: const Color(0XFF000000),
                                  ),
                                ),
                                SizedBox(height: dW * 0.036),
                                Text(
                                  getCurrentDay(),
                                  style: textTheme.headline2!.copyWith(
                                    fontSize: tS * 14,
                                    color: const Color(0XFF000000),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            height: dW * 1.2,
                            width: dW,
                            margin: const EdgeInsets.only(left: 8, right: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: const Color(0xffFFFFFF).withOpacity(0.6),
                            ),
                            child: Column(
                              children: [
                                Padding(
                                    padding: EdgeInsets.only(
                                        left: 21, right: 21, top: dW * 0.09),
                                    child: Text(
                                      language['embrace'],
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: tS * 18,
                                        fontFamily: 'Pridi',
                                        fontWeight: FontWeight.w400,
                                        color: themeColor,
                                      ),
                                    )),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 21, right: 21, top: dW * 0.09),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      for (int i = 0; i < 75; i++)
                                        Container(
                                          width: 4,
                                          height: 1,
                                          decoration: BoxDecoration(
                                            border: Border(
                                              bottom: BorderSide(
                                                width: 1,
                                                color: i % 2 == 0
                                                    ? const Color(0XFFDBDBE3)
                                                    : Colors.transparent,
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 21,
                                      right: 21,
                                      top: dW * 0.09,
                                      bottom: dW * 0.125),
                                  child: Text(
                                    language['welcomeVoiceDiary'],
                                    textAlign: TextAlign.center,
                                    style: textTheme.headline3!.copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0XFF363636),
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    push(NamedRoute.startRecordingScreen,
                                        arguments: StartRecordingArguments());
                                  },
                                  child: Container(
                                    height: dW * 0.4,
                                    width: dW * 0.4,
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            width: 6, color: Colors.white),
                                        borderRadius:
                                            BorderRadius.circular(108)),
                                    child: Padding(
                                      padding: const EdgeInsets.all(10),
                                      child: Container(
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(108),
                                            border: Border.all(
                                                width: 1, color: Colors.white),
                                            color: const Color(0XFFE65050)),
                                        child: Text(
                                          language['startRecording'],
                                          textAlign: TextAlign.center,
                                          style: textTheme.headline1!.copyWith(
                                            fontSize: tS * 12,
                                            color: const Color(0XFFFFFFFF),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: dW * 0.04),
                          Container(
                            width: dW,
                            margin: const EdgeInsets.only(left: 8, right: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: const Color(0xffFFFFFF).withOpacity(0.6),
                            ),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 16, right: 16, top: 16),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        language['pastRecordings'],
                                        style: textTheme.headline3!.copyWith(
                                          fontSize: tS * 12,
                                          color: const Color(0XFF000000),
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          applyFilterDialog();
                                        },
                                        child: Container(
                                          height: dW * 0.075,
                                          width: dW * 0.185,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(40),
                                              color: const Color(0xffFFFFFF)
                                                  .withOpacity(0.8)),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              const AssetSvgIcon(
                                                'filter',
                                              ),
                                              const SizedBox(width: 5),
                                              Text(
                                                language['filter'],
                                                style: textTheme.headline3!
                                                    .copyWith(
                                                  fontSize: tS * 11,
                                                  color:
                                                      const Color(0XFF1D1E22),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                isLoading
                                    ? Container(
                                        alignment: Alignment.center,
                                        padding: EdgeInsets.symmetric(
                                            vertical: dW * 0.1),
                                        child: const CircularLoader(),
                                      )
                                    : recordings.isEmpty
                                        ? Padding(
                                            padding: EdgeInsets.only(
                                                bottom: dW * 0.1),
                                            child: EmptyListWidget(
                                                textColor:
                                                    const Color(0XFF363636),
                                                text: language['noRecording'],
                                                topPadding: 0.2),
                                          )
                                        : ListView.builder(
                                            shrinkWrap: true,
                                            itemCount: recordings.length,
                                            physics:
                                                const BouncingScrollPhysics(),
                                            itemBuilder: (context, i) =>
                                                RecordingWidget(
                                                    recording: recordings[i]),
                                          ),
                                if (lazyLoading) lazyLoader(dW),
                                SizedBox(height: dW * 0.07),
                              ],
                            ),
                          ),
                          SizedBox(height: dW * 0.5)
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            )),
      ],
    );
  }
}
