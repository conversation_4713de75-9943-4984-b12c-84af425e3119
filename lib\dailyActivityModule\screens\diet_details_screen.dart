// // ignore_for_file: deprecated_member_use

// import 'package:flutter/material.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:nine_and_beyond/colors.dart';
// import 'package:provider/provider.dart';
// import 'package:youtube_player_flutter/youtube_player_flutter.dart';

// import '../../authModule/providers/auth_provider.dart';
// import '../../common_functions.dart';
// import '../../navigation/arguments.dart';
// import '../../navigation/navigators.dart';

// class DietDetailsScreen extends StatefulWidget {
//   final DietDetailsArguments args;

//   const DietDetailsScreen({super.key, required this.args});

//   @override
//   State<DietDetailsScreen> createState() => _DietDetailsScreenState();
// }

// class _DietDetailsScreenState extends State<DietDetailsScreen> {
//   //
//   Map language = {};
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;
//   TextTheme get textTheme => Theme.of(context).textTheme;

//   late YoutubePlayerController _controller;

//   @override
//   void initState() {
//     super.initState();
//     final videoID = YoutubePlayer.convertUrlToId(widget.args.videoUrl);
//     _controller = YoutubePlayerController(
//       initialVideoId: videoID.toString(),
//       flags: const YoutubePlayerFlags(
//         autoPlay: true,
//         // disableDragSeek: true,
//         // loop: false,
//         // hideThumbnail: true,
//       ),
//     );
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;
//     language = Provider.of<AuthProvider>(context).selectedLanguage;

//     return Scaffold(
//       appBar: AppBar(
//         backgroundColor: white,
//         elevation: 0,
//         automaticallyImplyLeading: false,
//         title: Text(
//           language['youtube'],
//           style: textTheme.headline3!.copyWith(
//             fontSize: tS * 12,
//             color: const Color(0XFF6B6C75),
//           ),
//         ),
//         actions: const [
//           IconButton(
//             onPressed: pop,
//             icon: Icon(
//               Icons.clear,
//               color: Color(0XFF000000),
//             ),
//           ),
//         ],
//       ),
//       body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
//     );
//   }

//   screenBody() {
//     return SingleChildScrollView(
//       physics: const AlwaysScrollableScrollPhysics(),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SafeArea(
//             child: YoutubePlayerBuilder(
//                 player: YoutubePlayer(
//                   controller: _controller,
//                   showVideoProgressIndicator: true,
//                   progressIndicatorColor: const Color(0XFF975EFF),
//                   progressColors: const ProgressBarColors(
//                     playedColor: Color(0XFF975EFF),
//                     handleColor: Color(0XFF975EFF),
//                   ),
//                 ),
//                 builder: (context, player) {
//                   return player;
//                 }),
//           ),
//           Padding(
//             padding: EdgeInsets.only(
//                 left: dW * 0.05,
//                 right: dW * 0.05,
//                 top: dW * 0.08,
//                 bottom: dW * 0.05),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   widget.args.videoTitle,
//                   style: textTheme.headline1!.copyWith(
//                     fontSize: tS * 16,
//                     color: const Color(0XFF1D1E22),
//                   ),
//                 ),
//                 SizedBox(height: dW * 0.07),
//                 Container(
//                   width: dW,
//                   height: 1,
//                   color: const Color(0XFFDBDBE3),
//                 ),
//                 SizedBox(height: dW * 0.07),
//                 Text(
//                   language['ingredients'],
//                   style: textTheme.headline2!.copyWith(
//                     fontSize: tS * 16,
//                     color: const Color(0XFF37383F),
//                   ),
//                 ),
//                 SizedBox(height: dW * 0.035),
//                 Html(data: widget.args.ingredients),
//                 // Column(
//                 //   crossAxisAlignment: CrossAxisAlignment.start,
//                 //   children: widget.args.ingredients.map((ingredients) {
//                 //     return Row(
//                 //       crossAxisAlignment: CrossAxisAlignment.start,
//                 //       children: [
//                 //         SizedBox(width: dW * 0.02),
//                 //         Padding(
//                 //           padding: EdgeInsets.only(top: dW * 0.0175),
//                 //           child: const Icon(
//                 //             Icons.circle,
//                 //             size: 5,
//                 //           ),
//                 //         ),
//                 //         SizedBox(width: dW * 0.02),
//                 //         Expanded(
//                 //           child: Text(
//                 //             ingredients,
//                 //             style: textTheme.headline2!.copyWith(
//                 //               fontSize: tS * 14,
//                 //               color: const Color(0XFF1D1E22),
//                 //             ),
//                 //           ),
//                 //         ),
//                 //       ],
//                 //     );
//                 //   }).toList(),
//                 // ),
//                 // SizedBox(height: dW * 0.07),
//                 // Container(
//                 //   width: dW,
//                 //   height: 1,
//                 //   color: const Color(0XFFDBDBE3),
//                 // ),
//                 SizedBox(height: dW * 0.07),
//                 Text(
//                   language['steps'],
//                   style: textTheme.headline2!.copyWith(
//                     fontSize: tS * 16,
//                     color: const Color(0XFF37383F),
//                   ),
//                 ),
//                 SizedBox(height: dW * 0.035),
//                 Html(data: widget.args.steps),
//                 // Column(
//                 //   children: widget.args.steps.map((steps) {
//                 //     return Row(
//                 //       crossAxisAlignment: CrossAxisAlignment.start,
//                 //       children: [
//                 //         SizedBox(width: dW * 0.02),
//                 //         Padding(
//                 //           padding: EdgeInsets.only(top: dW * 0.0175),
//                 //           child: const Icon(
//                 //             Icons.circle,
//                 //             size: 5,
//                 //           ),
//                 //         ),
//                 //         SizedBox(width: dW * 0.02),
//                 //         Expanded(
//                 //           child: Text(
//                 //             steps,
//                 //             style: textTheme.headline3!.copyWith(
//                 //               fontSize: tS * 14,
//                 //               color: const Color(0XFF515259),
//                 //             ),
//                 //           ),
//                 //         ),
//                 //       ],
//                 //     );
//                 //   }).toList(),
//                 // ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, must_be_immutable, unrelated_type_equality_checks

// import 'package:flutter/material.dart';
// import 'package:nine_and_beyond/colors.dart';
// import 'package:nine_and_beyond/commonWidgets/gradient_widget.dart';
// import 'package:nine_and_beyond/common_functions.dart';
// import 'package:provider/provider.dart';
// import '../../authModule/providers/auth_provider.dart';
// import '../../homeModule/widgets/custom_video_player.dart';
// import '../../navigation/arguments.dart';

// class DietDetailsScreen extends StatefulWidget {
//   final DietDetailsArguments args;

//   const DietDetailsScreen({super.key, required this.args});

//   @override
//   State<DietDetailsScreen> createState() => _DietDetailsScreenState();
// }

// class _DietDetailsScreenState extends State<DietDetailsScreen> {
//   double dW = 0.0;
//   double tS = 0.0;
//   Map language = {};

//   TextTheme get textTheme => Theme.of(bContext).textTheme;

//   trimesterText(int selectedTrimester) {
//     switch (selectedTrimester) {
//       case 1:
//         return language['1st'];
//       case 2:
//         return language['2nd'];
//       case 3:
//         return language['3rd'];
//       default:
//         return '';
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;
//     language = Provider.of<AuthProvider>(context).selectedLanguage;

//     return Scaffold(
//       body: SingleChildScrollView(
//         physics: const AlwaysScrollableScrollPhysics(),
//         child: Column(
//           children: [
//             SafeArea(
//               child: Stack(
//                 children: [
//                   CustomVideoPlayer(link: widget.args.selectedDiet.video),
//                   Positioned(
//                       left: 2,
//                       top: 1,
//                       child: Row(
//                         children: [
//                           const BackButton(),
//                           Text(
//                             language['back'],
//                             style: Theme.of(context)
//                                 .textTheme
//                                 .headline2!
//                                 .copyWith(fontSize: tS * 12, color: blackColor),
//                           ),
//                         ],
//                       )),
//                 ],
//               ),
//             ),
//             Padding(
//               padding: EdgeInsets.only(
//                   left: dW * 0.05,
//                   right: dW * 0.05,
//                   top: dW * 0.08,
//                   bottom: dW * 0.05),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   GradientWidget(
//                     gradient: LinearGradient(colors: gradientColors),
//                     child: Text(
//                       '${trimesterText(int.parse(widget.args.trimester))} ' +
//                           language['trimester'],
//                       style: textTheme.headline2!.copyWith(
//                         fontSize: tS * 12,
//                       ),
//                     ),
//                   ),
//                   SizedBox(height: dW * 0.02),
//                   Row(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Text(
//                         widget.args.selectedDiet.name,
//                         style: textTheme.headline1!.copyWith(
//                           fontSize: tS * 14,
//                           color: const Color(0XFF1D1E22),
//                         ),
//                       ),
//                     ],
//                   ),
//                   Padding(
//                     padding: EdgeInsets.only(top: dW * 0.1),
//                     child: Row(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           widget.args.selectedDiet.description,
//                           style: textTheme.headline1!.copyWith(
//                             fontSize: tS * 14,
//                             color: const Color(0XFF363636),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';

class DietDetailsScreen extends StatefulWidget {
  final DietDetailsArguments args;

  const DietDetailsScreen({super.key, required this.args});

  @override
  State<DietDetailsScreen> createState() => _DietDetailsScreenState();
}

class _DietDetailsScreenState extends State<DietDetailsScreen> {
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  TextTheme get textTheme => Theme.of(context).textTheme;

  bool isFullScreen = false;

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  late YoutubePlayerController _controller;

  @override
  void initState() {
    super.initState();
    final videoID =
        YoutubePlayer.convertUrlToId(widget.args.selectedDiet.video);
    _controller = YoutubePlayerController(
      initialVideoId: videoID.toString(),
      flags: const YoutubePlayerFlags(
        autoPlay: true,
      ),
    );

    _controller.addListener(() {
      if (_controller.value.isFullScreen != isFullScreen) {
        setState(() {
          isFullScreen = _controller.value.isFullScreen;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      body: Stack(
        children: [
          screenBody(),
          if (isFullScreen)
            Positioned(
              top: dW * 0.01,
              left: dW * 0.02,
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  _controller.toggleFullScreenMode();
                },
              ),
            ),
        ],
      ),
    );
  }

  screenBody() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isFullScreen)
            SafeArea(
              child: Container(
                color: white,
                child: Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: dW * 0.05),
                      child: Text(
                        language['youtube'],
                        style: textTheme.headline3!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFF6B6C75),
                        ),
                      ),
                    ),
                    const Spacer(),
                    const IconButton(
                      onPressed: pop,
                      icon: Icon(
                        Icons.clear,
                        color: Color(0XFF000000),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          YoutubePlayerBuilder(
            player: YoutubePlayer(
              controller: _controller,
              showVideoProgressIndicator: true,
              progressIndicatorColor: const Color(0XFF975EFF),
              progressColors: const ProgressBarColors(
                playedColor: Color(0XFF975EFF),
                handleColor: Color(0XFF975EFF),
              ),
            ),
            builder: (context, player) {
              return player;
            },
          ),
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.05,
                right: dW * 0.05,
                top: dW * 0.08,
                bottom: dW * 0.02),
            child: GradientWidget(
              gradient: LinearGradient(colors: gradientColors),
              child: Text(
                '${trimesterText(int.parse(widget.args.trimester))} ' +
                    language['trimester'],
                style: textTheme.headline2!.copyWith(
                  fontSize: tS * 12,
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.05,
                right: dW * 0.05,
                top: dW * 0.01,
                bottom: dW * 0.02),
            child: Text(
              widget.args.selectedDiet.name,
              style: textTheme.headline1!.copyWith(
                fontSize: tS * 16,
                color: const Color(0XFF1D1E22),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: dW * 0.03,
              right: dW * 0.03,
              top: dW * 0.05,
            ),
            child: Html(data: widget.args.selectedDiet.description),
          )
        ],
      ),
    );
  }
}
