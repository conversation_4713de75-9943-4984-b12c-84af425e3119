import '../dailyActivityModule/model/daily_activity_model.dart';
import '../diaryModule/model/diary_model.dart';
import '../homeModule/models/article_model.dart';
import 'dart:ui';

class HomeScreenArguments {
  const HomeScreenArguments();
}

class SelectLanguageScreenArguments {
  final bool fromOnboarding;
  const SelectLanguageScreenArguments({this.fromOnboarding = false});
}

class BottomNavArgumnets {
  final int index;
  BottomNavArgumnets({this.index = 0});
}

class VerifyOtpArguments {
  final String mobileNo;
  // final EndUserAddress? address;
  VerifyOtpArguments({
    required this.mobileNo,
    //this.address
  });
}

class RegistrationArguments {
  final String mobileNo;
  RegistrationArguments({required this.mobileNo});
}

class PersonalizePromptArguments {
  PersonalizePromptArguments();
}

class ChoosePragnancyStatusArguments {
  ChoosePragnancyStatusArguments();
}

class SelectPragnancyWeekArguments {
  late String status;
  SelectPragnancyWeekArguments({required this.status});
}

class PregnancyResultArguments {
  final String week;
  PregnancyResultArguments({required this.week});
}

class PersonalizeLoaderArguments {
  PersonalizeLoaderArguments();
}

class SelectPeriodDateArguments {
  SelectPeriodDateArguments();
}

class HomeArguments {
  HomeArguments();
}

// Chat GPT
class ChatGptScreenArguments {
  final String? session;

  ChatGptScreenArguments({this.session});
}

class RechargeWalletSummaryScreenArguments {
  final num amount;
  RechargeWalletSummaryScreenArguments({required this.amount});
}

class PaymentScreenArguments {
  final String orderId;
  final num amount;
  final String type;
  final Map transaction;
  // final List subscription;
  // final String subType;

  PaymentScreenArguments({
    required this.orderId,
    required this.amount,
    required this.type,
    required this.transaction,
    // this.subscription = const [],
    // this.subType = '',
  });
}

class StartRecordingArguments {
  StartRecordingArguments();
}

// class PlayAudioArguments {
//   final DateTime createdAt;
//   final String recordingName;
//   final String lyrics;
//   final String recording;
//   PlayAudioArguments({
//     required this.createdAt,
//     required this.recordingName,
//     required this.lyrics,
//     required this.recording,
//   });
// }

class PlayAudioArguments {
  final List<Recording> recordings;
  final int currentIndex;

  PlayAudioArguments({
    required this.recordings,
    required this.currentIndex,
  });
}

class YogaArguments {
  final String title;
  YogaArguments({
    required this.title,
  });
}

class YogaDetailsArguments {
  final String trimester;
  final List<Yoga> yoga;
  final Yoga selectedYoga;
  YogaDetailsArguments({
    required this.trimester,
    required this.yoga,
    required this.selectedYoga,
  });
}

class ChantsArguments {
  final String title;
  final Chant? chant;
  ChantsArguments({required this.title, this.chant});
}

// class MeditationArguments {
//   final String title;
//   final Meditation? meditation;
//   MeditationArguments({
//     required this.title,
//     this.meditation,
//   });
// }

class MeditationArguments {
  final String title;
  MeditationArguments({
    required this.title,
  });
}

class MeditationDetailsArguments {
  final String trimester;
  final List<Meditation> meditation;
  final Meditation selectedMeditation;
  MeditationDetailsArguments({
    required this.trimester,
    required this.meditation,
    required this.selectedMeditation,
  });
}

class BedtimeStoryArguments {
  final String title;
  final BedtimeStory? bedtimeStory;
  BedtimeStoryArguments({
    required this.title,
    this.bedtimeStory,
  });
}

class BedtimeStoryAudioArguments {
  final BedtimeStory bedtimeStory;
  final int currentIndex;
  final bool fromLikeScreen;
  final Function? playOrView;
  bool minimised;
  final Function? toggleAudioPlayer;
  final String? selectedPeriod;
  // final BedTimeCategory category;

  BedtimeStoryAudioArguments({
    required this.bedtimeStory,
    required this.currentIndex,
    this.fromLikeScreen = false,
    this.minimised = false,
    this.toggleAudioPlayer,
    this.selectedPeriod,
    this.playOrView,
    // required this.category,
  });
}

class BedtimeStoryHtmlContentArguments {
  final BedtimeStory bedtimeStory;
  final int currentIndex;
  bool minimised;
  final Function? toggleAudioPlayer;
  final String? selectedPeriod;
  final Function? playOrView;
  // final BedTimeCategory category;

  BedtimeStoryHtmlContentArguments({
    required this.bedtimeStory,
    required this.currentIndex,
    this.minimised = false,
    this.toggleAudioPlayer,
    this.selectedPeriod,
    this.playOrView,
    // required this.category,
  });
}

class RagaMusicArguments {
  final String title;
  final RagaMusic? ragaMusic;
  RagaMusicArguments({
    required this.title,
    this.ragaMusic,
  });
}

class DietArguments {
  final String title;
  DietArguments({
    required this.title,
  });
}

// class DietDetailsArguments {
//   final String videoUrl;
//   final String videoTitle;
//   final String ingredients;
//   final String steps;

//   DietDetailsArguments({
//     required this.videoUrl,
//     required this.videoTitle,
//     required this.ingredients,
//     required this.steps,
//   });
// }

class DietDetailsArguments {
  final String trimester;
  final List<Diet> diet;
  final Diet selectedDiet;
  DietDetailsArguments({
    required this.trimester,
    required this.diet,
    required this.selectedDiet,
  });
}

class SetPreferenceArguments {
  final String title;
  final Function? callback;
  SetPreferenceArguments({
    required this.title,
    this.callback,
  });
}

class EditSetPreferenceArguments {
  final String appBarTitle;
  final Preferences preference;
  EditSetPreferenceArguments({
    required this.appBarTitle,
    required this.preference,
  });
}

class ArticleDetailScreenArguments {
  final Article article;
  ArticleDetailScreenArguments({
    required this.article,
  });
}

class GbVideoDetailScreenArguments {
  final Article article;
  GbVideoDetailScreenArguments({
    required this.article,
  });
}

class VideoPlayerScreenArguments {
  final String link;
  VideoPlayerScreenArguments({
    required this.link,
  });
}

class PrivacyPolicyOrTermsAndConditionsArguments {
  final String contentType;
  final String title;
  PrivacyPolicyOrTermsAndConditionsArguments({
    required this.contentType,
    required this.title,
  });
}

class LesserKnownDetailScreenArguments {
  final List<Facts> lesserKnown;
  LesserKnownDetailScreenArguments({
    required this.lesserKnown,
  });
}

class SwitchPreferenceScreenArguments {
  SwitchPreferenceScreenArguments();
}

class EditProfileScreenArguments {
  EditProfileScreenArguments();
}

class LikedStoryArguments {
  LikedStoryArguments();
}

class LoadingScreenArguments {
  final String type;
  final String featureId;
  LoadingScreenArguments({
    required this.type,
    required this.featureId,
  });
}

class PhonePeGatewayScreenArguments {
  final String link;
  PhonePeGatewayScreenArguments({
    required this.link,
  });
}

class SubscriptionArguments {
  SubscriptionArguments();
}

class MasterClassScreenArguments {
  final String title;
  MasterClassScreenArguments({
    required this.title,
  });
}

class MasterClassDetailsScreenArguments {
  final List<MasterClass> masterClass;
  final MasterClass selectedMasterClass;
  final VoidCallback? onVideoProgress;
  final Map videoPercentage;

  MasterClassDetailsScreenArguments({
    required this.masterClass,
    required this.selectedMasterClass,
    this.onVideoProgress,
    required this.videoPercentage,
  });
}
