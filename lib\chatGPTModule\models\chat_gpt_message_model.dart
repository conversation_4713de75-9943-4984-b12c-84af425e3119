import 'chat_gpt_content_model.dart';

class ChatGPTMessage {
  final String id;
  final String session;
  final String content;
  final String role;
  bool isLocal;
  final DateTime createdAt;

  ChatGPTMessage({
    required this.id,
    required this.session,
    required this.role,
    required this.content,
    this.isLocal = false,
    required this.createdAt,
  });

  static ChatGPTMessage jsonToChatGPTMessage(Map messageData) => ChatGPTMessage(
        id: messageData['_id'],
        session: messageData['session'],
        role: messageData['role'],
        content: messageData['content'],
        createdAt: DateTime.parse(messageData['createdAt']).toLocal(),
      );
}
