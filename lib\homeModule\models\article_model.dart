class Article {
  final String id;
  final String type;
  final String category;
  final String imageUrl;
  final String title;
  final String shortDescription;
  final String htmlContent;
  final String videoUrl;

  Article({
    required this.id,
    required this.type,
    required this.category,
    required this.imageUrl,
    required this.title,
    required this.shortDescription,
    required this.htmlContent,
    required this.videoUrl,
  });

  static Article jsonToArticle(Map article) => Article(
        id: article['_id'],
        type: article['type'],
        category: article['category'],
        imageUrl: article['imageUrl'] ?? '',
        title: article['title'] ?? '',
        shortDescription: article['shortDescription'] ?? '',
        htmlContent: article['htmlContent'],
        videoUrl: article['videoUrl'] ?? '',
      );
}

class Facts {
  final String id;
  final String type;
  final String imageUrl;
  final String title;
  final String description;
  final List trimesters;
  final List months;
  final List weeks;

  Facts({
    required this.id,
    required this.type,
    required this.imageUrl,
    required this.title,
    required this.description,
    this.trimesters = const [],
    this.months = const [],
    this.weeks = const [],
  });

  static Facts jsonToFacts(Map facts) => Facts(
        id: facts['_id'],
        type: facts['type'] ?? '',
        imageUrl: facts['imageUrl'] ?? '',
        title: facts['title'] ?? '',
        description: facts['description'] ?? '',
        trimesters: facts['trimesters'] ?? [],
        months: facts['months'] ?? [],
        weeks: facts['weeks'] ?? [],
      );
}

class Testimonial {
  final String id;
  final String imageUrl;
  final String description;
  final String name;
  final String source;
  final List trimesters;
  final List months;
  final List weeks;

  Testimonial({
    required this.id,
    required this.imageUrl,
    required this.description,
    required this.name,
    required this.source,
    required this.trimesters,
    required this.months,
    required this.weeks,
  });

  static Testimonial jsonToTestimonial(Map testimonial) => Testimonial(
        id: testimonial['_id'],
        imageUrl: testimonial['imageUrl'] ?? '',
        description: testimonial['description'] ?? '',
        name: testimonial['name'] ?? '',
        source: testimonial['source'] ?? '',
        trimesters: testimonial['trimesters'],
        months: testimonial['months'],
        weeks: testimonial['weeks'],
      );
}

class Faq {
  final String id;
  final String question;
  final String answer;

  Faq({
    required this.id,
    required this.question,
    required this.answer,
  });

  static Faq jsonToFaq(Map faq) => Faq(
        id: faq['_id'],
        question: faq['question'] ?? '',
        answer: faq['answer'] ?? '',
      );
}
