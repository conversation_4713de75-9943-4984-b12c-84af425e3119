// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/moreModule/widgets/profile_widget.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/my_dialog_box.dart';
import '../../common_functions.dart';
import '../../dynamic_Link_api.dart';
import '../../main.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/account_settings.dart';

class MoreScreen extends StatefulWidget {
  const MoreScreen({super.key});

  @override
  State<MoreScreen> createState() => _MoreScreenState();
}

class _MoreScreenState extends State<MoreScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;

  late String thisVersion;
  Map? deleteFeature;

  bool isLoading = false;

  logout() async {
    Provider.of<AuthProvider>(context, listen: false).logout();
    pushAndRemoveUntil(NamedRoute.loginScreen);
  }

  deleteAccount() async {
    setState(() => isLoading = true);
    final response =
        await Provider.of<AuthProvider>(context, listen: false).deleteAccount();
    setState(() => isLoading = false);
    if (response['success']) {
      logout();
    } else {
      showSnackbar(language[response['message']]);
    }
  }

  confirmDeleteAccount() {
    showDialog(
      context: navigatorKey.currentContext!,
      // barrierDismissible: false,
      builder: (ctx) => AlertDialog(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        titlePadding: const EdgeInsets.all(0),
        contentPadding: const EdgeInsets.all(0),
        insetPadding: const EdgeInsets.all(0),
        content: MyDialogBox(
          header: language['deleteAccount'],
          confirmDescription: language['confirmDeleteAccount'],
          leftBtnTxt: 'yes',
          leftBtnFunc: () => {pop(), deleteAccount()},
          rightBtnTxt: 'no',
          rightBtnFunc: pop,
          // highlightRight: false,
        ),
      ),
    );
  }

  showLogoutDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialogBox(
            title: language['logout'],
            firstButton: FilledDialogButton(
              onPressed: pop,
              text: language['cancel'],
            ),
            secondButton: DialogTextButton(
              onPressed: logout,
              text: language['yesLogout'],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();

    final AuthProvider auth = Provider.of<AuthProvider>(context, listen: false);

    user = auth.user;
    thisVersion = Platform.isAndroid ? auth.androidVersion : auth.iOSVersion;
    deleteFeature = auth.deleteFeature;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    language["deleteAccount"] = "Delete Account";
    language["confirmDeleteAccount"] =
        "Are you sure you want to delete your profile?";
    language["deleteAccountFail"] = "Failed to delete account";

    return Scaffold(
      backgroundColor: const Color(0XFFF7F8FC),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  width: dW,
                  decoration: BoxDecoration(color: white, boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF975EFF).withOpacity(0.08),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 4),
                    ),
                  ]),
                  padding: EdgeInsets.only(
                    left: dW * horizontalPaddingFactor,
                    right: dW * horizontalPaddingFactor,
                    top: dW * (iOSCondition(dH) ? 0.01 : 0.05),
                    bottom: dW * 0.04,
                  ),
                  child: Text(
                    language['more'],
                    style: textTheme.headline1!.copyWith(
                      fontSize: tS * 12,
                      color: lightBlack,
                    ),
                  ),
                ),
                const ProfileWidget(),
                Container(
                  height: dW * 0.007,
                  margin: EdgeInsets.only(bottom: dW * 0.06),
                  color: const Color(0XFFFFFFFF),
                ),
                Container(
                  decoration: BoxDecoration(
                      color: const Color(0XFFFFFFFF),
                      borderRadius: BorderRadius.circular(8)),
                  margin: EdgeInsets.symmetric(horizontal: dW * 0.04),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: dW * 0.015),
                            height: dW * 0.08,
                            width: dW * 0.01,
                            decoration: const BoxDecoration(
                                gradient: LinearGradient(colors: [
                                  Color(0XFFCE1B69),
                                  Color(0XFFFF328B)
                                ]),
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(5),
                                    bottomRight: Radius.circular(5))),
                          ),
                          SizedBox(width: dW * 0.05),
                          Text(
                            language['switchPref'],
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF84858E),
                            ),
                          ),
                        ],
                      ),
                      AccountSettings(
                        title: language['yourPref'],
                        svg: 'your_pref',
                        function: () {
                          push(NamedRoute.switchPreferenceScreen);
                        },
                        showBorder: false,
                        status: user.status,
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                      color: const Color(0XFFFFFFFF),
                      borderRadius: BorderRadius.circular(8)),
                  margin: EdgeInsets.only(
                      left: dW * 0.04, right: dW * 0.04, top: dW * 0.03),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: dW * 0.015),
                            height: dW * 0.08,
                            width: dW * 0.01,
                            decoration: const BoxDecoration(
                                gradient: LinearGradient(colors: [
                                  Color(0XFFCE1B69),
                                  Color(0XFFFF328B)
                                ]),
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(5),
                                    bottomRight: Radius.circular(5))),
                          ),
                          SizedBox(width: dW * 0.05),
                          Text(
                            language['more'],
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF84858E),
                            ),
                          ),
                        ],
                      ),
                      AccountSettings(
                        title: language['t&c'],
                        svg: 't&c',
                        function: () {
                          push(
                              NamedRoute
                                  .privacyPolicyOrTermsAndConditionsScreen,
                              arguments:
                                  PrivacyPolicyOrTermsAndConditionsArguments(
                                      title: language['tnc'],
                                      contentType: 'TERMSANDCONDITIONS'));
                        },
                      ),
                      AccountSettings(
                        title: language['privacyPolicy'],
                        svg: 'privacy_policy',
                        function: () {
                          push(
                              NamedRoute
                                  .privacyPolicyOrTermsAndConditionsScreen,
                              arguments:
                                  PrivacyPolicyOrTermsAndConditionsArguments(
                                      title: language['privacyPolicy'],
                                      contentType: 'PRIVACYPOLICY'));
                        },
                      ),
                      AccountSettings(
                        title: language['shareApp'],
                        svg: 'share_app',
                        function: () => createAppLink(),
                      ),
                      if (deleteFeature != null &&
                          deleteFeature!['version'] == thisVersion &&
                          deleteFeature!['enabled'])
                        AccountSettings(
                          title: language['deleteAccount'],
                          svg: 'delete_account',
                          function: confirmDeleteAccount,
                        ),
                      AccountSettings(
                        title: language['logOut'],
                        svg: 'log_out',
                        function: showLogoutDialog,
                        showBorder: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
  }
}
