// ignore_for_file: must_be_immutable, avoid_print, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';

class SettingBottomSheet extends StatefulWidget {
  String selectedOption;
  List options;
  String title;
  final bool isRagaMusic;
  final RagaMusic? ragaMusic;
  final String selectedDuration;
  final bool customSetCounter;

  SettingBottomSheet({
    Key? key,
    required this.selectedOption,
    required this.options,
    required this.title,
    this.ragaMusic,
    this.isRagaMusic = false,
    this.selectedDuration = '',
    this.customSetCounter = false,
  }) : super(key: key);

  @override
  State<SettingBottomSheet> createState() => _SettingBottomSheetState();
}

class _SettingBottomSheetState extends State<SettingBottomSheet> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  TextTheme get textTheme => Theme.of(context).textTheme;
  TextEditingController customSetCounterController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  save() async {
    bool isValid = _formKey.currentState!.validate();
    if (!isValid) {
      setState(() {});
      return;
    }
    pop();
    pop('${customSetCounterController.text.trim()} times');
  }

  customSetCounterSheet() {
    // hideKeyBoard();
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        // isDismissible: false,
        // constraints: BoxConstraints(maxHeight: dH * 0.4),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.0),
            topRight: Radius.circular(15.0),
          ),
        ),
        builder: (BuildContext context) => Container(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      decoration:
                          BoxDecoration(border: Border(bottom: dividerBorder)),
                      padding: EdgeInsets.only(
                          left: dW * 0.08,
                          right: dW * 0.08,
                          bottom: dW * 0.04,
                          top: dW * 0.04),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            language['setCustomCounter'],
                            style:
                                Theme.of(context).textTheme.headline3!.copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0xFF515259),
                                    ),
                          ),
                          GestureDetector(
                            onTap: pop,
                            child: Container(
                              padding: const EdgeInsets.all(5),
                              child: const Icon(Icons.clear,
                                  color: Colors.black, size: 20),
                            ),
                          )
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                          top: dW * 0.05, left: dW * 0.05, right: dW * 0.05),
                      child: CustomTextFieldWithLabel(
                        hintText: language['enterCounter'],
                        controller: customSetCounterController,
                        borderColor: greyBorderColor,
                        inputType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return language['enterValue'];
                          }
                          return null;
                        },
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                          top: dW * 0.05,
                          left: dW * 0.05,
                          right: dW * 0.05,
                          bottom: dW * 0.05),
                      child: GradientButton(
                        elevation: 0,
                        onPressed: save,
                        buttonText: language['save'],
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  // @override
  // void dispose() {
  //   super.dispose();
  //   customSetCounterController.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    // final double height = societies.length <= 4 ? dW * 1.2 : dW * 1.4;

    return Container(
      width: dW,
      // height: height,
      margin: EdgeInsets.symmetric(vertical: dW * 0.015),
      child: Column(
        // crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            decoration: BoxDecoration(border: Border(bottom: dividerBorder)),
            padding: EdgeInsets.only(
                left: dW * 0.08,
                right: dW * 0.08,
                bottom: dW * 0.04,
                top: dW * 0.02),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: Theme.of(context).textTheme.headline3!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0xFF515259),
                      ),
                ),
                GestureDetector(
                  onTap: pop,
                  child: Container(
                    // decoration: BoxDecoration(
                    //   color: Colors.grey.shade300,
                    //   borderRadius: BorderRadius.circular(50),
                    // ),
                    padding: const EdgeInsets.all(5),
                    child:
                        const Icon(Icons.clear, color: Colors.black, size: 20),
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(
                horizontal: dW * 0.09,
                vertical: dW * 0.035,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (widget.customSetCounter == true)
                    GestureDetector(
                      onTap: () {
                        customSetCounterSheet();
                      },
                      child: Container(
                        alignment: Alignment.centerLeft,
                        margin:
                            EdgeInsets.only(top: dW * 0.045, bottom: dW * 0.04),
                        color: transparentColor,
                        child: Row(
                          children: [
                            const Icon(Icons.add, size: 16),
                            SizedBox(width: dW * 0.01),
                            Text(
                              language['setCustomCounter'],
                              style: Theme.of(context)
                                  .textTheme
                                  .headline2!
                                  .copyWith(
                                      fontSize: tS * 14,
                                      color: const Color(0xFF515259)),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (widget.isRagaMusic && widget.ragaMusic != null)
                    ...widget.ragaMusic!.audioVersions
                        .map((version) => GestureDetector(
                              onTap: () => setState(() {
                                pop(version);
                              }),
                              child: Container(
                                padding:
                                    EdgeInsets.symmetric(vertical: dW * 0.05),
                                width: dW,
                                color: Colors.transparent,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: dW * 0.8,
                                      child: Text(
                                        '${widget.ragaMusic!.name} - ${version.duration}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .headline2!
                                            .copyWith(
                                                fontSize: tS * 14,
                                                color: version.duration ==
                                                        widget.selectedDuration
                                                    ? const Color(0XFFFF328B)
                                                    : const Color(0xFF515259)),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ))
                        .toList(),
                  if (!widget.isRagaMusic || widget.ragaMusic == null)
                    ...widget.options
                        .map((o) => GestureDetector(
                              onTap: () => setState(() {
                                pop(o);
                              }),
                              child: Container(
                                padding:
                                    EdgeInsets.symmetric(vertical: dW * 0.05),
                                width: dW,
                                color: Colors.transparent,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: dW * 0.8,
                                      child: Text(
                                        o,
                                        style: Theme.of(context)
                                            .textTheme
                                            .headline2!
                                            .copyWith(
                                                fontSize: tS * 14,
                                                color: o ==
                                                        widget.selectedOption
                                                    ? const Color(0XFFFF328B)
                                                    : const Color(0xFF515259)),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ))
                        .toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
