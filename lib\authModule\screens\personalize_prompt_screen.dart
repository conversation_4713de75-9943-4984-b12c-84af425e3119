// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../colors.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../commonWidgets/my_text_button.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class PersonalizePromptScreen extends StatelessWidget {
  PersonalizePromptScreen({super.key});

  final LocalStorage storage = LocalStorage('9&BEYOND');
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      backgroundColor: white,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding:
                          EdgeInsets.only(left: 38, right: 38, top: dW * 0.2),
                      child: Image.asset(
                        'assets/images/personalize_app_graphic.png',
                        height: 221.46,
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(left: 38, right: 38, top: dW * 0.15),
                      child: Text(
                        language['personalizeTitle'],
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.headline1!.copyWith(
                              fontSize: tS * 16,
                              color: const Color(0XFF1D1E22),
                            ),
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(left: 38, right: 38, top: dW * 0.05),
                      child: Text(
                        language['personalizeSubtitle'],
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.headline3!.copyWith(
                              fontSize: tS * 14,
                              color: const Color(0XFF515259),
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: GradientButton(
                isLoading: isLoading,
                onPressed: () {
                  push(NamedRoute.choosePragnancyStatusScreen,
                      arguments: ChoosePragnancyStatusArguments());
                },
                buttonText: language['letsStart'],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: MyTextButton(
                color: Colors.grey,
                text: language['skip'],
                onPressed: () {
                  push(NamedRoute.bottomNavBarScreen,
                      arguments: BottomNavArgumnets());
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
