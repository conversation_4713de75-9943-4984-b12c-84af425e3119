// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../authModule/providers/auth_provider.dart';
import '../common_functions.dart';

class DiaryBackground extends StatelessWidget {
  DiaryBackground({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    final double dH = MediaQuery.of(context).size.height;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return SizedBox(
        height: dH,
        width: dW,
        child:
            Image.asset('assets/images/dairy_bg_new.gif', fit: BoxFit.cover));
  }
}
