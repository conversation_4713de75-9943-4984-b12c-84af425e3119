import 'package:flutter/material.dart';
import 'common_functions.dart';

Color get themeColor => Theme.of(bContext).primaryColor;

const transparentColor = Colors.transparent;
const Color offWhite = Color(0xFFF1F1F1);
const Color lightGray = Color(0xFF9798A3);
const Color backButtonColor = Color(0xFF1D1E22);
const Color appBarTitleColor = Color(0xFF5E5E5E);
const Color grayColor = Color(0xFF5E5E5E);
const Color disabledColor = Color(0xFFFAE1CD);
const Color greyBorderColor = Color(0xFFD9D9D9);
const Color redColor = Color(0xFFDD4F4D);
const Color yellowColor = Color(0xFFFFB200);
const Color greenColor = Color(0xFF34B53A);
const Color dividerColor = Color(0xFFEAEAEA);
const Color blackColor = Colors.black;
const Color blackColor3 = Color(0xFF3E3E3E);
const Color placeholderColor = Color(0xFFAAABB5);
const Color highlightColor = Color(0xFFD7FEFF);
const Color white = Colors.white;
const Color lueLine = Color(0xFF9AFCFF);
const Color lightBlue = Color(0xFF8DA4FF);
const Color darkBlue = Color(0xFF6580EE);
const Color lightBlack = Color(0xFF1D1E22);
Color baseColorShimmer = Colors.white.withOpacity(0.9);
Color highlightColorShimmer = Colors.grey.withOpacity(0.5);
Color containerDecorationColorShimmer = Colors.white.withOpacity(0.7);
