// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/dailyActivityModule/widgets/chants_widget.dart';
import 'package:provider/provider.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/activity_illustration_image.dart';
import '../widgets/audio_player_widget.dart';

class ChantsScreen extends StatefulWidget {
  final ChantsArguments args;
  const ChantsScreen({super.key, required this.args});

  @override
  State<ChantsScreen> createState() => _ChantsScreenState();
}

class _ChantsScreenState extends State<ChantsScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  int currentIndex = 0;
  bool playing = false;

  late Chant playingChant;

  bool minimised = false;

  late User user;
  bool isLoading = false;

  List month = [1, 2, 3, 4, 5, 6, 7, 8, 9];

  int selectedMonth = 1;

  List<Chant> chants = [];

  bool showAudioPlayerWidget = false;

  final ItemScrollController monthScrollController = ItemScrollController();
  final ItemPositionsListener monthPositionsListener =
      ItemPositionsListener.create();

  scrollMonths() {
    Future.delayed(const Duration(seconds: 0)).then((value) {
      monthScrollController.scrollTo(
        index: ((selectedMonth + 1) < 3 ? 0 : (selectedMonth + 1) - 3).toInt(),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOutCubic,
      );
    });
  }

  toggleAudioPlayer(bool val, [bool killPlayer = false]) {
    if (killPlayer) {
      setState(() {
        playing = false;
        minimised = false;
      });
      return;
    }
    setState(() {
      minimised = val;
    });
  }

  monthText(int selectedMonth) {
    switch (selectedMonth) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      case 4:
        return language['4th'];
      case 5:
        return language['5th'];
      case 6:
        return language['6th'];
      case 7:
        return language['7th'];
      case 8:
        return language['8th'];
      case 9:
        return language['9th'];
      default:
        return '';
    }
  }

  fetchChant() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .chants
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedChant(
      accessToken: user.accessToken,
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
    if (widget.args.chant != null) {
      final allChants =
          Provider.of<DailyActivityProvider>(context, listen: false).chants;
      int i = allChants
          .indexWhere((element) => element.id == widget.args.chant!.id);
      if (i != -1) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.0),
              topRight: Radius.circular(15.0),
            ),
          ),
          builder: (BuildContext context) => AudioPlayerWidget(
            chant: allChants[i],
            currentIndex: i,
            selectedPeriod: selectedMonth,
            minimised: minimised,
            toggleAudioPlayer: toggleAudioPlayer,
          ),
        );
        // scrollMonths();
      }
    }
    scrollMonths();
  }

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    selectedMonth = user.pregnancyMonth.toInt();
    selectedMonth = selectedMonth < 0
        ? 0
        : selectedMonth > 9
            ? 9
            : selectedMonth;
    if (selectedMonth > 9) {
      selectedMonth = 9;
    }

    fetchChant();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    chants = Provider.of<DailyActivityProvider>(context)
        .getChantUsingMonth(selectedMonth);

    return Scaffold(
      body: Stack(
        alignment: AlignmentDirectional.bottomCenter,
        children: [
          SizedBox(
            height: dH,
            width: dW,
            child: Scaffold(
              backgroundColor: white,
              appBar: CustomAppBar(title: widget.args.title, dW: dW),
              body: iOSCondition(dH)
                  ? screenBody()
                  : SafeArea(child: screenBody()),
            ),
          ),
          if (playing)
            AudioPlayerWidget(
              chant: playingChant,
              currentIndex: currentIndex,
              minimised: minimised,
              toggleAudioPlayer: toggleAudioPlayer,
              selectedPeriod: selectedMonth,
            )
        ],
      ),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : SizedBox(
            height: dH,
            width: dW,
            child: Column(
              children: [
                SizedBox(height: dW * 0.04),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        SizedBox(
                          height: dW * 0.08,
                          child: ScrollablePositionedList.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: 9,
                            padding: EdgeInsets.only(right: 10),
                            itemScrollController: monthScrollController,
                            itemPositionsListener: monthPositionsListener,
                            physics: const ClampingScrollPhysics(),
                            itemBuilder: (context, i) => GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedMonth = i + 1;
                                });
                                scrollMonths();
                              },
                              child: Container(
                                margin: EdgeInsets.only(left: dW * 0.03),
                                width: dW * 0.25,
                                decoration: BoxDecoration(
                                  color: selectedMonth == i + 1
                                      ? const Color(0XFF975EFF)
                                      : const Color(0XFFF4F4F4),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Center(
                                  child: Text(
                                    '${monthText(i + 1)} ' + language['month'],
                                    style: textTheme.headline2!.copyWith(
                                      fontSize: tS * 12,
                                      color: selectedMonth == i + 1
                                          ? const Color(0XFFFFFFFF)
                                          : const Color(0XFF000000),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: dW * 0.07),
                        Padding(
                          padding: EdgeInsets.only(
                              left: dW * 0.04, right: dW * 0.04),
                          child: Row(
                            children: [
                              const AssetSvgIcon(
                                'more_yoga',
                                color: Color(0XFFFF328B),
                              ),
                              SizedBox(width: dW * 0.03),
                              Text(
                                language['soothingChants'],
                                style: textTheme.headline2!.copyWith(
                                  fontSize: tS * 12,
                                  color: const Color(0XFF1D1E22),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.05),
                        ListView.builder(
                          shrinkWrap: true,
                          itemCount: chants.length,
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, i) => GestureDetector(
                            onTap: () {
                              if (chants[i].audio.isNotEmpty) {
                                setState(() {
                                  playingChant = chants[i];
                                  playing = true;
                                  currentIndex = i;
                                });
                              } else {
                                buySubscriptionDialog();
                              }
                            },
                            child: ChantsWidget(
                              playAudio: () {
                                setState(() {
                                  playingChant = chants[i];
                                  playing = true;
                                  currentIndex = i;
                                });
                              },
                              chant: chants[i],
                              activityTitle: widget.args.title,
                              selectedPeriod: selectedMonth,
                              currentIndex: currentIndex,
                              minimised: minimised,
                              toggleAudioPlayer: toggleAudioPlayer,
                            ),
                          ),
                        ),
                        ActivityIllustrationImage('chants_illus.png'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
  }
}
