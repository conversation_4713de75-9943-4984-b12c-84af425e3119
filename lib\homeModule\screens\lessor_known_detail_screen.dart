// ignore_for_file: deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/scrolling_page_indicator.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';

class LesserKnownDetailScreen extends StatefulWidget {
  final LesserKnownDetailScreenArguments args;
  const LesserKnownDetailScreen({Key? key, required this.args})
      : super(key: key);

  @override
  LesserKnownDetailScreenState createState() => LesserKnownDetailScreenState();
}

class LesserKnownDetailScreenState extends State<LesserKnownDetailScreen>
    with TickerProviderStateMixin {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  final PageController _pageController = PageController();
  late AnimationController fadeAnimationController;
  late Animation<double> fadeAnimation;
  int _currentPage = 0;

  fetchData() async {
    setState(() => isLoading = true);
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fadeAnimationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: fadeAnimationController,
        curve: Curves.easeIn,
      ),
    );
    _pageController.addListener(() {
      setState(() {
        // fadeAnimationController.reverse();
        _currentPage = _pageController.page?.round() ?? 0;
      });
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      appBar: CustomAppBar(
        title: '',
        leadingWidth: dW * 0.115,
        titleWidget: Container(
          alignment: Alignment.center,
          child: ScrollingPageIndicator(
            controller: _pageController,
            itemCount: widget.args.lesserKnown.length,
            dotColor: const Color(0xffDBDBE3),
            dotSelectedColor: const Color(0xffCE1B69),
            dotSize: 7,
            dotSelectedSize: 7,
          ),
        ),
        centerTitle: true,
        dW: dW,
        leading: const SizedBox.shrink(),
        actions: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(right: dW * 0.05),
                child: GestureDetector(
                  onTap: pop,
                  child: const Icon(
                    Icons.clear_rounded,
                    color: blackColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            children: [
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (_) {
                    fadeAnimationController.forward();
                  },
                  scrollDirection: Axis.horizontal,
                  children: [
                    ...widget.args.lesserKnown
                        .map((e) => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (e.id == widget.args.lesserKnown.first.id)
                                  Padding(
                                    padding: EdgeInsets.only(top: dW * 0.03),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child:
                                          //  CachedImageWidget(
                                          //   e.imageUrl,
                                          //   boxFit: BoxFit.fill,
                                          //   width: dW,
                                          //   height: dH * 0.375,
                                          // ),
                                          CachedNetworkImage(
                                        repeat: ImageRepeat.repeat,
                                        fit: BoxFit.fill,
                                        // width: dW,
                                        // height: dH * 0.375,
                                        imageUrl: e.imageUrl,
                                        placeholder: (_, __) {
                                          return const SizedBox.shrink();
                                        },
                                      ),
                                    ),
                                  ),
                                if (e.id != widget.args.lesserKnown.first.id)
                                  Padding(
                                    padding: EdgeInsets.all(dW * 0.05),
                                    child: FadeTransition(
                                      opacity: fadeAnimation,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: CachedImageWidget(
                                          e.imageUrl,
                                          boxFit: BoxFit.cover,
                                          width: dW,
                                          height: dH * 0.35,
                                        ),
                                      ),
                                    ),
                                  ),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.05),
                                  child: e.id !=
                                          widget.args.lesserKnown.first.id
                                      ? FadeTransition(
                                          opacity: fadeAnimation,
                                          child: Text(
                                            e.title,
                                            style: Theme.of(context)
                                                .textTheme
                                                .headline1!
                                                .copyWith(
                                                    fontSize: tS * 16,
                                                    color:
                                                        const Color(0XFF1D1E22),
                                                    height: 1.7),
                                          ),
                                        )
                                      : Text(
                                          e.title,
                                          style: Theme.of(context)
                                              .textTheme
                                              .headline1!
                                              .copyWith(
                                                  fontSize: tS * 16,
                                                  color:
                                                      const Color(0XFF1D1E22),
                                                  height: 1.7),
                                        ),
                                ),
                                e.description.isEmpty
                                    ? Padding(
                                        padding: EdgeInsets.only(
                                            top: dW * 0.03,
                                            left: dW * 0.05,
                                            right: dW * 0.05),
                                        child: Text(
                                          language['swipeLeft'],
                                          style: Theme.of(context)
                                              .textTheme
                                              .headline3!
                                              .copyWith(
                                                fontSize: tS * 10,
                                                color: const Color(0XFF515259),
                                              ),
                                        ),
                                      )
                                    : Padding(
                                        padding: EdgeInsets.only(
                                            top: dW * 0.03,
                                            left: dW * 0.05,
                                            right: dW * 0.05),
                                        child: FadeTransition(
                                          opacity: fadeAnimation,
                                          child: Text(
                                            e.description,
                                            style: Theme.of(context)
                                                .textTheme
                                                .headline3!
                                                .copyWith(
                                                    fontSize: tS * 14,
                                                    color:
                                                        const Color(0XFF6B6C75),
                                                    height: 1.7),
                                          ),
                                        ),
                                      ),
                              ],
                            ))
                        .toList()
                  ],
                ),
              ),
            ],
          );
  }
}
