import 'package:nine_and_beyond/common_functions.dart';

import '../../dailyActivityModule/model/daily_activity_model.dart';

class User {
  final String id;
  String fullName;
  final String phone;
  String email;
  final String avatar;
  final bool isActive;
  bool completedWalkThrough;
  final String accessToken;
  String fcmToken;
  String? status;
  num pregnancyWeek;
  num pregnancyDay;
  num pregnancyTrimester;
  num pregnancyMonth;
  num gptCredits;
  DateTime? conceptionDate;
  Plan? plan;
  // final String fcmToken;
  String masterClassCount;

  User({
    required this.id,
    required this.fullName,
    required this.phone,
    required this.email,
    required this.avatar,
    required this.isActive,
    required this.completedWalkThrough,
    required this.accessToken,
    this.fcmToken = '',
    this.status,
    required this.pregnancyWeek,
    required this.pregnancyDay,
    required this.pregnancyTrimester,
    required this.pregnancyMonth,
    this.gptCredits = 0,
    this.conceptionDate,
    required this.plan,
    required this.masterClassCount,
  });

  static User jsonToUser(
    Map user, {
    required String accessToken,
  }) {
    int? pregnancyDay;
    int? pregnancyWeek;
    int? pregnancyMonth;
    int? pregnancyTrimester;

    if (user['conceptionDate'] != null) {
      pregnancyDay = DateTime.now()
          .difference(getParseDate(user['conceptionDate'])!)
          .inDays;
      if (pregnancyDay < 0) {
        pregnancyWeek = 0;
      } else {
        pregnancyWeek = (pregnancyDay / 7).ceil();
        pregnancyMonth = (pregnancyDay / 30).ceil();
        pregnancyTrimester = (pregnancyDay / 90).ceil();

        pregnancyWeek = pregnancyWeek > 40 ? 40 : pregnancyWeek;
        pregnancyTrimester = pregnancyTrimester > 3 ? 3 : pregnancyTrimester;
      }
    }

    return User(
      id: user['_id'],
      fullName: user['fullName'] ?? '',
      phone: user['phone'] ?? '',
      email: user['email'] ?? '',
      avatar: user['avatar'] ?? '',
      accessToken: accessToken,
      isActive: user['isActive'] ?? true,
      completedWalkThrough: user['completedWalkThrough'] ?? false,
      status: user['pregnanacyStatus'] ?? '',
      gptCredits: user['gptCredits'] ?? 0,
      pregnancyDay: pregnancyDay ?? 0,
      pregnancyWeek: pregnancyWeek ?? 0,
      pregnancyMonth: pregnancyMonth ?? 0,
      pregnancyTrimester: pregnancyTrimester ?? 0,
      // pregnancyDay: 3,
      // pregnancyWeek: 3,
      // pregnancyMonth: 2,
      // pregnancyTrimester: 3,
      conceptionDate: getParseDate(user['conceptionDate']),
      plan: (user['planId'] != null && user['planId'] is Map)
          ? Plan.jsonToPlan(user['planId'])
          : null,
      masterClassCount: user['masterClassCount'] ?? '',
    );
  }
}
