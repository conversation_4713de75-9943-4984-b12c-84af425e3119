// ignore_for_file: deprecated_member_use, avoid_print, prefer_typing_uninitialized_variables, no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../common_functions.dart';
import '../../dynamic_Link_api.dart';
import '../providers/daily_activity_provider.dart';
import '../widgets/bedtime_story_widget.dart';
import '../widgets/setting_bottom_sheet.dart';

class BedtimeStoryAudioScreen extends StatefulWidget {
  final BedtimeStoryAudioArguments args;

  const BedtimeStoryAudioScreen({super.key, required this.args});

  @override
  State<BedtimeStoryAudioScreen> createState() =>
      _BedtimeStoryAudioScreenState();
}

class _BedtimeStoryAudioScreenState extends State<BedtimeStoryAudioScreen> {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late String albumArt;
  late String audioUrl;
  late String name;
  bool isLoading = false;

  late BedTimeCategory currentCategory;
  bool timerColor = false;
  bool speedColor = true;
  String selectedValue = '';

  String selectedSpeed = '1x';
  Timer? _timer;

  // bool collaps = false;

  late TextEditingController _changeSpeedController;
  late TextEditingController _audioStopController;

  List changeSpeedOptions = [
    '0.5x',
    '0.8x',
    '1x',
    '1.2x',
    '1.5x',
    '2x',
  ];

  List audioStopOptions = [
    '5 minutes',
    '10 minutes',
    '15 minutes',
    '30 minutes',
    '45 minutes',
    '1 hour',
    'End of the story',
  ];

  final player = AudioPlayer();
  Duration currentPos = Duration.zero;
  Duration duration = Duration.zero;
  int currentIndex = 0;
  bool isPlaying = false;
  bool isPaused = false;

  seekToDuration(double d) async {
    await player.seek(Duration(milliseconds: d.toInt()));
    currentPos = Duration(milliseconds: d.toInt());
    setState(() {});
  }

  seekForward() async {
    int newPosition = currentPos.inMilliseconds + 10000;
    await player.seek(Duration(milliseconds: newPosition));
    currentPos = Duration(milliseconds: newPosition);
    setState(() {});
  }

  seekBackward() async {
    int newPosition = currentPos.inMilliseconds - 10000;
    if (newPosition < 0) {
      newPosition = 0;
    }
    await player.seek(Duration(milliseconds: newPosition));
    currentPos = Duration(milliseconds: newPosition);
    setState(() {});
  }

  play({bool firstTime = false}) async {
    try {
      final bedTimeStoryAudio = widget.args.fromLikeScreen == true
          ? Provider.of<DailyActivityProvider>(context, listen: false)
              .likedBedtimeStories
          : Provider.of<DailyActivityProvider>(context, listen: false)
              .getBedtimeStoryUsingCategory(widget.args.selectedPeriod!);
      name = firstTime
          ? widget.args.bedtimeStory.name
          : bedTimeStoryAudio[currentIndex].name;
      albumArt = firstTime
          ? widget.args.bedtimeStory.albumArt
          : bedTimeStoryAudio[currentIndex].albumArt;
      audioUrl = firstTime
          ? widget.args.bedtimeStory.audio
          : bedTimeStoryAudio[currentIndex].audio;

      await player.play(UrlSource(audioUrl));
      isPaused = false;
      setState(() {});
    } catch (e) {
      print(e);
      showSnackbar(language['failedPlay'], Colors.red);
    }
  }

  pause() async {
    await player.pause();
    isPaused = true;
    setState(() {});
  }

  resume() async {
    await player.resume();
    isPaused = false;
    setState(() {});
  }

  initAudioPlayer() {
    if (Platform.isIOS) {
      final AudioContext audioContext = AudioContext(
        iOS: AudioContextIOS(
          defaultToSpeaker: true,
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.defaultToSpeaker,
            AVAudioSessionOptions.mixWithOthers,
          ],
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.sonification,
          usageType: AndroidUsageType.assistanceSonification,
          audioFocus: AndroidAudioFocus.none,
        ),
      );
      AudioPlayer.global.setGlobalAudioContext(audioContext);
    }
    play(firstTime: true);
    isLoading = true;
    player.onPlayerStateChanged.listen((state) {
      if (mounted) {
        setState(() {
          isPlaying = state == PlayerState.playing;
        });
      }

      isLoading = false;
    });

    player.onDurationChanged.listen((newDuration) {
      if (mounted) {
        setState(() {
          duration = newDuration;
        });
      }
    });

    player.onPositionChanged.listen((newPos) {
      if (mounted) {
        setState(() {
          currentPos = newPos;
        });
      }
    });

    player.onPlayerComplete.listen((event) {
      if (_timer != null && _timer!.isActive) {
        play();
      } else {
        setState(() {
          isPlaying = false;
          currentPos = Duration.zero;
        });
      }
    });
  }

  releasePlayer() async {
    await player.release();
  }

  changeAudioSpeed(double speed) async {
    await player.setPlaybackRate(speed);
    setState(() {});
  }

  changeSpeedSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      constraints: BoxConstraints(maxHeight: dH * 0.4),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => SettingBottomSheet(
        title: language['changeSpeed'],
        selectedOption: selectedSpeed,
        options: changeSpeedOptions,
      ),
    ).then((value) {
      if (value != null) {
        double speed = double.parse(value.replaceAll('x', ''));
        changeAudioSpeed(speed);
        setState(() {
          selectedSpeed = value;
          speedColor = true;
        });
      }
    });
  }

  audioStop(String time) async {
    late Duration duration;
    if (time == "End of the story") {
      // pause();
      if (_timer != null && _timer!.isActive) {
        _timer!.cancel();
      }
    } else {
      final split = time.split(" ");
      if (split[1].contains("minute")) {
        duration = Duration(minutes: int.parse(split[0]));
        print("Audio Stop in = $duration");
      } else if (split[1].contains("hour")) {
        final hourSplit = split[0].split(".");
        if (hourSplit.length == 1) {
          duration = Duration(hours: int.parse(split[0]));
        } else {
          duration = Duration(
              minutes:
                  int.parse(hourSplit[1]) + (int.parse(hourSplit[0]) * 60));
        }
        print("Audio Stop in = $duration");
      }
      if (_timer != null && _timer!.isActive) {
        _timer!.cancel();
      }
      _timer = Timer(duration, () {
        pause();
        _timer!.cancel();
      });
    }
  }

  audioStopSheet() {
    // hideKeyBoard();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      // isDismissible: false,
      constraints: BoxConstraints(maxHeight: dH * 0.5),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15.0),
          topRight: Radius.circular(15.0),
        ),
      ),
      builder: (BuildContext context) => SettingBottomSheet(
        title: language['audioStop'],
        selectedOption: selectedValue,
        options: audioStopOptions,
      ),
    ).then((value) async {
      if (value != null) {
        selectedValue = value;
        await audioStop(value);
        setState(() {
          timerColor = true;
        });
        showSnackbar('${language['audioStop']} : $selectedValue',
            const Color(0xff975EFF));
      }
    });
  }

  myInit() {
    // user = Provider.of<AuthProvider>(context, listen: false).user;

    currentIndex = widget.args.currentIndex;

    initAudioPlayer();
    _changeSpeedController = TextEditingController();
    _audioStopController = TextEditingController();

    currentCategory = widget.args.bedtimeStory.category;
  }

  @override
  void didUpdateWidget(covariant BedtimeStoryAudioScreen oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);

    if (oldWidget.args.bedtimeStory.id != widget.args.bedtimeStory.id) {
      myInit();
    }
  }

  @override
  void initState() {
    super.initState();

    myInit();
  }

  @override
  void dispose() {
    super.dispose();
    releasePlayer();
    player.onPlayerStateChanged.drain();
    player.onDurationChanged.drain();
    player.onPositionChanged.drain();
    player.onPlayerComplete.drain();
    _changeSpeedController.dispose();
    _audioStopController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    int currentMinutes = currentPos.inMinutes;
    int currentSeconds = currentPos.inSeconds % 60;

    int durationMinutes = duration.inMinutes;
    int durationSeconds = duration.inSeconds % 60;

    final bedtimeStories =
        Provider.of<DailyActivityProvider>(context, listen: true).bedtimeStory;

    final likedBedtimeStories =
        Provider.of<DailyActivityProvider>(context, listen: true)
            .likedBedtimeStories;

    return widget.args.minimised
        ? GestureDetector(
            onTap: () {
              if (widget.args.toggleAudioPlayer != null) {
                widget.args.toggleAudioPlayer!(false);
              }
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              color: const Color(0XFFD6EAFF),
              height: dW * 0.27,
              width: dW,
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(3),
                    child: CachedImageWidget(
                      albumArt,
                      boxFit: BoxFit.cover,
                      width: dW * 0.26,
                      height: dW * 0.27,
                    ),
                  ),
                  SizedBox(width: dW * 0.04),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: dW * 0.4,
                        child: Text(
                          name,
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF1D1E22),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      SizedBox(height: dW * 0.02),
                      Row(
                        children: [
                          Text(
                            '$currentMinutes.${currentSeconds.toString().padLeft(2, '0')}/',
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF515259),
                            ),
                          ),
                          Text(
                            '$durationMinutes.${durationSeconds.toString().padLeft(2, '0')}',
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF515259),
                            ),
                          ),
                          SizedBox(width: dW * 0.01),
                          Text(
                            language['minC'],
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0xFF515259),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          GestureDetector(
                              onTap: () => isPlaying
                                  ? pause()
                                  : isPaused
                                      ? resume()
                                      : play(),
                              child: AssetSvgIcon(
                                isPlaying ? 'pause' : 'play',
                                color: const Color(0XFF1D1E22),
                                height: 20,
                              )),
                          SizedBox(width: dW * 0.04),
                          GestureDetector(
                            onTap: () {
                              if (widget.args.toggleAudioPlayer != null) {
                                widget.args.toggleAudioPlayer!(false, true);
                              }
                            },
                            child: const Icon(Icons.clear,
                                color: Colors.black, size: 20),
                          ),
                          SizedBox(width: dW * 0.04),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        : Scaffold(
            body: SizedBox(
            height: dH,
            width: dW,
            child: Column(
              children: [
                SizedBox(height: dW * 0.12),
                Padding(
                  padding: EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          if (widget.args.toggleAudioPlayer != null) {
                            widget.args.toggleAudioPlayer!(true);
                          } else if (widget
                              .args.bedtimeStory.audio.isNotEmpty) {
                            pop();
                          }
                        },
                        child: Container(
                            color: transparentColor,
                            child: const Icon(Icons.arrow_back)),
                      ),
                      SizedBox(width: dW * 0.04),
                      Text(
                        language['back'],
                        style: textTheme.headline1!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFF1D1E22),
                        ),
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () => createActivityLink(
                            widget.args.bedtimeStory.id, 'Bedtime Story'),
                        child: const AssetSvgIcon('share'),
                      )
                    ],
                  ),
                ),
                SizedBox(height: dW * 0.07),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(3),
                          child: CachedImageWidget(
                            widget.args.fromLikeScreen == true
                                ? likedBedtimeStories[currentIndex].albumArt
                                : albumArt,
                            boxFit: BoxFit.cover,
                            width: dW * 0.9,
                            height: dW * 0.9,
                          ),
                        ),
                        SizedBox(height: dW * 0.17),
                        Container(
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.only(
                              left: dW * 0.05, right: dW * 0.05),
                          child: Text(
                            widget.args.fromLikeScreen == true
                                ? likedBedtimeStories[currentIndex].name
                                : name,
                            style: textTheme.headline1!.copyWith(
                              fontSize: tS * 12,
                              color: const Color(0XFF1D1E22),
                            ),
                          ),
                        ),
                        Column(
                          children: [
                            SizedBox(height: dW * 0.05),
                            Slider(
                              activeColor: const Color(0xff975EFF),
                              inactiveColor: const Color(0xffF4F4F4),
                              value: currentPos.inMilliseconds.toDouble(),
                              onChanged: seekToDuration,
                              max: duration.inMilliseconds.toDouble(),
                            ),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: dW * 0.05, right: dW * 0.05),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '$currentMinutes:${currentSeconds.toString().padLeft(2, '0')}',
                                    style: textTheme.headline3!.copyWith(
                                      fontSize: tS * 14,
                                      color: const Color(0XFF515259),
                                    ),
                                  ),
                                  Text(
                                    '$durationMinutes:${durationSeconds.toString().padLeft(2, '0')}',
                                    style: textTheme.headline3!.copyWith(
                                      fontSize: tS * 14,
                                      color: const Color(0XFF515259),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: dW * 0.06),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                GestureDetector(
                                  onTap: changeSpeedSheet,
                                  child: Text(
                                    selectedSpeed,
                                    style: textTheme.headline1!.copyWith(
                                      fontSize: tS * 14,
                                      color: speedColor
                                          ? const Color(0XFFFF328B)
                                          : const Color(0XFF292D32),
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                    onTap: seekBackward,
                                    child: const AssetSvgIcon('backward')),
                                Container(
                                  height: dW * 0.15,
                                  width: dW * 0.15,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    gradient:
                                        LinearGradient(colors: gradientColors),
                                    borderRadius: BorderRadius.circular(108),
                                  ),
                                  child: GestureDetector(
                                      onTap: () => isPlaying
                                          ? pause()
                                          : isPaused
                                              ? resume()
                                              : play(),
                                      child: isLoading
                                          ? circularForButton(dW * 0.05,
                                              color: white, sW: 2.3)
                                          : AssetSvgIcon(
                                              isPlaying ? 'pause' : 'play',
                                              color: const Color(0XFFFFFFFF),
                                            )),
                                ),
                                GestureDetector(
                                    onTap: seekForward,
                                    child: const AssetSvgIcon('forward')),
                                Column(
                                  children: [
                                    if (selectedValue.isNotEmpty)
                                      SizedBox(height: dW * 0.05),
                                    GestureDetector(
                                      onTap: () {
                                        audioStopSheet();
                                      },
                                      child: AssetSvgIcon(
                                        'clock',
                                        color: timerColor
                                            ? const Color(0XFFFF328B)
                                            : const Color(0XFF292D32),
                                      ),
                                    ),
                                    if (selectedValue.isNotEmpty)
                                      Padding(
                                        padding:
                                            EdgeInsets.only(top: dW * 0.01),
                                        child: Text(
                                          selectedValue.replaceAll(
                                              RegExp(r'[^0-9]'), ''),
                                          style: textTheme.headline1!.copyWith(
                                            fontSize: tS * 12,
                                            color: const Color(0XFFFF328B),
                                          ),
                                        ),
                                      ),
                                  ],
                                )
                              ],
                            ),
                            SizedBox(height: dW * 0.1),
                            Padding(
                              padding: EdgeInsets.only(
                                  left: dW * 0.05, right: dW * 0.05),
                              child: Row(
                                children: [
                                  const AssetSvgIcon(
                                    'more_yoga',
                                    color: Color(0XFFFF328B),
                                  ),
                                  SizedBox(width: dW * 0.03),
                                  Text(
                                    language['listenSimilar'],
                                    style: textTheme.headline2!.copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0XFF1D1E22),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            ListView.builder(
                              shrinkWrap: true,
                              itemCount: widget.args.fromLikeScreen
                                  ? likedBedtimeStories
                                      .where((story) =>
                                          story.type == 'Listen' &&
                                          story.category.id ==
                                              currentCategory.id &&
                                          story.id !=
                                              widget.args.bedtimeStory.id)
                                      .length
                                  : bedtimeStories
                                      .where((story) =>
                                          story.type == 'Listen' &&
                                          story.category.id ==
                                              currentCategory.id &&
                                          story.id !=
                                              widget.args.bedtimeStory.id)
                                      .length,
                              physics: const BouncingScrollPhysics(),
                              itemBuilder: (context, i) {
                                final listenBedtimeStories =
                                    widget.args.fromLikeScreen
                                        ? likedBedtimeStories
                                            .where((story) =>
                                                story.type == 'Listen' &&
                                                story.category.id ==
                                                    currentCategory.id &&
                                                story.id !=
                                                    widget.args.bedtimeStory.id)
                                            .toList()
                                        : bedtimeStories
                                            .where((story) =>
                                                story.type == 'Listen' &&
                                                story.category.id ==
                                                    currentCategory.id &&
                                                story.id !=
                                                    widget.args.bedtimeStory.id)
                                            .toList();
                                return GestureDetector(
                                  onTap: () {
                                    if (widget.args.playOrView != null) {
                                      widget.args
                                          .playOrView!(listenBedtimeStories[i]);
                                    }
                                  },
                                  child: BedtimeStoryWidget(
                                    playOrView: widget.args.playOrView,
                                    minimised: widget.args.minimised,
                                    toggleAudioPlayer:
                                        widget.args.toggleAudioPlayer,
                                    bedtimeStory: listenBedtimeStories[i],
                                    fromLikeScreen:
                                        widget.args.fromLikeScreen == true
                                            ? true
                                            : false,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ));
  }
}
