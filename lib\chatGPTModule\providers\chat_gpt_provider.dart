// ignore_for_file: prefer_final_fields

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/chatGPTModule/models/chat_gpt_session_model.dart';

import '../../api.dart';
import '../../http_helper.dart';
import '../models/chat_gpt_message_model.dart';
import '../models/credit_transaction_model.dart';

class ChatGPTProvider with ChangeNotifier {
  //

  List<ChatGPTMessage> _messages = [];
  List<ChatGPTMessage> get messages => [..._messages];

  List<ChatGPTSession> _sessions = [];
  List<ChatGPTSession> get sessions => [..._sessions];

  List<CreditTransaction> _creditTransactions = [];
  List<CreditTransaction> get creditTransactions => [..._creditTransactions];

  String? currentSessionId;

  clearMessages([notify = false]) {
    _messages.clear();
    if (notify) {
      notifyListeners();
    }
  }

  clearSessions() {
    _sessions.clear();
  }

  createMessage({
    required String accessToken,
    required Map body,
  }) async {
    try {
      _messages.add(ChatGPTMessage(
        id: messages.length.toString(),
        session: body['sessionId'],
        role: 'user',
        content: body['newMessage']['content'],
        isLocal: true,
        createdAt: DateTime.now(),
      ));

      notifyListeners();

      final url = '${webApi['domain']}${endPoint['createMessage']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        body: body,
        accessToken: accessToken,
      );

      if (response['success']) {
        final List<ChatGPTMessage> newMessages = response['result']
                ['newChatGptMessages']
            .map<ChatGPTMessage>(
                (msg) => ChatGPTMessage.jsonToChatGPTMessage(msg))
            .toList();

        _messages.removeWhere((element) => element.isLocal);
        _messages.addAll(newMessages);

        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetResponse'};
    }
  }

  fetchSession({
    required String accessToken,
    required String query,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchSession']}$query';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success'] && response['result'] != null) {
        final List<ChatGPTMessage> fetchedMessages = response['result']
                ['messages']
            .map<ChatGPTMessage>(
                (msg) => ChatGPTMessage.jsonToChatGPTMessage(msg))
            .toList();

        _messages = fetchedMessages;

        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSession'};
    }
  }

  fetchAllSessions({
    required String accessToken,
    bool refresh = false,
  }) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['fetchAllSessions']}?skip=${refresh ? 0 : _sessions.length}';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        final List<ChatGPTSession> fetchedSessions = response['result']
            .map<ChatGPTSession>(
                (session) => ChatGPTSession.jsonToChatGPTSession(session))
            .toList();

        if (refresh || _sessions.isEmpty) {
          _sessions = fetchedSessions;
        } else {
          _sessions.addAll(fetchedSessions);
        }

        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSession'};
    }
  }

  buyGptCredits({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['buyGptCredits']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        accessToken: accessToken,
        body: body,
      );

      if (response['success']) {
        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSession'};
    }
  }

  updateTransactionStatus({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updateTransactionStatus']}';
      final response = await RemoteServices.httpRequest(
        method: 'POST',
        url: url,
        accessToken: accessToken,
        body: body,
      );

      if (response['success']) {
        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSession'};
    }
  }

  fetchCreditHistory({
    required String accessToken,
    bool refresh = false,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchCreditHistory']}';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        final List<CreditTransaction> fetchedTransaction = response['result']
            .map<CreditTransaction>((transaction) =>
                CreditTransaction.jsonToCreditTransaction(transaction))
            .toList();

        // if (refresh || _sessions.isEmpty) {
        _creditTransactions = fetchedTransaction;
        // } else {
        //   _sessions.addAll(fetchedTransaction);
        // }

        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSession'};
    }
  }

  updateTransactionStatusToFailed({
    required String accessToken,
    required String transactionId,
  }) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['updateTransactionStatusToFailed']}/$transactionId';
      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        final List<CreditTransaction> fetchedTransaction = response['result']
            .map<CreditTransaction>((transaction) =>
                CreditTransaction.jsonToCreditTransaction(transaction))
            .toList();

        // if (refresh || _sessions.isEmpty) {
        _creditTransactions = fetchedTransaction;
        // } else {
        //   _sessions.addAll(fetchedTransaction);
        // }

        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSession'};
    }
  }
}
