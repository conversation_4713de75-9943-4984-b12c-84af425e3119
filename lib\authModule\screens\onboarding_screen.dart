// ignore_for_file: library_private_types_in_public_api, deprecated_member_use

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_button.dart';
import 'package:provider/provider.dart';

import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({super.key});

  @override
  _OnBoardingScreenState createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen> {
  final PageController _pageController = PageController(initialPage: 0);
  int _currentPage = 0;
  Timer? _timer;

  Map language = {};

  final List<String> _imageUrls = [
    'assets/images/onBoarding1.png',
    'assets/images/onBoarding2.png',
    'assets/images/onBoarding3.png',
    'assets/images/onBoarding4.png',
  ];

  final List<String> _text = [
    'Based on the ancient\nIndian wisdom of\nGarb<PERSON><PERSON>kar',
    'How will this application\nhelp you',
    'Positive and Stressfree\nPregnancy',
    'Talk About Anything and\nEverything',
  ];

  final List<String> _subText = [
    'India’s gift to humanity',
    'structured daily activity\n(nutrition , yoga ,meditation, mantra chanting,\nraga music ,garbh samvad)',
    'wellness application which promotes positive\nand stress free pregnancy',
    '• weekly baby tracker\n• Frequently asked question\n• Little known facts on pregnancy\n• Thought provoking & informative content\n• Daily notification\n• Weekly Emailer\n• Newsletter',
  ];

  Widget _buildSubText(int index) {
    if (_subText[index].contains('• weekly baby tracker') ||
        _subText[index].contains('• Frequently asked question') ||
        _subText[index].contains('• Little known facts on pregnancy')) {
      return Text(
        _subText[index],
        textAlign: TextAlign.start,
        style: textTheme.headline3!.copyWith(
          fontSize: MediaQuery.of(context).textScaleFactor * 14,
          color: const Color(0xff515259),
        ),
      );
    } else {
      return Text(
        _subText[index],
        textAlign: TextAlign.center,
        style: textTheme.headline3!.copyWith(
          fontSize: MediaQuery.of(context).textScaleFactor * 14,
          color: const Color(0xff515259),
        ),
      );
    }
  }

  TextTheme get textTheme => Theme.of(bContext).textTheme;

  _startAutoScroll() {
    const Duration autoScrollDuration = Duration(seconds: 6);
    _timer = Timer.periodic(autoScrollDuration, (Timer timer) {
      if (_currentPage < _imageUrls.length - 1) {
        _currentPage++;
      } else {
        _currentPage = 0;
      }
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    });
  }

  _stopAutoScroll() {
    _timer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _stopAutoScroll();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double dW = MediaQuery.of(context).size.width;
    // final double dH = MediaQuery.of(context).size.height;
    final double tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _imageUrls.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(top: dW * 0.1),
                    child: Column(
                      children: [
                        Image.asset(
                          _imageUrls[index],
                          height: dW * 0.86,
                        ),
                        Text(
                          _text[index],
                          textAlign: TextAlign.center,
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 24,
                            color: const Color(0xff1D1E22),
                          ),
                        ),
                        SizedBox(height: dW * 0.04),
                        _subText[index].contains(
                                '• weekly baby tracker\n• Frequently asked question\n• Little known facts on pregnancy\n• Thought provoking & informative content\n• Daily notification\n• Weekly Emailer\n• Newsletter')
                            ? Text(
                                _subText[index],
                                textAlign: TextAlign.start,
                                style: textTheme.headline3!.copyWith(
                                  fontSize: tS * 14,
                                  color: const Color(0xff515259),
                                ),
                              )
                            : Text(
                                _subText[index],
                                textAlign: TextAlign.center,
                                style: textTheme.headline3!.copyWith(
                                  fontSize: tS * 14,
                                  color: const Color(0xff515259),
                                ),
                              ),
                      ],
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: dW * 0.12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  _imageUrls.length,
                  (index) => buildDot(index, _currentPage),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 24, right: 24, bottom: 16),
              child: GradientButton(
                buttonText: language['getStarted'],
                onPressed: () => push(
                  NamedRoute.loginScreen,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildDot(int index, int currentPage) {
    return Container(
      width: index == currentPage ? 17 : 8,
      height: 8,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: index == currentPage
            ? const Color(0xffCE1B69)
            : const Color(0xff84858E),
      ),
    );
  }
}
