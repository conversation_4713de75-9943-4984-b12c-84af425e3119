// // ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings

// import 'package:flutter/material.dart';
// import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
// import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
// import 'package:provider/provider.dart';
// import '../../authModule/model/user_model.dart';
// import '../../authModule/providers/auth_provider.dart';
// import '../../colors.dart';
// import '../../commonWidgets/circular_loader.dart';
// import '../../commonWidgets/custom_app_bar.dart';
// import '../../common_functions.dart';
// import '../../navigation/arguments.dart';
// import '../widgets/activity_illustration_image.dart';
// import '../widgets/diet_widget.dart';

// class DietScreen extends StatefulWidget {
//   final DietArguments args;
//   const DietScreen({super.key, required this.args});

//   @override
//   State<DietScreen> createState() => _DietScreenState();
// }

// class _DietScreenState extends State<DietScreen> {
//   //
//   Map language = {};
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;
//   TextTheme get textTheme => Theme.of(context).textTheme;

//   late User user;
//   bool isLoading = false;

//   int currentIndex = 0;

//   List trimester = [1, 2, 3];

//   int selectedTrimester = 1;

//   trimesterText(int selectedTrimester) {
//     switch (selectedTrimester) {
//       case 1:
//         return language['1stTrimester'];
//       case 2:
//         return language['2ndTrimester'];
//       case 3:
//         return language['3rdTrimester'];
//       default:
//         return '';
//     }
//   }

//   List<Diet> diets = [];

//   fetchDiet() async {
//     if (Provider.of<DailyActivityProvider>(context, listen: false)
//         .diet
//         .isEmpty) {
//       setState(() => isLoading = true);
//     }
//     final response =
//         await Provider.of<DailyActivityProvider>(context, listen: false)
//             .fetchedDiet(
//       accessToken: user.accessToken,
//     );
//     if (!response['success']) {
//       showSnackbar(response['message']);
//     }
//     setState(() => isLoading = false);
//   }

//   @override
//   void initState() {
//     super.initState();

//     user = Provider.of<AuthProvider>(context, listen: false).user;
//     selectedTrimester = user.pregnancyTrimester.toInt();
//     fetchDiet();
//   }

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;
//     language = Provider.of<AuthProvider>(context).selectedLanguage;

//     diets = Provider.of<DailyActivityProvider>(context)
//         .getDietUsingTrimesterWithDay(selectedTrimester);

//     return Scaffold(
//       backgroundColor: white,
//       appBar: CustomAppBar(
//         title: widget.args.title,
//         dW: dW,
//       ),
//       body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
//     );
//   }

//   screenBody() {
//     return isLoading
//         ? const Center(child: CircularLoader())
//         : Column(
//             children: [
//               Expanded(
//                 child: SingleChildScrollView(
//                   physics: const AlwaysScrollableScrollPhysics(),
//                   child: Column(
//                     children: [
//                       Container(
//                         color: const Color(0XFFF7F8FC),
//                         child: SingleChildScrollView(
//                           padding: EdgeInsets.symmetric(vertical: dW * 0.065),
//                           scrollDirection: Axis.horizontal,
//                           child: Row(
//                             children: [
//                               ...trimester.map(
//                                 (i) => GestureDetector(
//                                   onTap: () {
//                                     setState(() {
//                                       selectedTrimester = i;
//                                     });
//                                   },
//                                   child: Container(
//                                     margin: EdgeInsets.only(left: dW * 0.03),
//                                     padding:
//                                         const EdgeInsets.symmetric(vertical: 5),
//                                     width: dW * 0.3,
//                                     decoration: BoxDecoration(
//                                       border: Border.all(
//                                           width: 1,
//                                           color: const Color(0XFFDBDBE3)),
//                                       color: selectedTrimester == i
//                                           ? const Color(0XFF975EFF)
//                                           : transparentColor,
//                                       borderRadius: BorderRadius.circular(20),
//                                     ),
//                                     child: Center(
//                                       child: Text(
//                                         trimesterText(i),
//                                         style: textTheme.headline2!.copyWith(
//                                           fontSize: tS * 12,
//                                           color: selectedTrimester == i
//                                               ? const Color(0XFFFFFFFF)
//                                               : const Color(0XFF6B6C75),
//                                           overflow: TextOverflow.ellipsis,
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               )
//                             ],
//                           ),
//                         ),
//                       ),
//                       Padding(
//                         padding: EdgeInsets.symmetric(
//                             horizontal: dW * 0.04, vertical: dW * 0.07),
//                         child: Row(
//                           children: [
//                             Image.asset(
//                               'assets/images/mom_graphic.png',
//                               height: dW * 0.05,
//                             ),
//                             SizedBox(width: dW * 0.01),
//                             Text(
//                               'Healthy Mom in ${trimesterText(selectedTrimester)}',
//                               style: textTheme.headline2!.copyWith(
//                                 fontSize: tS * 12,
//                                 color: const Color(0XFF1D1E22),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       ListView.builder(
//                         shrinkWrap: true,
//                         itemCount: diets.length,
//                         physics: const BouncingScrollPhysics(),
//                         itemBuilder: (context, i) => DietWidget(
//                           diet: diets[i],
//                         ),
//                       ),
//                       ActivityIllustrationImage('diet_illus.png'),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           );
//   }
// }

// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/activity_illustration_image.dart';
import '../widgets/diet_widget.dart';

class DietScreen extends StatefulWidget {
  final DietArguments args;
  const DietScreen({super.key, required this.args});

  @override
  State<DietScreen> createState() => _DietScreenState();
}

class _DietScreenState extends State<DietScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  List trimester = [1, 2, 3];

  int selectedTrimester = 1;

  List<Diet> diets = [];

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  fetchDiet() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .diet
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedDiet(
      accessToken: user.accessToken,
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    selectedTrimester = user.pregnancyTrimester.toInt();
    fetchDiet();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    diets = Provider.of<DailyActivityProvider>(context)
        .getDietUsingTrimesterWithDay(selectedTrimester);

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(title: widget.args.title, dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            children: [
              SizedBox(height: dW * 0.03),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: () {
                          push(NamedRoute.pregnancyGuideOnDietScreen);
                        },
                        child: Container(
                          margin: EdgeInsets.only(
                              left: dW * 0.025,
                              right: dW * 0.025,
                              bottom: dW * 0.02),
                          child: Image.asset(
                            'assets/images/pd.png',
                          ),
                        ),
                      ),
                      SizedBox(
                        height: dW * 0.08,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: [
                            Row(
                              children: [
                                ...trimester.map(
                                  (i) => GestureDetector(
                                    onTap: () {
                                      setState(
                                        () {
                                          selectedTrimester = i;
                                        },
                                      );
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(left: dW * 0.03),
                                      width: dW * 0.3,
                                      decoration: BoxDecoration(
                                        color: selectedTrimester == i
                                            ? const Color(0XFF975EFF)
                                            : const Color(0XFFF4F4F4),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Center(
                                        child: Text(
                                          '${trimesterText(i)} ' +
                                              language['trimester'],
                                          style: textTheme.headline2!.copyWith(
                                            fontSize: tS * 12,
                                            color: selectedTrimester == i
                                                ? const Color(0XFFFFFFFF)
                                                : const Color(0XFF000000),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.07),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: diets.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, i) => DietWidget(
                          diet: diets[i],
                          selectedTrimester: selectedTrimester,
                        ),
                      ),
                      ActivityIllustrationImage('diet_illus.png'),
                    ],
                  ),
                ),
              )
            ],
          );
  }
}
