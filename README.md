
# 9 & Beyond

The concept of this application is for women who are either pregnant, want to get pregnant or are in the post pregnancy period. The purpose of application if to help women through their journey of jouney of pregnancy and guide them of the dos, donts for a healthly and safe baby.

App users(roles):\
    This app does not have any spcific role. Any one can singup on our application regardless of even their gender. However, for now, the app shows content for pregnant women only.
    

# Project Design

Below is the link for FIGMA design file of 9&Beyond.

Desgin Link - https://www.figma.com/file/vBRuHWbEEUH18L00f0GBvy/%F0%9F%A4%B0%F0%9F%91%B6Papp-v3-Design?node-id=5070%3A88595&mode=dev


# System Architecture

Components:

    1. Database - MongoDB 
    2. Server - NodeJs in Typescript
    3. Application - Flutter Framework

# Application Structure & Modules:
     1. All locally used images and svg icons used are kept in the assets folder.
        a. project/assets
        b. Images in project/assets/images
        c. Svg Icons in project/assets/svgIcons
        d. Placeholders in project/assets/placeholders
        e. App logo path: assets/images/rrIcon.png
        f. The fonts used in the application are stored in the project/assets/fonts folder.
        
The application is split into the following Modules:
All source code files are kept inside the "lib" folder on the main level in their respective module folder.

    1. Auth Module - Auth module handles all the authentication/credentials and stores all the user's data.
    2. Home Module - This module contains all the screens, widgets, provider and models of the home module i.e. Home screen.
    3. Daily Activity Module - This module contains the files and folders for all the daily activties (i.e. Yoga, Diet, Meditaition, Chants, Bedtime stories, etc.).
    4. More Module - Contains edit profile, more screen, and view personal data.
    5. Chat GPT Module - This module contains the files and folders for the ChatGpt powered chatbot which answers question regarding pregnancy.
    6. Diary Module - Contains all screen, widget, models, providers realted to all the features in the More Section.


There are more single files handling the configurations in the application.

    1. api.dart - Holds the Server IP/domains and all the "apis" used to make http requests to the server.
    2. colors.dart - Some repeatedly used colors used thoughout the application are stored in this files and used everywhere needed.
    3. common_functions.dart - Some repeatedly used functions used thoughout the application are stored in this files and used everywhere needed.
    4. local_notification_service.dart - The code used to handle the local sent notifications.
    5. theme_mamager.dart - Holds the theme data of the entire application.
    5. storage_manager.dart - used to store and retrieve the theme data of the app stored in local storage.
    6. http_helper.dart - This files has all the custom boiler plate code needed to make http requests. All http requests are to be made by importing this file and using these functions.
        a. httprequest() - used for Normal http request that do not contain any files.
        b. formDataRequest() - Used for sending file data request.

There are some third party files in the application: 

    1. Firebase "google-services.json" in the android/app/ folder downloaded from firebase.
    2. Firebase "GoogleService-Info.plist" in the ios/runner folder downloaded from firebase.


# Server structure & Modules
    
    1. Authentication module (src/utils/generic/auth/) - 
        a. The send, verify and resend otp APIs are written in auth.controller.ts in the auth folder.
        b. The middleware folder has the createAccessToken method to assign auth_tokens to users.
    2. App Module (src/appModule) - 
        All module folders in here are used for the integration with the app.
        This module has submodules for articles, baby tracker, chatGpt, recording, transaction module. These submodules contains its respective models, controllers and routes for all the its related apis.
    3. All daily activites have their own module folders (diet, chants, meditation, bedtime stories, etc. ) and contain their respective controllers, models, and routes.
    4. Like module - There is a like module that has all the required files for like integration. The like module handles likes for all the the content in the application.
    5. Preference Module - This module has the required files for handling the preferences of the daily activities of each user.
    6. FaQ Module.
    7. Testimonials.
    8. Lesser Known Module.
    9. Facts Module.
        
    
# ChatGPT 
    1. We have used ChatGPT as an AI powered chatbot in the application. We have done this using the apis provided by OPENAI for sending a query to chatgpt and receiving the auto generated response by ChatGPT. We store all the questions and the responses in our database. 
    2. We charge certain credits for interacting with the chatbot. Prices are set in the backend and can be changed anytime.
    3. A user can buy credits by paying a certain amount.
        Currently,
        1 rupee = 1 credit.
        And 1 question = 0.25 credits.
    4. We are using GPT 3.5 right now.
    5. Chat GPT allows integration with OPENAI API Key which is stored in the .env file.
    You can find the integration in the OPEN AI API documentation.

# PhonePe
    As needed to buy credits for the chatbot, User has to buy the credits by paying real money. User can pay using the payment gateway integrated in the application. That payment gateway is the PhonePe Payment Gateway.
    All credentials for the phone gateway are in the .env file named: 
    1. PHONEPE_ENVIRONMENT
    2. PHONEPE_APPID
    3. PHONEPE_MERCHANT_ID
    4. PHONEPE_WEBHOOK_URL
    5. PHONEPE_API_ENDPOINT
    6. SALT_KEY
    7. SALT_INDEX
    You can find the integration in the PhonePe API documentation.
    

# Packages

Packages used in pubspec.yaml

    1. http
    2. pin_code_fields
    3. flutter_svg
    4. localstorage
    5. shared_preferences
    6. provider
    7. carousel_slider
    8. intl
    9. firebase_messaging
    10. flutter_local_notifications
    11. firebase_core
    12. image_picker
    13. email_validator
    14. url_launcher
    15. flutter_html
    16. file_picker
    17. razorpay_flutter
    18. permission_handler
    19. cached_network_image
    20. open_file_safe
    21. path_provider
    22. scrollable_positioned_list
    23. firebase_dynamic_links
    24. share_plus
    25. dio
    26. video_player
    27. better_player
    28. audioplayers
    29. record_mp3
    30. youtube_player_flutter
    31. card_swiper
    32. page_transition
    33. flutter_launcher_icons
    34. animate_do
    35. shimmer
    36. phonepe_payment_sdk
    37. device_info_plus
    38. flutter_webview_pro

    