// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/dotted_seperator.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../common_functions.dart';
import '../../dailyActivityModule/model/daily_activity_model.dart';
import '../../dailyActivityModule/providers/daily_activity_provider.dart';
import '../../dailyActivityModule/widgets/daily_activity_widget.dart';

class WelcomeToJourneyScreen extends StatefulWidget {
  const WelcomeToJourneyScreen({Key? key}) : super(key: key);

  @override
  WelcomeToJourneyScreenState createState() => WelcomeToJourneyScreenState();
}

class WelcomeToJourneyScreenState extends State<WelcomeToJourneyScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  List<DailyActivity> dailyActivities = [];

  fetchDailyActivity() async {
    setState(() => isLoading = true);
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedDailyActivity(accessToken: user.accessToken);
    setState(() => isLoading = false);

    if (!response['success']) {
      showSnackbar(response['message']);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchDailyActivity();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    dailyActivities = Provider.of<DailyActivityProvider>(context).dailyActivity;

    return Scaffold(
      body: SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: screenHorizontalPadding(dW),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          SizedBox(height: dW * 0.1),
                          Text(language['wlcmToYrGrbSnskrJrny'],
                              style: textTheme.headline1!.copyWith(
                                fontSize: tS * 14,
                                color: lightBlack,
                              )),
                          SizedBox(height: dW * 0.018),
                          Text(language['slctOneToBgnJrny'],
                              style: textTheme.headline3!.copyWith(
                                height: 1.8,
                                fontSize: tS * 11,
                                color: const Color(0xFF515259),
                              )),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: dW * 0.08),
                            child: DottedSeperator(
                              width: 5,
                              color: lightBlack.withOpacity(0.3),
                            ),
                          )
                        ]),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    itemCount: dailyActivities.length,
                    physics: const BouncingScrollPhysics(),
                    itemBuilder: (context, i) => DailyActivityWidget(
                      dailyActivity: dailyActivities[i],
                      fromGB: true,
                      dailyActivities: dailyActivities,
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
