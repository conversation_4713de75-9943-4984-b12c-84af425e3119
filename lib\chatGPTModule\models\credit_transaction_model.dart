import 'package:nine_and_beyond/common_functions.dart';

class CreditTransaction {
  final String id;
  final String title;
  final num amount;
  final String transactionType;
  final DateTime createdAt;

  CreditTransaction({
    required this.id,
    required this.title,
    required this.amount,
    required this.transactionType,
    required this.createdAt,
  });

  static CreditTransaction jsonToCreditTransaction(Map transaction) =>
      CreditTransaction(
        id: transaction['_id'].toString(),
        title: transaction['count'].toString(),
        amount: transaction['totalAmount'],
        transactionType: transaction['transactionType'],
        createdAt: getParseDate(transaction['date'])!,
      );
}
