// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/scrolling_page_indicator.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../models/article_model.dart';

class RecommendedArticleWidget extends StatefulWidget {
  List<Article> recommendedArticles;
  RecommendedArticleWidget({super.key, required this.recommendedArticles});

  @override
  State<RecommendedArticleWidget> createState() =>
      _RecommendedArticleWidgetState();
}

class _RecommendedArticleWidgetState extends State<RecommendedArticleWidget> {
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  Map language = {};

  TextTheme get textTheme => Theme.of(bContext).textTheme;

  final PageController _pageController = PageController(
    viewportFraction: 0.5,
    initialPage: 0,
  );

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
        padding: EdgeInsets.only(bottom: dW * 0.08),
        color: const Color(0XFFFFFFFF),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                  top: dW * 0.07,
                  bottom: dW * 0.05,
                  left: dW * 0.04,
                  right: dW * 0.04),
              child: Text(
                language['recommendedArticle'],
                style: Theme.of(context).textTheme.headline1!.copyWith(
                      fontSize: tS * 14,
                      color: const Color(0XFF1D1E22),
                    ),
              ),
            ),
            SizedBox(
              height: dW * 0.65,
              child: PageView.builder(
                physics: const BouncingScrollPhysics(),
                padEnds: false,
                controller: _pageController,
                scrollDirection: Axis.horizontal,
                itemCount: widget.recommendedArticles.length,
                itemBuilder: (context, index) {
                  final e = widget.recommendedArticles[index];
                  return GestureDetector(
                    onTap: () {
                      push(NamedRoute.articleDetailScreen,
                          arguments: ArticleDetailScreenArguments(article: e));
                    },
                    child: Container(
                      margin:
                          EdgeInsets.only(bottom: dW * 0.08, left: dW * 0.04),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: CachedImageWidget(
                              e.imageUrl,
                              boxFit: BoxFit.cover,
                              width: dW * 0.5,
                              height: dW * 0.403,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.03),
                            child: SizedBox(
                              width: dW * 0.5,
                              child: Text(
                                e.category,
                                style: const TextStyle(
                                  fontSize: 8,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'IBMPlexSansKR',
                                  letterSpacing: 2,
                                  color: Color(0XFFFF676E),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: dW * 0.02),
                            child: SizedBox(
                              width: dW * 0.5,
                              child: Text(
                                e.title,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context)
                                    .textTheme
                                    .headline1!
                                    .copyWith(
                                      fontSize: tS * 11,
                                      color: const Color(0XFF1D1E22),
                                    ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              alignment: Alignment.center,
              child: ScrollingPageIndicator(
                controller: _pageController,
                itemCount: widget.recommendedArticles.length - 1,
                dotColor: const Color(0xffDBDBE3),
                dotSelectedColor: blackColor,
                dotSize: 7,
                dotSelectedSize: 7,
              ),
            ),
          ],
        ));
  }
}
