// Daily Activity

class DailyActivity {
  final String id;
  String title;
  final String subTitle;
  final String banner;
  final String icon;
  final int position;
  final String recommendedText;
  final bool isDiet;

  DailyActivity({
    required this.id,
    required this.title,
    this.subTitle = '',
    this.banner = '',
    required this.icon,
    required this.position,
    this.recommendedText = '',
    required this.isDiet,
  });

  static DailyActivity jsonToDailyActivity(Map dailyActivity) {
    return DailyActivity(
      id: dailyActivity['_id'],
      title: dailyActivity['title'] ?? '',
      subTitle: dailyActivity['subTitle'] ?? '',
      banner: dailyActivity['banner'] ?? '',
      icon: dailyActivity['icon'] ?? '',
      position: dailyActivity['position'] ?? 0,
      recommendedText: dailyActivity['recommendedText'] ?? '',
      isDiet: dailyActivity['isDiet'] ?? false,
    );
  }
}

// Set Preference

class Preferences {
  final String id;
  List<num> timeSlots;
  final DailyActivity dailyActivity;
  bool isActive;

  Preferences({
    required this.id,
    required this.timeSlots,
    required this.dailyActivity,
    required this.isActive,
  });

  static Preferences jsonToPreferences(Map preferences) {
    return Preferences(
      id: preferences['_id'],
      timeSlots: List<num>.from(preferences['timeSlots']),
      dailyActivity:
          DailyActivity.jsonToDailyActivity(preferences['dailyActivity']),
      isActive: preferences['isActive'],
    );
  }
}

// Yoga

class Yoga {
  final String id;
  String name;
  final String duration;
  final String thumbnail;
  final String video;
  final String views;
  final List trimester;

  Yoga({
    required this.id,
    required this.name,
    required this.duration,
    required this.thumbnail,
    required this.video,
    required this.views,
    required this.trimester,
  });

  static Yoga jsonToYoga(Map yoga) {
    return Yoga(
        id: yoga['_id'],
        name: yoga['name'],
        duration: yoga['duration'],
        thumbnail: yoga['thumbnail'],
        video: yoga['video'] ?? '',
        views: yoga['views'],
        trimester: yoga['trimester']);
  }
}

// Chant
class Chant {
  final String id;
  String name;
  final String audio;
  final String duration;
  final String albumArt;
  final List month;
  final List position;

  Chant({
    required this.id,
    required this.name,
    required this.audio,
    required this.duration,
    required this.albumArt,
    required this.month,
    required this.position,
  });

  static Chant jsonToChant(Map chant) {
    return Chant(
      id: chant['_id'],
      name: chant['name'],
      audio: chant['audio'] ?? '',
      duration: chant['duration'],
      albumArt: chant['albumArt'],
      month: chant['month'],
      position: chant['position'] ?? [],
    );
  }
}

// Meditation

// class Meditation {
//   final String id;
//   String name;
//   final String audio;
//   final String duration;
//   final String albumArt;
//   final String albumArtGif;
//   final List trimester;
//   final List position;

//   Meditation({
//     required this.id,
//     required this.name,
//     required this.audio,
//     required this.duration,
//     required this.albumArt,
//     required this.albumArtGif,
//     required this.trimester,
//     required this.position,
//   });

//   static Meditation jsonToMeditation(Map meditation) {
//     return Meditation(
//       id: meditation['_id'],
//       name: meditation['name'],
//       audio: meditation['audio'],
//       duration: meditation['duration'],
//       albumArt: meditation['albumArt'],
//       albumArtGif: meditation['albumArtGif'] ?? '',
//       trimester: meditation['trimester'],
//       position: meditation['position'] ?? [],
//     );
//   }
// }

class Meditation {
  final String id;
  String name;
  final String thumbnail;
  final String video;
  final String duration;
  final List trimester;

  Meditation({
    required this.id,
    required this.name,
    required this.thumbnail,
    required this.video,
    required this.duration,
    required this.trimester,
  });

  static Meditation jsonToMeditation(Map meditation) {
    return Meditation(
        id: meditation['_id'],
        name: meditation['name'],
        thumbnail: meditation['thumbnail'],
        video: meditation['video'] ?? '',
        duration: meditation['duration'],
        trimester: meditation['trimester']);
  }
}

// Bedtime Story

class BedTimeCategory {
  final String id;
  final String name;
  final int position;

  BedTimeCategory({
    required this.id,
    required this.name,
    required this.position,
  });

  static BedTimeCategory jsonToBedtimeCategory(Map bedTimeCategory) =>
      BedTimeCategory(
        id: bedTimeCategory['_id'],
        name: bedTimeCategory['name'],
        position: bedTimeCategory['position'],
      );
}

class BedtimeStory {
  final String id;
  String name;
  final String type;
  final String audio;
  final String duration;
  final String albumArt;
  final String htmlContent;
  final BedTimeCategory category;
  final String authorName;
  bool isLiked;

  BedtimeStory({
    required this.id,
    required this.name,
    required this.type,
    this.audio = '',
    required this.duration,
    this.albumArt = '',
    this.htmlContent = '',
    required this.category,
    this.authorName = '',
    this.isLiked = false,
  });

  static BedtimeStory jsonToBedtimeStory(Map bedtimeStory) {
    return BedtimeStory(
      id: bedtimeStory['_id'],
      name: bedtimeStory['name'],
      type: bedtimeStory['type'],
      audio: bedtimeStory['audio'] ?? '',
      duration: bedtimeStory['duration'],
      albumArt: bedtimeStory['albumArt'] ?? '',
      htmlContent: bedtimeStory['htmlContent'] ?? '',
      category: BedTimeCategory.jsonToBedtimeCategory(bedtimeStory['category']),
      authorName: bedtimeStory['authorName'] ?? '',
      isLiked: bedtimeStory['isLiked'] ?? false,
    );
  }
}

// Raga Music

class RagaMusic {
  final String id;
  String name;
  // final String audio;
  final String duration;
  final String albumArt;
  final List trimester;
  final List position;
  final List<AudioVersion> audioVersions;

  RagaMusic({
    required this.id,
    required this.name,
    // required this.audio,
    required this.duration,
    required this.albumArt,
    required this.trimester,
    required this.position,
    required this.audioVersions,
  });

  static RagaMusic jsonToRagaMusic(Map ragaMusic) {
    return RagaMusic(
      id: ragaMusic['_id'],
      name: ragaMusic['name'],
      // audio: ragaMusic['audio'],
      duration: ragaMusic['duration'],
      albumArt: ragaMusic['albumArt'],
      trimester: ragaMusic['trimester'],
      position: ragaMusic['position'] ?? [],
      audioVersions: ragaMusic['audioVersions'] != null
          ? ragaMusic['audioVersions']
              .map<AudioVersion>(
                  (version) => AudioVersion.jsonToAudioVersion(version))
              .toList()
          : [],
    );
  }
}

class AudioVersion {
  final String duration;
  final String audioUrl;

  AudioVersion({
    required this.duration,
    required this.audioUrl,
  });
  static AudioVersion jsonToAudioVersion(Map audioVersion) {
    return AudioVersion(
      audioUrl: audioVersion['audioUrl'] ?? '',
      duration: audioVersion['duration'] ?? '',
    );
  }
}

// Diet

// class Diet {
//   final String id;
//   final String pictureUrl;
//   String title;
//   final double calories;
//   final List<Product> products;
//   final List trimester;
//   final List day;
//   final DailyActivity dailyActivity;

//   Diet({
//     required this.id,
//     required this.pictureUrl,
//     required this.title,
//     required this.calories,
//     required this.products,
//     required this.trimester,
//     required this.day,
//     required this.dailyActivity,
//   });

//   static Diet jsonToDiet(Map diet) {
//     return Diet(
//       id: diet['_id'],
//       pictureUrl: diet['pictureUrl'] ?? '',
//       title: diet['title'] ?? '',
//       calories: double.tryParse(diet['calories'] ?? 0) ?? 0,
//       products: (diet['dietProducts'] as List<dynamic>?)
//               ?.map((product) => Product.jsonToProduct(product))
//               .toList() ??
//           [],
//       trimester: diet['trimester'],
//       day: diet['day'] ?? [],
//       dailyActivity: DailyActivity.jsonToDailyActivity(diet['dailyActivity']),
//     );
//   }
// }

// class Product {
//   final String id;
//   final String name;
//   final String productPictureUrl;
//   final double weight;
//   final double calories;
//   final String videoUrl;
//   final String videoTitle;
//   final String ingredients;
//   final String steps;

//   Product({
//     required this.id,
//     required this.name,
//     required this.productPictureUrl,
//     required this.weight,
//     required this.calories,
//     required this.videoUrl,
//     required this.videoTitle,
//     required this.ingredients,
//     required this.steps,
//   });

//   static Product jsonToProduct(Map product) {
//     return Product(
//       id: product['_id'],
//       name: product['name'] ?? '',
//       productPictureUrl: product['productPictureUrl'] ?? '',
//       weight: double.parse(product['weight']),
//       calories: double.parse(product['calories']),
//       videoUrl: product['videoUrl'] ?? '',
//       videoTitle: product['videoTitle'] ?? '',
//       ingredients: product['ingredients'] ?? '',
//       steps: product['steps'] ?? '',
//     );
//   }
// }

class Diet {
  final String id;
  String name;
  final String description;
  final String thumbnail;
  final String video;
  final String duration;
  final List trimester;

  Diet({
    required this.id,
    required this.name,
    required this.description,
    required this.thumbnail,
    required this.video,
    required this.duration,
    required this.trimester,
  });

  static Diet jsonToDiet(Map diet) {
    return Diet(
        id: diet['_id'],
        name: diet['name'],
        description: diet['description'],
        thumbnail: diet['thumbnail'],
        video: diet['video'] ?? '',
        duration: diet['duration'],
        trimester: diet['trimester']);
  }
}

class Plan {
  final String id;
  String name;
  final String description;
  final String price;
  final num durationDays;
  final List access;
  final List content;

  Plan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationDays,
    required this.access,
    required this.content,
  });

  static Plan jsonToPlan(Map plan) {
    return Plan(
      id: plan['_id'],
      name: plan['name'],
      description: plan['description'],
      price: plan['price'],
      durationDays: plan['durationDays'],
      access: plan['access'],
      content: plan['content'],
    );
  }
}

class MasterClass {
  final String id;
  String name;
  final String description;
  final String thumbnail;
  final String video;
  final String duration;

  MasterClass({
    required this.id,
    required this.name,
    required this.description,
    required this.thumbnail,
    required this.video,
    required this.duration,
  });

  static MasterClass jsonToMasterClass(masterClass) {
    return MasterClass(
      id: masterClass['_id'],
      name: masterClass['name'],
      description: masterClass['description'],
      thumbnail: masterClass['thumbnail'],
      video: masterClass['video'] ?? '',
      duration: masterClass['duration'],
    );
  }
}
