// ignore_for_file: deprecated_member_use, must_be_immutable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../providers/daily_activity_provider.dart';
import '../widgets/bedtime_story_widget.dart';
import '../widgets/gradient_start_button.dart';

class LikedStoryScreen extends StatefulWidget {
  final Function? playOrView;
  final bool? minimised;
  final Function? toggleAudioPlayer;
  final Function hideScreen;
  final bool? fromLikedStoryScreen;

  const LikedStoryScreen({
    super.key,
    this.playOrView,
    this.minimised,
    this.toggleAudioPlayer,
    required this.hideScreen,
    this.fromLikedStoryScreen,
  });

  @override
  State<LikedStoryScreen> createState() => _LikedStoryScreenState();
}

class _LikedStoryScreenState extends State<LikedStoryScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  List<BedtimeStory> likedBedtimeStories = [];

  fetchLikedBedtimeStory() async {
    setState(() => isLoading = true);
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedBedtimeStory(
      accessToken: user.accessToken,
      query: '?fetchLiked=true',
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchLikedBedtimeStory();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    likedBedtimeStories =
        Provider.of<DailyActivityProvider>(context).likedBedtimeStories;

    return Scaffold(
      appBar: CustomAppBar(
        title: '',
        dW: dW,
        actionMethod: () {
          widget.hideScreen();
        },
      ),
      backgroundColor: const Color(0XFFF7F8FC),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      width: dW,
      height: dH,
      child: isLoading
          ? const Center(child: CircularLoader())
          : likedBedtimeStories.isNotEmpty
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                          left: dW * 0.04,
                          right: dW * 0.04,
                          top: dW * 0.07,
                          bottom: dW * 0.07),
                      child: Row(
                        children: [
                          Text(
                            language['likedStories'],
                            style: textTheme.headline2!.copyWith(
                              fontSize: tS * 14,
                              color: const Color(0XFF1D1E22),
                            ),
                          ),
                          SizedBox(width: dW * 0.025),
                          Text(
                            '(${likedBedtimeStories.length} ${language['story']})',
                            style: textTheme.headline2!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF84858E),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(bottom: dH * 0.2),
                        itemCount: likedBedtimeStories.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, i) => GestureDetector(
                          onTap: () {
                            if (widget.playOrView != null) {
                              widget.playOrView!(likedBedtimeStories[i], true);
                              if (widget.toggleAudioPlayer != null) {
                                widget.toggleAudioPlayer!(false);
                              }
                            }
                          },
                          child: BedtimeStoryWidget(
                            bedtimeStory: likedBedtimeStories[i],
                            fromLikeScreen: true,
                            minimised: widget.minimised,
                            playOrView: widget.playOrView,
                            toggleAudioPlayer: widget.toggleAudioPlayer,
                          ),
                        ),
                      ),
                    )
                  ],
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/fav_grpahic.png',
                      height: dW * 0.65,
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.09),
                      child: Text(
                        language['favEmpty'],
                        style: textTheme.headline1!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF1D1E22),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.025),
                      child: Text(
                        language['tapHeart'],
                        style: textTheme.headline3!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF515259),
                            height: 1.75),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          left: dW * 0.375, right: dW * 0.375, top: dW * 0.07),
                      width: dW,
                      height: dW * 0.1,
                      child: GradientStartButton(
                        buttonText: language['goToStories'],
                        onPressed: pop,
                      ),
                    )
                  ],
                ),
    );
  }
}
