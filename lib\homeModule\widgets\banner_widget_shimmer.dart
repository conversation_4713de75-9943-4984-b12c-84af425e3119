// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../common_functions.dart';

class BannerWidgetShimmer extends StatelessWidget {
  BannerWidgetShimmer({super.key});

  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Shimmer.fromColors(
      baseColor: baseColorShimmer,
      highlightColor: highlightColorShimmer,
      child: Container(
        width: dW,
        height: dH,
        decoration: BoxDecoration(color: containerDecorationColorShimmer),
      ),
    );
  }
}
