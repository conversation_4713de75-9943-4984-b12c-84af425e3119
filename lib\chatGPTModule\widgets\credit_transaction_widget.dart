// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/chatGPTModule/models/credit_transaction_model.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';

class CreditTransactionWidget extends StatelessWidget {
  final CreditTransaction transaction;
  CreditTransactionWidget({super.key, required this.transaction});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  Color get transactionColor => transaction.transactionType == 'Debit'
      ? const Color(0xFFC82525)
      : const Color(0xFF35B52B);

  String get amountText =>
      '${transaction.transactionType == 'Credit' ? '+' : '- '}\u20b9${transaction.amount}';

  String get title => transaction.transactionType == 'Credit'
      ? language['balAdToWal']
      : '${transaction.title} ${language['qnsAskd']}';

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      margin: EdgeInsets.only(bottom: dW * 0.02),
      decoration: BoxDecoration(
        color: white,
        border: Border.all(width: 1, color: const Color(0xFFF4F4F4)),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: dW * 0.035,
        vertical: dW * 0.025,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
                color: const Color(0xFFF7F8FC),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(width: 1, color: const Color(0xFFF4F4F4))),
            child: Image.asset(
                'assets/images/${transaction.transactionType.toLowerCase()}_trans.png',
                width: 32,
                height: 32),
          ),
          Container(
            margin: EdgeInsets.only(left: dW * 0.03),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(
                  width: dW * 0.45,
                  child: Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.headline2!.copyWith(
                          fontSize: tS * 11,
                          color: blackColor,
                        ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: dW * 0.025),
                  width: dW * 0.45,
                  child: Text(
                    DateFormat('dd MMM, yyyy').format(transaction.createdAt),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.headline2!.copyWith(
                          fontSize: tS * 10,
                          color: const Color(0xFF84858E),
                        ),
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          Text(
            amountText,
            style: Theme.of(context).textTheme.headline1!.copyWith(
                  fontSize: tS * 15,
                  color: transactionColor,
                ),
          ),
        ],
      ),
    );
  }
}
