// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../common_functions.dart';

class FaqWidgetShimmer extends StatelessWidget {
  FaqWidgetShimmer({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Column(children: [
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          margin: EdgeInsets.only(
              left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
          padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.03),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
      Shimmer.fromColors(
        baseColor: baseColorShimmer,
        highlightColor: highlightColorShimmer,
        child: Container(
          height: dW * 1.25,
          width: dW,
          margin: EdgeInsets.only(
              left: dW * 0.04,
              right: dW * 0.04,
              top: dW * 0.06,
              bottom: dW * 0.3),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: containerDecorationColorShimmer),
        ),
      ),
    ]);
  }
}
