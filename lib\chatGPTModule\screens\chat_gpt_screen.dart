// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, use_build_context_synchronously

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/chatGPTModule/models/chat_gpt_message_model.dart';
import 'package:nine_and_beyond/chatGPTModule/providers/chat_gpt_provider.dart';
import 'package:nine_and_beyond/chatGPTModule/widgets/chat_gpt_message_box.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_button.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/chat_date_time_widget.dart';

class ChatGptScreen extends StatefulWidget {
  final ChatGptScreenArguments args;
  const ChatGptScreen({super.key, required this.args});

  @override
  State<ChatGptScreen> createState() => _ChatGptScreenState();
}

class _ChatGptScreenState extends State<ChatGptScreen> {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  bool isLoading = false;
  late User user;

  double keyboardHeight = 0;
  bool isFetching = false;
  bool isLazyLoading = false;
  bool isSending = false;
  bool noBalance = false;

  List<ChatGPTMessage> messages = [];
  List chatGptMsgsArray = [];
  String sessionId = '';

  late ScrollController _scrollController;
  late TextEditingController _chatController;
  late FocusNode _chatFocusNode;
  // Timer? longPullTimer;

  scrollDown() => Future.delayed(const Duration(milliseconds: 0), () {
        _scrollController.animateTo(_scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 100), curve: Curves.easeIn);
      });

  createMessage([String? prompt]) async {
    try {
      if (isSending || (prompt == null && _chatController.text.isEmpty)) {
        return;
      }

      if (prompt != null) {
        prompt = prompt.replaceAll('“', '');
        prompt = prompt.replaceAll('”', '');
      }

      String question = prompt ?? _chatController.text.trim();
      question = question.replaceAll('', '');
      // let the highlighted apostrophe and double quotes be like that
      question = question.replaceAll("’", "'");
      question = question.replaceAll("”", '"');

      setState(() {
        isSending = true;
        _chatController.clear();
      });

      Future.delayed(const Duration(milliseconds: 100), scrollDown);

      final response =
          await Provider.of<ChatGPTProvider>(context, listen: false)
              .createMessage(
        accessToken: user.accessToken,
        body: {
          'newMessage': {
            'role': "user",
            'content': question,
          },
          'sessionId': sessionId,
          'messages': json.encode(chatGptMsgsArray)
        },
      );

      setState(() => isSending = false);

      if (response['success']) {
        chatGptMsgsArray = response['result']['messages'];
        Provider.of<ChatGPTProvider>(context, listen: false).currentSessionId =
            sessionId = response['result']['sessionId'];
        Provider.of<AuthProvider>(context, listen: false)
            .updateCredits(response['result']['gptCredits']);
      } else {
        showSnackbar(language[response['message']]);
        if (response['message'] == 'insfcntBal' && response['result'] != null) {
          Provider.of<AuthProvider>(context, listen: false)
              .updateCredits(response['result']['gptCredits'] ?? 0);
        }
      }
      scrollDown();

      //
    } finally {
      setState(() => isSending = false);
    }
  }

  confirmNewChatDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialogBox(
            title: language['confirmNewChat'],
            firstButton: DialogTextButton(
              onPressed: () {
                Provider.of<ChatGPTProvider>(context, listen: false)
                    .clearMessages(true);
                sessionId = '';
                chatGptMsgsArray = [];
                pop();
              },
              text: language['startNew'],
            ),
            secondButton: FilledDialogButton(
              onPressed: pop,
              text: language['continue'],
            ),
          )),
    );
  }

  fetchSession([refresh = false]) async {
    setState(() => isLoading = true);
    String query = '?';
    if (refresh) {
      query += 'sessionId=$sessionId';
    } else if (widget.args.session != null) {
      query += 'sessionId=${widget.args.session}';
    }
    final response =
        await Provider.of<ChatGPTProvider>(context, listen: false).fetchSession(
      query: query,
      accessToken: user.accessToken,
    );
    setState(() => isLoading = false);

    if (response['success']) {
      if (response['result'] != null) {
        Provider.of<ChatGPTProvider>(context, listen: false).currentSessionId =
            sessionId = response['result']['sessionId'] ?? '';

        chatGptMsgsArray = response['result']['chatGptMsgsArray'];
      }
    } else {
      showSnackbar(language[response['message']]);
    }

    Future.delayed(const Duration(milliseconds: 200), () {
      _scrollController.animateTo(_scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300), curve: Curves.easeIn);
    });

    // if (widget.args.session == null && sessionId != '' && user.gptCredits > 0) {
    //   Future.delayed(const Duration(milliseconds: 200), confirmNewChatDialog);
    // }
  }

  List<Widget> get noBalanceWidgets => [
        SizedBox(height: dH * 0.1),
        Image.asset(
          'assets/images/no_balance.png',
          width: dW * 0.55,
        ),
        Container(
          margin: EdgeInsets.only(
            top: dW * 0.11,
            left: dW * horizontalPaddingFactor,
            right: dW * horizontalPaddingFactor,
          ),
          padding: EdgeInsets.symmetric(
            horizontal: dW * 0.035,
            vertical: dW * 0.035,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFFDEEF2),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              width: 1,
              color: const Color(0xFFFCD7E1),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  const AssetSvgIcon('info'),
                  SizedBox(width: dW * 0.015),
                  Text(
                    language['insfBalInWal'],
                    style: Theme.of(context).textTheme.headline2!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0xFFCD1617),
                        ),
                  ),
                ],
              ),
              SizedBox(height: dW * 0.03),
              Text(
                language['noBalDesc'],
                style: Theme.of(context).textTheme.headline3!.copyWith(
                      height: 1.9,
                      fontSize: tS * 10,
                      color: const Color(0xFF6B6C75),
                    ),
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(
            top: dW * 0.065,
            left: dW * horizontalPaddingFactor,
            right: dW * horizontalPaddingFactor,
          ),
          height: dW * 0.14,
          child: CustomGradientButton(
            buttonText: language['addBalance'],
            onPressed: () =>
                push(NamedRoute.rechargeWalletScreen).then((value) async {
              await Provider.of<AuthProvider>(context, listen: false)
                  .refreshUser();
            }),
          ),
        ),
      ];

  @override
  void initState() {
    super.initState();

    Provider.of<ChatGPTProvider>(context, listen: false).clearMessages();
    user = Provider.of<AuthProvider>(context, listen: false).user;
    // chatRoomId = widget.args.chatRoomId;
    fetchSession();

    _scrollController = ScrollController();
    _chatController = TextEditingController();
    _chatFocusNode = FocusNode();

    // fetchInitMessages();
    // _scrollController = ScrollController()..addListener(_scrollListener);
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    user = Provider.of<AuthProvider>(context).user;
    noBalance =
        user.gptCredits == 0 && widget.args.session == null && sessionId == '';

    language = Provider.of<AuthProvider>(context).selectedLanguage;
    messages = Provider.of<ChatGPTProvider>(context).messages;

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(
        dW: dW,
        title: language['chat'],
        dragDown: pop,
        titleWidget: Container(
          margin: EdgeInsets.only(left: dW * 0.025),
          child: Text(
            language['chat'],
            style: Theme.of(context).textTheme.headline1!.copyWith(
                  color: lightBlack,
                  fontSize: 13,
                ),
          ),
        ),
        leadingWidth: dW * 0.12,
        leading: GestureDetector(
          onTap: pop,
          child: Container(
            alignment: Alignment.centerRight,
            decoration: BoxDecoration(
              color: transparentColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.clear_rounded,
              size: 24,
              color: blackColor,
            ),
          ),
        ),
        actions: [
          GestureDetector(
            onTap: () => push(NamedRoute.walletScreen),
            child: Container(
              margin: EdgeInsets.only(
                top: 12,
                bottom: 12,
                right: dW * 0.04,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: dW * 0.02,
                vertical: dW * 0.01,
              ),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFD6EAFF), width: 1),
                borderRadius: BorderRadius.circular(2),
                color: transparentColor,
              ),
              child: Row(
                children: [
                  Image.asset(
                    'assets/images/credit.png',
                    width: 14,
                    height: 14,
                  ),
                  SizedBox(width: dW * 0.015),
                  Text(
                    convertAmountString(user.gptCredits.toDouble()),
                    style: Theme.of(context).textTheme.headline2!.copyWith(
                          fontSize: tS * 9,
                          color: blackColor,
                        ),
                  ),
                ],
              ),
            ),
          ),
          Theme(
            data: Theme.of(context).copyWith(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
            ),
            child: PopupMenuButton(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.zero,
              itemBuilder: (BuildContext bc) => [
                if (user.gptCredits > 0)
                  popupMenuItem(
                    position: 1,
                    title: language['newChat'],
                    icon: 'add',
                    dW: dW,
                  ),
                popupMenuItem(
                  position: 2,
                  title: language['chatHistory'],
                  icon: 'chat_history',
                  dW: dW,
                ),
              ],
              onSelected: (value) {
                if (value == 1) {
                  confirmNewChatDialog();
                } else if (value == 2) {
                  if (widget.args.session != null) {
                    pop();
                  } else {
                    push(NamedRoute.chatGptHistoryScreen).then((value) {
                      if (Provider.of<ChatGPTProvider>(context, listen: false)
                                  .currentSessionId !=
                              sessionId &&
                          sessionId != '') {
                        fetchSession(true);
                      }
                    });
                  }
                }
              },
              child: const Icon(Icons.more_vert_rounded, size: 22),
            ),
          ),
          SizedBox(width: dW * 0.06),
        ],
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Container(
          //   width: dW,
          //   decoration: BoxDecoration(color: white, boxShadow: [
          //     BoxShadow(
          //       color: const Color(0xFF975EFF).withOpacity(0.08),
          //       blurRadius: 8,
          //       spreadRadius: 0,
          //       offset: const Offset(0, 4),
          //     ),
          //   ]),
          //   padding: EdgeInsets.only(
          //     left: dW * horizontalPaddingFactor,
          //     right: dW * horizontalPaddingFactor,
          //     top: dW * (iOSCondition(dH) ? 0.01 : 0.03),
          //     bottom: dW * 0.04,
          //   ),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.start,
          //     children: [
          //       // if (widget.args.session != null)
          //       GestureDetector(
          //         onTap: pop,
          //         child: Container(
          //           decoration: BoxDecoration(
          //             color: transparentColor,
          //             borderRadius: BorderRadius.circular(12),
          //           ),
          //           padding: EdgeInsets.only(
          //             top: 0,
          //             right: dW * 0.02,
          //           ),
          //           child: const Icon(
          //             Icons.clear_rounded,
          //             size: 24,
          //             color: blackColor,
          //           ),
          //         ),
          //       ),
          //       Container(
          //         color: Colors.transparent,
          //         child: Text(
          //           language['chat'],
          //           style: textTheme.headline1!.copyWith(
          //             fontSize: tS * 12,
          //             color: lightBlack,
          //           ),
          //         ),
          //       ),
          //       const Spacer(),
          //       GestureDetector(
          //         onTap: () => push(NamedRoute.walletScreen),
          //         child: Container(
          //           margin: EdgeInsets.only(right: dW * 0.04),
          //           padding: EdgeInsets.symmetric(
          //             horizontal: dW * 0.02,
          //             vertical: dW * 0.01,
          //           ),
          //           decoration: BoxDecoration(
          //             border:
          //                 Border.all(color: const Color(0xFFD6EAFF), width: 1),
          //             borderRadius: BorderRadius.circular(2),
          //             color: transparentColor,
          //           ),
          //           child: Row(
          //             children: [
          //               Image.asset(
          //                 'assets/images/credit.png',
          //                 width: 14,
          //                 height: 14,
          //               ),
          //               SizedBox(width: dW * 0.015),
          //               Text(
          //                 convertAmountString(user.gptCredits.toDouble()),
          //                 style:
          //                     Theme.of(context).textTheme.headline2!.copyWith(
          //                           fontSize: tS * 9,
          //                           color: blackColor,
          //                         ),
          //               ),
          //             ],
          //           ),
          //         ),
          //       ),
          //       Theme(
          //         data: Theme.of(context).copyWith(
          //           highlightColor: Colors.transparent,
          //           splashColor: Colors.transparent,
          //         ),
          //         child: PopupMenuButton(
          //           shape: RoundedRectangleBorder(
          //             borderRadius: BorderRadius.circular(10),
          //           ),
          //           padding: EdgeInsets.zero,
          //           itemBuilder: (BuildContext bc) => [
          //             if (user.gptCredits > 0)
          //               popupMenuItem(
          //                 position: 1,
          //                 title: language['newChat'],
          //                 icon: 'add',
          //                 dW: dW,
          //               ),
          //             popupMenuItem(
          //               position: 2,
          //               title: language['chatHistory'],
          //               icon: 'chat_history',
          //               dW: dW,
          //             ),
          //           ],
          //           onSelected: (value) {
          //             if (value == 1) {
          //               confirmNewChatDialog();
          //             } else if (value == 2) {
          //               if (widget.args.session != null) {
          //                 pop();
          //               } else {
          //                 push(NamedRoute.chatGptHistoryScreen).then((value) {
          //                   if (Provider.of<ChatGPTProvider>(context,
          //                                   listen: false)
          //                               .currentSessionId !=
          //                           sessionId &&
          //                       sessionId != '') {
          //                     fetchSession(true);
          //                   }
          //                 });
          //               }
          //             }
          //           },
          //           child: const Icon(Icons.more_vert_rounded, size: 22),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          if (isLoading)
            Padding(
              padding: EdgeInsets.only(top: dH * 0.2),
              child: const CircularLoader(),
            ),
          if (!isLoading) ...[
            Expanded(
              child: SingleChildScrollView(
                controller: _scrollController,
                physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                child: Column(
                  children: noBalance
                      ? noBalanceWidgets
                      : messages.isNotEmpty
                          ? [
                              ListView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.04),
                                  itemCount: messages.length,
                                  itemBuilder: ((context, i) => Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          if (i == 0 ||
                                              !isSameDay(
                                                  messages[i - 1].createdAt,
                                                  messages[i].createdAt))
                                            ChatDateTime(
                                              messages[i].createdAt,
                                              isFirst: i == 0,
                                            ),
                                          ChatGPTMessageBox(
                                            key: ValueKey(messages[i].id),
                                            message: messages[i],
                                            isContinued: i != 0 &&
                                                messages[i].role ==
                                                    messages[i - 1].role,
                                          ),
                                        ],
                                      ))),
                              if (isSending)
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      margin: EdgeInsets.only(
                                        top: dW * 0.05,
                                        left: dW * 0.04,
                                      ),
                                      padding: EdgeInsets.only(
                                        left: dW * 0.025,
                                        right: dW * 0.025,
                                        top: dW * 0.02,
                                        bottom: dW * 0.03,
                                      ),
                                      decoration: const BoxDecoration(
                                          color: Color(0xFFF7F8FC),
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(12),
                                              topRight: Radius.circular(12),
                                              bottomLeft: Radius.zero,
                                              bottomRight:
                                                  Radius.circular(12))),
                                      constraints: BoxConstraints(
                                        minWidth: dW * 0.12,
                                        maxWidth: dW * 0.85,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(' ...',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .headline3!
                                                  .copyWith(
                                                    color: lightBlack,
                                                    fontSize: tS * 20,
                                                    // height: 1.6,
                                                  )),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              SizedBox(height: dW * 0.05),
                            ]
                          : [ChatFAQs(createMessage: createMessage)],
                ),
              ),
            ),
            ChatTextField(
              chatController: _chatController,
              chatFocusNode: _chatFocusNode,
              scrollDown: scrollDown,
              keyboardHeight: keyboardHeight,
              createMessage: createMessage,
            ),
          ],
        ],
      ),
    );
  }
}

class ChatTextField extends StatefulWidget {
  final TextEditingController chatController;
  final FocusNode chatFocusNode;
  final Function scrollDown;
  final double keyboardHeight;
  final Function createMessage;

  const ChatTextField({
    super.key,
    required this.chatController,
    required this.chatFocusNode,
    required this.scrollDown,
    required this.keyboardHeight,
    required this.createMessage,
  });

  @override
  State<ChatTextField> createState() => _ChatTextFieldState();
}

class _ChatTextFieldState extends State<ChatTextField> {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final User user = Provider.of<AuthProvider>(context).user;
    bool isEnabled = user.gptCredits > 0;
    // bool isEnabled = true;

    return Container(
        width: dW,
        padding: EdgeInsets.only(
          top: dW * 0.042,
          bottom: dW *
              (widget.keyboardHeight != 0
                  ? 0.02
                  : iOSCondition(dH)
                      ? 0.1
                      : 0.07),
          left: dW * 0.04,
          right: dW * 0.04,
        ),
        color: isEnabled ? const Color(0xFFF7F8FC) : const Color(0xFFF4F4F4),
        child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: isEnabled ? white : const Color(0xFFF4F4F4),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                        width: 0.6,
                        color:
                            isEnabled ? lightBlack : const Color(0xFFBFC0C8)),
                  ),
                  constraints: BoxConstraints(
                    maxHeight: dW * 0.285,
                    minHeight: dW * 0.125,
                  ),
                  child: TextFormField(
                    maxLines: null,
                    enabled: isEnabled,
                    controller: widget.chatController,
                    focusNode: widget.chatFocusNode,
                    onTap: () => Future.delayed(
                        const Duration(milliseconds: 400),
                        () => widget.scrollDown()),
                    cursorColor: Theme.of(context).primaryColor,
                    textInputAction: TextInputAction.done,
                    textCapitalization: TextCapitalization.sentences,
                    decoration: InputDecoration(
                      hintText: language['askAnything'],
                      hintStyle:
                          Theme.of(context).textTheme.headline3!.copyWith(
                                color: const Color(0xFF636363),
                                fontSize: tS * 12,
                              ),
                      contentPadding: EdgeInsets.only(
                        top: dW * 0.05,
                        bottom: dW * 0.02,
                        left: dW * 0.03,
                        right: dW * 0.03,
                      ),
                      enabledBorder: chatFieldBorder,
                      disabledBorder: chatFieldBorder,
                      focusedBorder: chatFieldBorder,
                      border: chatFieldBorder,
                    ),
                    style: Theme.of(context).textTheme.headline3!.copyWith(
                          fontSize: tS * 12.5,
                          letterSpacing: .3,
                          color: lightBlack,
                        ),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => widget.createMessage(),
                child: Container(
                  width: dW * 0.127,
                  height: dW * 0.127,
                  margin: EdgeInsets.only(left: dW * 0.035),
                  decoration: BoxDecoration(
                    color: isEnabled ? lightBlack : const Color(0xFFAAABB5),
                    gradient: isEnabled ? linearGradient : null,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: shadow,
                  ),
                  child: const Center(
                    child: AssetSvgIcon('send_message_icon'),
                  ),
                ),
              ),
            ]));
  }
}

class ChatFAQs extends StatefulWidget {
  final Function createMessage;
  const ChatFAQs({super.key, required this.createMessage});

  @override
  State<ChatFAQs> createState() => _ChatFAQsState();
}

class _ChatFAQsState extends State<ChatFAQs> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      margin: EdgeInsets.only(top: dW * 0.1),
      width: dW,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            language['fAQs:'],
            style: Theme.of(context).textTheme.headline3!.copyWith(
                  fontSize: tS * 14,
                  color: const Color(0xFF37383F),
                ),
          ),
          SizedBox(height: dW * 0.04),
          ...Provider.of<AuthProvider>(context)
              .chatFAQs
              .map((faq) => GestureDetector(
                    onTap: () => widget.createMessage(language[faq]),
                    child: Container(
                      width: dW * 0.9,
                      alignment: Alignment.centerLeft,
                      margin: EdgeInsets.only(bottom: dW * 0.02),
                      padding: const EdgeInsets.symmetric(
                        vertical: 16,
                        horizontal: 9,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: const Color(0xFFF7F8FC),
                      ),
                      child: Text(
                        language[faq] + "   -->",
                        style: Theme.of(context).textTheme.headline3!.copyWith(
                              fontSize: tS * 12,
                              color: const Color(0xFF6B6C75),
                            ),
                      ),
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }
}
