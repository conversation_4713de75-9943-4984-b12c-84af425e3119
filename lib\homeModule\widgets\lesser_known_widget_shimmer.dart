// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../common_functions.dart';

class LesserKnownWidgetShimmer extends StatelessWidget {
  LesserKnownWidgetShimmer({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Shimmer.fromColors(
      baseColor: baseColorShimmer,
      highlightColor: highlightColorShimmer,
      child: Container(
        width: dW,
        height: dW * 1.2,
        margin:
            EdgeInsets.only(left: dW * 0.04, right: dW * 0.04, top: dW * 0.06),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: containerDecorationColorShimmer),
      ),
    );
  }
}
