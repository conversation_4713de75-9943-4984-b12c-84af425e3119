// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';

class YogaWidget extends StatefulWidget {
  final Yoga yoga;
  final int? selectedTrimester;
  const YogaWidget({super.key, required this.yoga, this.selectedTrimester});

  @override
  State<YogaWidget> createState() => _YogaWidgetState();
}

class _YogaWidgetState extends State<YogaWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final yoga =
        Provider.of<DailyActivityProvider>(context, listen: false).yoga;

    return Padding(
      padding: EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              if (widget.yoga.video.isNotEmpty) {
                push(
                  NamedRoute.yogaDetailsScreen,
                  arguments: YogaDetailsArguments(
                      trimester: widget.selectedTrimester.toString(),
                      yoga: yoga,
                      selectedYoga: widget.yoga),
                );
              } else {
                buySubscriptionDialog();
              }
            },
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: CachedImageWidget(
                    widget.yoga.thumbnail,
                    boxFit: BoxFit.cover,
                    width: dW,
                    height: dW * 0.5,
                  ),
                ),
                Positioned(
                  top: dW * 0.175,
                  left: 0,
                  right: dW * 0.03,
                  child: Column(
                    children: [
                      const AssetSvgIcon('play_in_circle_yoga'),
                      SizedBox(height: dW * 0.02),
                      Text(
                        language['start'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFFFFFFFF),
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.yoga.video.isEmpty)
                  Positioned(
                    top: dW * 0.03,
                    right: dW * 0.03,
                    child: const AssetSvgIcon(
                      's_lock',
                    ),
                  ),
                // Positioned(
                //     top: dW * 0.03,
                //     left: dW * 0.65,
                //     right: dW * 0.03,
                //     child: Container(
                //       height: dW * 0.06,
                //       decoration: BoxDecoration(
                //           color: const Color(0XFF767676).withOpacity(0.5),
                //           borderRadius: BorderRadius.circular(4)),
                //       child: Row(
                //         mainAxisAlignment: MainAxisAlignment.center,
                //         children: [
                //           const AssetSvgIcon('views'),
                //           SizedBox(width: dW * 0.01),
                //           Text(
                //             widget.yoga.views,
                //             style: textTheme.headline2!.copyWith(
                //               fontSize: tS * 10,
                //               color: const Color(0XFFFFFFFF),
                //             ),
                //           ),
                //           SizedBox(width: dW * 0.005),
                //           Text(
                //             language['views'],
                //             style: textTheme.headline2!.copyWith(
                //               fontSize: tS * 10,
                //               color: const Color(0XFFFFFFFF),
                //             ),
                //           ),
                //         ],
                //       ),
                //     ))
              ],
            ),
          ),
          SizedBox(height: dW * 0.04),
          Text(
            widget.yoga.name,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 14,
              color: const Color(0XFF1D1E22),
            ),
          ),
          SizedBox(height: dW * 0.025),
          Text(
            widget.yoga.duration,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 12,
              color: const Color(0XFF6B6C75),
            ),
          ),
          SizedBox(height: dW * 0.07),
        ],
      ),
    );
  }
}
