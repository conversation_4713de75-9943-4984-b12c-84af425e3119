// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:nine_and_beyond/commonWidgets/custom_text_field.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';

import '../../colors.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class RegisterUserScreen extends StatefulWidget {
  final RegistrationArguments args;
  const RegisterUserScreen({super.key, required this.args});

  @override
  State<RegisterUserScreen> createState() => _RegisterUserScreenState();
}

class _RegisterUserScreenState extends State<RegisterUserScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  bool isLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController fullNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  String deviceBrand = '';
  String deviceModel = '';
  String osVersion = '';
  String osType = '';

  getDeviceDetails() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceBrand = androidInfo.brand!;
      deviceModel = androidInfo.model!;
      osVersion = androidInfo.version.release!;
      osType = 'Android';
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceBrand = 'Apple';
      deviceModel = iosInfo.utsname.machine!;
      osVersion = iosInfo.systemVersion!;
      osType = 'IOS';
    }
  }

  // void _signUp() async {
  //   final isValid = _formKey.currentState!.validate();

  //   if (!isValid) {
  //     return;
  //   }
  //   _formKey.currentState!.save();

  //   push(
  //     NamedRoute.personalizePromptScreen,
  //     arguments: PersonalizePromptArguments(),
  //   );

  //   // final user =User(fullName: fullName, phone: phone, role: role, avatar: avatar, isActive: isActive, accessToken: accessToken)
  //   // final response = await Provider.of<AuthProvider>(context, listen: false)
  //   //     .createUser(user);
  //   // if (response['success']) {
  //   //   Navigator.of(context).pushAndRemoveUntil(
  //   //       MaterialPageRoute(
  //   //         builder: (ctx) => const HomeScreen(),
  //   //       ),
  //   //       (route) => false);
  //   // } else {
  //   //   showSnackbar(response['message'], Colors.red);
  //   // }
  // }

  void updateValidity() {
    setState(() {});
  }

  register() async {
    bool isValid = _formKey.currentState!.validate();

    if (!isValid) {
      setState(() {});
      return;
    }

    setState(() => isLoading = true);

    final Map<String, String> body = {
      "fullName": fullNameController.text.trim(),
      "email": emailController.text.trim(),
      "phone": widget.args.mobileNo,
      "avatar": '',
      "deviceBrand": deviceBrand,
      "deviceModel": deviceModel,
      "osVersion": osVersion,
      "osType": osType,
    };

    final Map<String, String> files = {};

    final response =
        await Provider.of<AuthProvider>(context, listen: false).register(
      body: body,
      files: files,
    );
    setState(() => isLoading = false);

    if (!response['success']) {
      showSnackbar(language[response['message']]);
    } else {
      pushAndRemoveUntil(NamedRoute.personalizePromptScreen,
          arguments: PersonalizePromptArguments());
    }
  }

  @override
  void initState() {
    super.initState();

    fullNameController.addListener(updateValidity);
    emailController.addListener(updateValidity);
    getDeviceDetails();
  }

  @override
  void dispose() {
    super.dispose();

    fullNameController.dispose();
    emailController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    bool valid = fullNameController.text.isNotEmpty &&
        emailController.text.isNotEmpty &&
        RegExp(r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$')
            .hasMatch(emailController.text);

    return Scaffold(
        backgroundColor: white,
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                              left: 16, right: 16, top: dW * 0.1),
                          child: Text(
                            language['makeProfile'],
                            style:
                                Theme.of(context).textTheme.headline3!.copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0XFF6B6C75),
                                    ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: 16, right: 16, top: dW * 0.03),
                          child: Text(
                            language['personalInfo'],
                            style:
                                Theme.of(context).textTheme.headline1!.copyWith(
                                      fontSize: tS * 16,
                                      color: const Color(0XFF1D1E22),
                                    ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: 16,
                              right: 16,
                              top: dW * 0.07,
                              bottom: dW * 0.06),
                          child: Image.asset(
                            'assets/images/register_user_screen_graphic.png',
                            height: 123.3,
                            width: 146,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: 16, right: 16, bottom: dW * 0.05),
                          child: CustomTextFieldWithLabel(
                            label: language['fullName'],
                            hintText: language['enterFullName'],
                            controller: fullNameController,
                            borderColor: greyBorderColor,
                            textCapitalization: TextCapitalization.words,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return language['pleaseEnterFullName'];
                              }
                              return null;
                            },
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              left: 16, right: 16, bottom: dW * 0.05),
                          child: CustomTextFieldWithLabel(
                            label: language['emailId'],
                            hintText: language['enterEmail'],
                            controller: emailController,
                            borderColor: greyBorderColor,
                            inputType: TextInputType.emailAddress,
                            textCapitalization: TextCapitalization.none,
                            // textInputType: TextInputType.emailAddress,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return language['pleaseEnterEmail'];
                              } else if (!RegExp(
                                      r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$')
                                  .hasMatch(value)) {
                                return language['pleaseEnterValidEmail'];
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
                child: Stack(
                  children: [
                    GradientButton(
                      elevation: valid ? 2 : 0,
                      isLoading: isLoading,
                      onPressed: valid ? register : () {},
                      buttonText: language['saveAndContinue'],
                    ),
                    if (!valid)
                      Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          top: 0,
                          child: Container(
                            color: Colors.white.withOpacity(.7),
                          )),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
