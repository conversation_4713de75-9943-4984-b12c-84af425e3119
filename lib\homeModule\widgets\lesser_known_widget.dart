// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../common_functions.dart';
import '../../navigation/routes.dart';
import '../models/article_model.dart';

class LesserKnownWidget extends StatelessWidget {
  List<Facts> lesserKnowns;
  LesserKnownWidget({super.key, required this.lesserKnowns});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return GestureDetector(
      onTap: () {
        push(NamedRoute.lesserKnownDetailScreen,
            arguments:
                LesserKnownDetailScreenArguments(lesserKnown: lesserKnowns));
      },
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedImageWidget(
              lesserKnowns.first.imageUrl,
              boxFit: BoxFit.cover,
              width: dW,
              height: dW * 1.2,
            ),
          ),
          Positioned(
            right: 0,
            top: dW * 0.05,
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: dW * 0.09),
                child: const AssetSvgIcon('phone_shake')),
          ),
          Positioned(
            left: dW * 0.01,
            right: 0,
            bottom: dW * 0.15,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: dW * 0.1),
              child: Text(
                lesserKnowns.first.title,
                style: textTheme.headline1!.copyWith(
                  fontSize: tS * 18,
                  color: const Color(0XFFFFFFFF),
                ),
              ),
            ),
          ),
          Positioned(
            left: dW * 0.01,
            right: 0,
            bottom: dW * 0.07,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: dW * 0.1),
              child: Text(
                language['readMore'],
                style: textTheme.headline2!.copyWith(
                  fontSize: tS * 12,
                  color: const Color(0XFFF37177),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
