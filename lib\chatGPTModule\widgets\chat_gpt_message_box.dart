// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/chatGPTModule/models/chat_gpt_message_model.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';

class ChatGPTMessageBox extends StatefulWidget {
  final ChatGPTMessage message;
  final bool isContinued;

  const ChatGPTMessageBox({
    super.key,
    required this.message,
    required this.isContinued,
  });

  @override
  State<ChatGPTMessageBox> createState() => _ChatGPTMessageBoxState();
}

class _ChatGPTMessageBoxState extends State<ChatGPTMessageBox> {
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  Map language = {};

  late User user;

  Duration currentPos = Duration.zero;
  Duration duration = Duration.zero;

  bool isDownloading = false;
  bool isMyMessage = false;

  double radius = 12;

  @override
  void initState() {
    super.initState();

    // user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    isMyMessage = widget.message.role == 'user';

    return Row(
      mainAxisAlignment:
          isMyMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(top: dW * (widget.isContinued ? 0.02 : 0.05)),
          padding: EdgeInsets.only(
            left: dW * 0.025,
            right: dW * 0.025,
            top: dW * 0.025,
            bottom: dW * 0.033,
          ),
          decoration: BoxDecoration(
              color: isMyMessage
                  ? const Color(0xFFD6EAFF)
                  : const Color(0xFFF7F8FC),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(radius),
                  topRight: Radius.circular(radius),
                  bottomLeft:
                      isMyMessage ? Radius.circular(radius) : Radius.zero,
                  bottomRight:
                      isMyMessage ? Radius.zero : Radius.circular(radius))),
          constraints: BoxConstraints(maxWidth: dW * 0.85, minWidth: dW * 0.25),
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: isMyMessage
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                children: [
                  // if (!widget.isContinued && !isMyMessage)
                  //   Container(
                  //     margin: EdgeInsets.only(bottom: dW * 0.01),
                  //     child: Text(
                  //       isMyMessage ? language['you'] : widget.message.sentBy,
                  //       style: Theme.of(context).textTheme.headline2!.copyWith(
                  //             fontSize: tS * 14.5,
                  //             color: const Color(0xFF292929),
                  //           ),
                  //     ),
                  //   ),

                  // if (widget.message.content.type == 'Document')
                  //   GestureDetector(
                  //     onTap: () {
                  //       if (widget.message.isLocal) {
                  //         openLocalDocument(
                  //             context, widget.message.content.url);
                  //       } else {
                  //         openFile(
                  //             url: widget.message.content.url,
                  //             fileName: widget.message.content.docName);
                  //       }
                  //     },
                  //     child: Container(
                  //       decoration: BoxDecoration(
                  //           color: isMyMessage
                  //               ? whiteColor
                  //               : Colors.grey.withOpacity(0.1),
                  //           borderRadius: BorderRadius.circular(12)),
                  //       padding: EdgeInsets.symmetric(
                  //           horizontal: dW * 0.02, vertical: dW * 0.02),
                  //       child: Row(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           Padding(
                  //             padding: EdgeInsets.only(right: dW * 0.015),
                  //             child: SvgPicture.asset(
                  //                 '${svgPath}document_icon.svg',
                  //                 height: dW * 0.05),
                  //           ),
                  //           Expanded(
                  //             child: SizedBox(
                  //               child: Column(
                  //                 children: [
                  //                   Text(widget.message.content.docName),
                  //                 ],
                  //               ),
                  //             ),
                  //           )
                  //         ],
                  //       ),
                  //     ),
                  //   ),

                  Text(widget.message.content,
                      style: Theme.of(context).textTheme.headline3!.copyWith(
                            color: lightBlack,
                            fontSize: tS * 13,
                            height: 1.6,
                          )),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
