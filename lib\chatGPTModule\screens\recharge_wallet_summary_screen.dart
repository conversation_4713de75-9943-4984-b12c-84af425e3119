// ignore_for_file: deprecated_member_use

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/chatGPTModule/providers/chat_gpt_provider.dart';
import 'package:nine_and_beyond/commonWidgets/dotted_seperator.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/routes.dart';
import '../widgets/question_charge_widget.dart';

class RechargeWalletSummaryScreen extends StatefulWidget {
  final RechargeWalletSummaryScreenArguments args;
  const RechargeWalletSummaryScreen({Key? key, required this.args})
      : super(key: key);

  @override
  RechargeWalletSummaryScreenState createState() =>
      RechargeWalletSummaryScreenState();
}

class RechargeWalletSummaryScreenState
    extends State<RechargeWalletSummaryScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  bool isPaying = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  String? signature;

  Map? transaction;

  num tax = 0;
  num discount = 0;
  num grandTotal = 0;

  calculateAndSetData() async {
    tax = 0.18 * widget.args.amount;
    grandTotal = widget.args.amount + tax - discount;
  }

  Widget amountRow(
          {required String title,
          TextStyle? titleStyle,
          required num amount,
          TextStyle? amountStyle,
          bool isNegative = false}) =>
      Container(
        margin: EdgeInsets.only(top: dW * 0.035),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: titleStyle ??
                  Theme.of(context).textTheme.headline3!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0xFF6B6C75),
                      ),
            ),
            Text(
              '${isNegative ? '- ' : ''}\u20b9${convertAmountString(amount.toDouble())}',
              style: amountStyle ??
                  Theme.of(context).textTheme.headline3!.copyWith(
                        fontSize: tS * 14,
                        color: lightBlack,
                      ),
            ),
          ],
        ),
      );

  razorpayBuyProcess() async {
    if (transaction != null) {
      push(NamedRoute.paymentScreen,
          arguments: PaymentScreenArguments(
            orderId: transaction!['rzpOrderId'],
            amount: transaction!['totalAmount'],
            type: 'gptCredits',
            transaction: transaction!,
          ));
    } else {
      setState(() => isPaying = true);
      final response =
          await Provider.of<ChatGPTProvider>(context, listen: false)
              .buyGptCredits(accessToken: user.accessToken, body: {
        'rechargeAmount': widget.args.amount,
        'tax': tax,
        'discount': discount,
        'total': grandTotal,
        'userPhone': user.phone,
      });

      setState(() => isPaying = false);

      if (response['success']) {
        transaction = response['result'];
        push(NamedRoute.paymentScreen,
            arguments: PaymentScreenArguments(
              orderId: transaction!['rzpOrderId'],
              amount: transaction!['totalAmount'],
              type: 'gptCredits',
              transaction: transaction!,
            ));
      }
    }
  }

  phonePeBuyProcess() async {
    setState(() => isPaying = true);
    final response = await Provider.of<ChatGPTProvider>(context, listen: false)
        .buyGptCredits(accessToken: user.accessToken, body: {
      'rechargeAmount': widget.args.amount,
      'tax': tax,
      'discount': discount,
      'total': grandTotal,
      'userPhone': user.phone,
    });

    if (response['success']) {
      transaction = response['result']['transaction'];

      initiatePhonePePayment(
        body: response['result']['encodedBody'],
        checksum: response['result']['checksum'],
        apiEndPoint: response['result']['apiEndPoint'],
        headers: Map<String, String>.from(response['result']['headers']),
        callBackURL: response['result']['callbackUrl'],
        packageName: Platform.isIOS ? null : 'com.nineandbeyond.app',
        // response['result']['packageName'],
        merchantId: response['result']['merchantId'],
        env: response['result']['env'],
        appId: response['result']['appId'],
      );
    }
  }

  handlePhonePePaymentError(error) {
    showSnackbar('Payment failed--0-0-0-0-\n$error');
    updateTransactionStatusToFailed();
  }

  updateTransactionStatusToFailed() async {
    if (transaction != null) {
      // setState(() => isPaying = true);
      final response =
          await Provider.of<ChatGPTProvider>(context, listen: false)
              .updateTransactionStatusToFailed(
        accessToken: user.accessToken,
        transactionId: transaction!['_id'],
      );
      // setState(() => isPaying = false);
    }
  }

  initiatePhonePePayment({
    required String body,
    required String callBackURL,
    required String checksum,
    required Map<String, String> headers,
    required String apiEndPoint,
    String? packageName,
    required String merchantId,
    required String env,
    required String appId,
  }) async {
    final isInitialized =
        await PhonePePaymentSdk.init(env, appId, merchantId, true);

    if (!isInitialized) {
      showSnackbar('Failed to initiate payment');
      return;
    } else {
      // signature = await PhonePePaymentSdk.getPackageSignatureForAndroid();
      // setState(() {});

      try {
        var paymentResponse;
        var response = PhonePePaymentSdk.startPGTransaction(
            body, callBackURL, checksum, headers, apiEndPoint, packageName);
        response
            .then((val) => {
                  setState(() {
                    paymentResponse = val;
                    if (paymentResponse['status'] == 'SUCCESS') {
                      handleSuccess();
                    } else {
                      setState(() => isPaying = false);
                      updateTransactionStatusToFailed();
                    }
                  })
                })
            .catchError((error) {
          setState(() => isPaying = false);
          handlePhonePePaymentError(error);
          return <dynamic>{};
        });
      } catch (error) {
        setState(() => isPaying = false);
        handlePhonePePaymentError(error);
      }
    }
  }

  handleSuccess() async {
    await Provider.of<AuthProvider>(context, listen: false).refreshUser();
    setState(() => isPaying = false);
    pop();
    pop(true);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    calculateAndSetData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return WillPopScope(
      onWillPop: () async {
        return !isPaying;
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: '',
          dW: dW,
          actionMethod: () {
            if (!isPaying) {
              pop();
            }
          },
        ),
        body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
      ),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: screenHorizontalPadding(dW),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        SizedBox(height: dW * 0.05),
                        QuestionChargeWidget(),
                        Container(
                          margin: EdgeInsets.only(top: dW * 0.05),
                          padding: EdgeInsets.only(
                            top: dW * 0.04,
                            bottom: dW * 0.08,
                            left: dW * horizontalPaddingFactor,
                            right: dW * horizontalPaddingFactor,
                          ),
                          decoration: BoxDecoration(
                            color: white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Text(
                                language['subtotal'],
                                style: Theme.of(context)
                                    .textTheme
                                    .headline2!
                                    .copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0xFF84858E),
                                    ),
                              ),
                              SizedBox(height: dW * 0.015),
                              amountRow(
                                  title: language['rchrgeAmt'],
                                  amount: widget.args.amount),
                              amountRow(title: language['tax'], amount: tax),
                              amountRow(
                                  title: language['discount'],
                                  isNegative: true,
                                  amount: discount,
                                  amountStyle: textTheme.headline2!.copyWith(
                                    color: const Color(0xFF72CD95),
                                    fontSize: tS * 14,
                                  )),
                              Padding(
                                padding: EdgeInsets.only(top: dW * 0.05),
                                child: const DottedSeperator(
                                  height: 0.4,
                                  width: 3,
                                  color: Color(0xFF84858E),
                                ),
                              ),
                              amountRow(
                                  title: language['grandTotal'],
                                  titleStyle: textTheme.headline2!.copyWith(
                                    color: lightBlack,
                                    fontSize: tS * 14,
                                  ),
                                  amount: grandTotal,
                                  amountStyle: textTheme.headline1!.copyWith(
                                    color: lightBlack,
                                    fontWeight: FontWeight.w700,
                                    fontSize: tS * 14,
                                  )),
                              // SelectableText(signature.toString()),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                BottomAlignedWidget(
                    dW: dW,
                    dH: dH,
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.02),
                      child: SizedBox(
                        height: dW * 0.14,
                        child: CustomGradientButton(
                          isLoading: isPaying,
                          buttonText: language['pay'] +
                              ' \u20B9' +
                              convertAmountString(grandTotal.toDouble()),
                          // onPressed: phonePeBuyProcess,
                          onPressed: razorpayBuyProcess,
                        ),
                      ),
                    ))
              ],
            ),
    );
  }
}
