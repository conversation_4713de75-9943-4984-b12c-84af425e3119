// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/commonWidgets/custom_text_field.dart';
import 'package:nine_and_beyond/diaryModule/providers/diary_provider.dart';
import 'package:provider/provider.dart';
import '../../../authModule/providers/auth_provider.dart';
import '../../authModule/model/user_model.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/diary_model.dart';

class RecordingWidget extends StatefulWidget {
  final Recording recording;
  const RecordingWidget({super.key, required this.recording});

  @override
  State<RecordingWidget> createState() => _RecordingWidgetState();
}

class _RecordingWidgetState extends State<RecordingWidget> {
  TextEditingController renameController = TextEditingController();

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  bool isLoading = false;
  late User user;

  TextTheme get textTheme => Theme.of(bContext).textTheme;

  deleteDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialogBox(
            title: '${language['areYouSure']} “${widget.recording.name}” ?',
            firstButton: DialogTextButton(
              onPressed: () {
                pop();
              },
              text: language['cancel'],
            ),
            secondButton: FilledDialogButton(
              onPressed: () {
                updateRecording(deleteRecording: true);
              },
              text: language['yesDelete'],
            ),
          )),
    );
  }

  renameDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  language['renameAudio'],
                  style: textTheme.headline2!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0xFF1D1E22),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: dW * 0.03),
                CustomTextFieldWithLabel(
                  hintText: language['enterAudioName'],
                  controller: renameController,
                  borderColor: greyBorderColor,
                ),
                SizedBox(height: dW * 0.09),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    DialogTextButton(
                      onPressed: () {
                        pop();
                      },
                      text: language['cancel'],
                    ),
                    SizedBox(width: dW * 0.08),
                    FilledDialogButton(
                      onPressed: () {
                        if (renameController.text.trim().isEmpty) {
                          showSnackbar(language['pleaseEnterAudioName']);
                        } else {
                          updateRecording(deleteRecording: false);
                        }
                      },
                      text: language['rename'],
                    ),
                  ],
                )
              ],
            ),
          )),
    );
  }

  updateRecording({bool deleteRecording = false}) async {
    setState(() {
      isLoading = true;
    });
    final response = await Provider.of<DiaryProvider>(context, listen: false)
        .updateRecording(accessToken: user.accessToken, body: {
      'recordingId': widget.recording.id,
      'name': renameController.text,
      'deleteRecording': deleteRecording,
    });

    setState(() {
      isLoading = false;
    });
    if (response['success']) {
      pop();
    } else {
      showSnackbar(response['message']);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  void dispose() {
    super.dispose();

    renameController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final recordings =
        Provider.of<DiaryProvider>(context, listen: false).recordings;

    return GestureDetector(
      onTap: () {
        push(
          NamedRoute.playAudioScreen,
          arguments: PlayAudioArguments(
            recordings: recordings,
            currentIndex: recordings.indexOf(widget.recording),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12, left: 12, right: 12),
        height: dW * 0.2,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: const Color(0XFF000000).withOpacity(0.1),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
          color: const Color(0XFF000000).withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 5, right: 10),
          child: Row(
            children: [
              Container(
                height: dW * 0.17,
                width: dW * 0.17,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: const Color(0xffF7F8FC).withOpacity(0.4),
                ),
                child: const AssetSvgIcon(
                  'play_in_circle',
                ),
              ),
              const SizedBox(width: 10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    DateFormat('d MMM, yy').format(widget.recording.createdAt),
                    style: textTheme.headline3!.copyWith(
                      fontSize: tS * 10,
                      color: const Color(0xFFF4F4F4),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: dW * 0.015),
                  Text(
                    widget.recording.name,
                    style: textTheme.headline1!.copyWith(
                      fontSize: tS * 12,
                      color: const Color(0xFFF4F4F4),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: dW * 0.015),
                  Row(
                    children: [
                      Text(
                        widget.recording.duration,
                        style: textTheme.headline3!.copyWith(
                          fontSize: tS * 8,
                          color: const Color(0xFFDBDBE3),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: dW * 0.01),
                      Text(
                        language['minC'],
                        style: textTheme.headline3!.copyWith(
                          fontSize: tS * 8,
                          color: const Color(0xFFDBDBE3),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const Spacer(),
              Theme(
                data: Theme.of(context).copyWith(
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                ),
                child: PopupMenuButton(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: EdgeInsets.zero,
                  itemBuilder: (BuildContext bc) => [
                    popupMenuItem(
                      position: 1,
                      title: language['rename'],
                      icon: 'rename',
                      dW: dW,
                    ),
                    popupMenuItem(
                      position: 2,
                      title: language['delete'],
                      icon: 'delete',
                      dW: dW,
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 1) {
                      renameDialog();
                    } else if (value == 2) {
                      deleteDialog();
                    }
                  },
                  child: const Icon(
                    Icons.more_vert_rounded,
                    size: 22,
                    color: Color(0xffFFFFFF),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
