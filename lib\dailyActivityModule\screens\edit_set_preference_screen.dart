// ignore_for_file: deprecated_member_use, avoid_function_literals_in_foreach_calls
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';

class EditSetPreferenceScreen extends StatefulWidget {
  final EditSetPreferenceArguments args;

  const EditSetPreferenceScreen({super.key, required this.args});

  @override
  State<EditSetPreferenceScreen> createState() =>
      _EditSetPreferenceScreenState();
}

class _EditSetPreferenceScreenState extends State<EditSetPreferenceScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;
  bool isLoading = false;

  List<DateTime> dateTimes = [];

  final TimeOfDay _timeOfDay = const TimeOfDay(hour: 6, minute: 00);

  Future<TimeOfDay?> _showTimePicker(TimeOfDay time) async {
    return await showTimePicker(
      context: context,
      initialTime: time,
    );
  }

  savePreference() async {
    setState(() {
      isLoading = true;
    });

    List<num> timeSlotsInNumbers = dateTimes.map((dateTime) {
      return convertDateToNumber(dateTime);
    }).toList();
    timeSlotsInNumbers.sort();
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .savePreference(accessToken: user.accessToken, body: {
      'preferenceId': widget.args.preference.id,
      'timeSlots': timeSlotsInNumbers,
    });

    setState(() {
      isLoading = false;
    });

    if (response['success']) {
      pop();
    } else {
      showSnackbar(response['message']);
    }
  }

  removeTimeSlot(int index) {
    setState(() {
      dateTimes.removeAt(index);
    });
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;

    widget.args.preference.timeSlots.forEach((element) {
      // if (element.toString().contains('.')) {
      //   final split = element.toString().split('.');
      //   dateTimes.add(
      //       DateTime(2004, 1, 1, int.parse(split[0]), int.parse(split[1])));
      // } else {
      //   dateTimes.add(DateTime(2004, 1, 1, element.toInt()));
      // }
      final DateTime newDateTime = convertNumberToDate(element);
      dateTimes.add(newDateTime);
    });
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(title: widget.args.appBarTitle, dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  Widget screenBody() {
    List<Widget> timeWidgets = [];
    for (int index = 0; index < dateTimes.length; index++) {
      final e = dateTimes[index];
      final amPm = e.hour < 12 ? 'AM' : 'PM';
      timeWidgets.add(
        GestureDetector(
          onTap: () async {
            final selectedTime = await _showTimePicker(TimeOfDay(
                hour: dateTimes[index].hour, minute: dateTimes[index].minute));
            if (selectedTime != null) {
              final updatedDateTime =
                  DateTime(2004, 1, 1, selectedTime.hour, selectedTime.minute);
              setState(() {
                dateTimes[index] = updatedDateTime;
              });
            }
          },
          child: Padding(
            padding: EdgeInsets.only(top: dW * 0.065),
            child: Column(
              children: [
                Row(
                  children: [
                    if (index >= 0)
                      dateTimes.length == 1
                          ? Text(
                              '${language['preferred']} ${widget.args.preference.dailyActivity.title}',
                              style: textTheme.headline3!.copyWith(
                                fontSize: tS * 10,
                                color: const Color(0XFF707070),
                              ),
                            )
                          : Text(
                              '${language['pref']} ${widget.args.preference.dailyActivity.title} ${language['time']}(${index + 1})',
                              style: textTheme.headline3!.copyWith(
                                fontSize: tS * 10,
                                color: const Color(0XFF707070),
                              ),
                            ),
                    SizedBox(width: dW * 0.015),
                    if (index > 0)
                      GestureDetector(
                        onTap: () {
                          removeTimeSlot(index);
                        },
                        child: Text(language['remove'],
                            style: textTheme.headline2!.copyWith(
                              fontSize: tS * 9,
                              color: const Color(0XFFE34833),
                            )),
                      ),
                  ],
                ),
                SizedBox(height: dW * 0.015),
                Container(
                  height: dW * 0.125,
                  width: dW,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      width: 0.5,
                      color: const Color(0XFFAAABB5),
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
                    child: Row(
                      children: [
                        const AssetSvgIcon('setPref_clock'),
                        SizedBox(width: dW * 0.025),
                        Text(
                          '${(e.hour % 12 == 0 ? 12 : e.hour % 12).toString().padLeft(2, '0')}:${(e.minute).toString().padLeft(2, '0')}',
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF1D1E22),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          amPm,
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF1D1E22),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    return Padding(
      padding:
          EdgeInsets.only(left: dW * 0.07, right: dW * 0.07, top: dW * 0.05),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(3),
                        child: CachedImageWidget(
                          widget.args.preference.dailyActivity.icon,
                          boxFit: BoxFit.cover,
                          width: dW * 0.1,
                          height: dW * 0.1,
                        ),
                      ),
                      SizedBox(width: dW * 0.05),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.args.preference.dailyActivity.title,
                            style: textTheme.headline2!.copyWith(
                              fontSize: tS * 12,
                              color: const Color(0XFF212121),
                            ),
                          ),
                          SizedBox(height: dW * 0.02),
                          Text(
                            widget
                                .args.preference.dailyActivity.recommendedText,
                            style: textTheme.headline3!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0XFF707070),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: dW * 0.07),
                  ...timeWidgets,
                  if ([
                    'Yoga',
                    'Chants',
                    'Meditation',
                    'Bedtime Stories',
                    'Raga Music',
                  ].contains(widget.args.preference.dailyActivity.title))
                    if (dateTimes.length <= 2)
                      GestureDetector(
                        onTap: () async {
                          final selectedTime = await _showTimePicker(TimeOfDay(
                              hour: DateTime.now().hour,
                              minute: DateTime.now().minute));
                          if (selectedTime != null) {
                            dateTimes.add(DateTime(2004, 1, 1,
                                selectedTime.hour, selectedTime.minute));
                          }
                          setState(() {});
                        },
                        child: Container(
                          margin: EdgeInsets.only(top: dW * 0.1),
                          alignment: Alignment.center,
                          child: GradientWidget(
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xffFF328B),
                                Color(0xffCE1B69),
                              ],
                            ),
                            child: Text(
                              language['addMore'],
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Inter',
                              ),
                            ),
                          ),
                        ),
                      ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: GradientButton(
              isLoading: isLoading,
              onPressed: savePreference,
              buttonText: language['savePreference'],
            ),
          ),
        ],
      ),
    );
  }
}
