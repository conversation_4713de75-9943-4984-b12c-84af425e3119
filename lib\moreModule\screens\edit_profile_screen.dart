// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/bottom_sheet_content.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;

  bool isLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController fullNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  String selectedPhoto = '';
  bool photoRemoved = false;
  String selectedImagePath = 'assets/images/hp_lady.png';

  Widget get noPhoto => Stack(
        children: [
          Container(
            width: dW * 0.22,
            height: dW * 0.22,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child: Image.asset('assets/images/hp_lady.png'),
          ),
          editIcon,
        ],
      );

  Widget get editIcon => Positioned(
      right: dW * 0.002,
      bottom: dW * 0.002,
      child: Container(
        padding: EdgeInsets.all(dW * 0.01),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100),
          gradient: const LinearGradient(
            colors: [
              Color(0XFFCE1B69),
              Color(0XFFFF328B),
            ],
          ),
        ),
        child: const Icon(
          Icons.edit,
          color: Color(0XFFFFFFFF),
          size: 15,
        ),
      ));

  editProfile() async {
    final isValid = _formKey.currentState!.validate();

    if (!isValid) {
      return;
    }
    _formKey.currentState!.save();

    setState(() => isLoading = true);
    final Map<String, String> body = {
      "fullName": fullNameController.text.trim(),
      "email": emailController.text.trim(),
      "photoRemoved": photoRemoved.toString(),
    };

    final Map<String, String> files = {};
    if (selectedPhoto != '') {
      files["avatar"] = selectedPhoto;
    }

    final response =
        await Provider.of<AuthProvider>(context, listen: false).editProfile(
      body: body,
      files: files,
    );
    setState(() => isLoading = false);

    if (!response['success']) {
      showSnackbar(language[response['message']]);
    } else {
      pop();
      selectedPhoto = '';
      setState(() {});
    }
  }

  showImageOptionsBottomSheet() async {
    showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(left: dW * 0.08),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    Padding(
                      padding: EdgeInsets.only(bottom: 20, top: 20),
                      child: Text(
                        "Profile Photo",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Color.fromRGBO(41, 49, 49, 1),
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 25),
                  child: Row(
                    children: [
                      if (selectedPhoto != '' ||
                          (user.avatar != '' && !photoRemoved))
                        InkWell(
                          onTap: () {},
                          child: BottomSheetContent(
                            svgColor: const Color.fromRGBO(218, 11, 11, 1),
                            icon: Icon(Icons.delete,
                                color: Theme.of(context).primaryColor),
                            title: "Remove",
                            title2: " Photo",
                            func: () {
                              setState(() {
                                selectedPhoto = '';
                                photoRemoved = true;
                              });
                              pop();
                            },
                          ),
                        ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: (selectedPhoto != '' ||
                                    (user.avatar != '' && !photoRemoved))
                                ? dW * 0.06
                                : 0),
                        child: BottomSheetContent(
                          svgColor: const Color.fromRGBO(175, 17, 150, 1),
                          icon: Icon(Icons.image,
                              color: Theme.of(context).primaryColor),
                          title: "Gallery",
                          title2: "",
                          func: () async {
                            final img = await pickImage(ImageSource.gallery);
                            if (img != null) {
                              setState(() {
                                selectedPhoto = img.path;
                                photoRemoved = false;
                              });
                            }
                            pop();
                          },
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: dW * 0.06),
                        child: BottomSheetContent(
                          svgColor: const Color.fromRGBO(1, 82, 186, 1),
                          icon: Icon(Icons.camera_alt,
                              color: Theme.of(context).primaryColor),
                          title: "Camera",
                          title2: "",
                          func: () async {
                            final img = await pickImage(ImageSource.camera);
                            if (img != null) {
                              setState(() {
                                selectedPhoto = img.path;
                                photoRemoved = false;
                              });
                            }
                            pop();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  getAwsSignedUrl() async {
    final response =
        await Provider.of<AuthProvider>(context, listen: false).getAwsSignedUrl(
      fileName: selectedPhoto.split('/').last,
      filePath: selectedPhoto,
      // files: {'file': selectedPhoto},
    );
    if (response['success']) {
      showSnackbar('Signed Url: ${response['result']}');
    } else {
      showSnackbar(response['message']);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fullNameController.text = user.fullName;
    emailController.text = user.email;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    bool valid = fullNameController.text.isNotEmpty ||
        emailController.text.isNotEmpty ||
        RegExp(r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$')
            .hasMatch(emailController.text);

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(
        title: language['editProfile'],
        dW: dW,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: dW * 0.06),
                      child: GestureDetector(
                        onTap: showImageOptionsBottomSheet,
                        child: Column(children: [
                          selectedPhoto != ''
                              ? Stack(
                                  children: [
                                    SizedBox(
                                      width: dW * 0.22,
                                      height: dW * 0.22,
                                      child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(100),
                                          child: Image.file(File(selectedPhoto),
                                              fit: BoxFit.cover)),
                                    ),
                                    editIcon,
                                  ],
                                )
                              : photoRemoved
                                  ? noPhoto
                                  : user.avatar != ''
                                      ? Stack(
                                          children: [
                                            Container(
                                              decoration: const BoxDecoration(
                                                color: Color(0XFF975EFF),
                                                shape: BoxShape.circle,
                                              ),
                                              padding: const EdgeInsets.all(2),
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(100),
                                                child: CachedImageWidget(
                                                  user.avatar != ''
                                                      ? user.avatar
                                                      : networkDummy,
                                                  boxFit: BoxFit.cover,
                                                  width: dW * 0.22,
                                                  height: dW * 0.22,
                                                ),
                                              ),
                                            ),
                                            editIcon,
                                          ],
                                        )
                                      : noPhoto,
                        ]),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                          top: dW * 0.06, left: dW * 0.06, right: dW * 0.06),
                      child: CustomTextFieldWithLabel(
                        label: language['fullName'],
                        hintText: '',
                        controller: fullNameController,
                        borderColor: greyBorderColor,
                        textCapitalization: TextCapitalization.words,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return language['pleaseEnterFullName'];
                          }
                          return null;
                        },
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          top: dW * 0.04, left: dW * 0.06, right: dW * 0.06),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        language['mobileNumber'],
                        style: Theme.of(context).textTheme.headline2!.copyWith(
                              fontSize: tS * 12,
                              color: const Color(0xff84858E),
                            ),
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(left: dW * 0.06, right: dW * 0.06),
                      child: Row(
                        children: [
                          SizedBox(
                            width: dW * 0.22,
                            child: CustomTextFieldWithLabel(
                              enabled: false,
                              prefixIcon: Padding(
                                padding: const EdgeInsets.only(left: 12),
                                child: Row(
                                  children: [
                                    const AssetSvgIcon(
                                      'flag_icon',
                                    ),
                                    const SizedBox(width: 5),
                                    Text(
                                      '+91',
                                      style: textTheme.headline2!.copyWith(
                                        fontSize: tS * 14,
                                        color: const Color(0XFF84858E),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              prefixIconConstraints:
                                  const BoxConstraints(maxHeight: 24),
                              hintText: '',
                              borderColor: greyBorderColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: CustomTextFieldWithLabel(
                              enabled: false,
                              hintText: '',
                              borderColor: greyBorderColor,
                              prefixIcon: Padding(
                                padding: EdgeInsets.only(left: dW * 0.05),
                                child: Row(
                                  children: [
                                    Text(
                                      user.phone.substring(0, 5),
                                      style: textTheme.headline2!.copyWith(
                                        fontSize: tS * 12,
                                        color: const Color(0XFF84858E),
                                      ),
                                    ),
                                    SizedBox(width: dW * 0.007),
                                    Text(
                                      user.phone.substring(5),
                                      style: textTheme.headline2!.copyWith(
                                        fontSize: tS * 12,
                                        color: const Color(0XFF84858E),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      alignment: Alignment.centerLeft,
                      margin: EdgeInsets.only(
                          top: dW * 0.02, left: dW * 0.06, right: dW * 0.06),
                      child: Text(
                        language['thisCan'],
                        style: Theme.of(context).textTheme.headline2!.copyWith(
                              fontSize: tS * 10,
                              color: const Color(0xffAAABB5),
                            ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                          top: dW * 0.045, left: dW * 0.06, right: dW * 0.06),
                      child: CustomTextFieldWithLabel(
                        label: language['emailId'],
                        hintText: '',
                        controller: emailController,
                        borderColor: greyBorderColor,
                        inputType: TextInputType.emailAddress,
                        textCapitalization: TextCapitalization.none,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return language['pleaseEnterEmail'];
                          } else if (!RegExp(
                                  r'^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$')
                              .hasMatch(value)) {
                            return language['pleaseEnterValidEmail'];
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
                bottom: dW * 0.06, left: dW * 0.06, right: dW * 0.06),
            child: GradientButton(
              elevation: 0,
              isLoading: isLoading,
              onPressed: valid ? editProfile : () {},
              //  onPressed: selectedPhoto == '' ? () {} : getAwsSignedUrl,
              buttonText: language['updateChanges'],
            ),
          ),
        ],
      ),
    );
  }
}
