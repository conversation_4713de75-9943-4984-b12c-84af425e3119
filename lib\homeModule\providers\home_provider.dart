// ignore_for_file: prefer_final_fields, avoid_print, avoid_function_literals_in_foreach_calls

import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../../api.dart';
import '../../dailyActivityModule/model/daily_activity_model.dart';
import '../../http_helper.dart';
import '../models/article_model.dart';
import '../models/baby_tracker_model.dart';

class HomeProvider with ChangeNotifier {
  List<Article> _garbhSanskarArticles = [];
  List<Article> get garbhSanskarArticles => [..._garbhSanskarArticles];

  List<BabyTracker> _babyTrackers = [];
  List<BabyTracker> get babyTrackers => [..._babyTrackers];

  BabyTracker? getBabyDataByWeek(num week) {
    int i = _babyTrackers.indexWhere((element) => element.week == week);
    if (i != -1) {
      return _babyTrackers[i];
    } else {
      return null;
    }
  }

  Map garbhSanskarVideos = {
    'gbVideo':
        'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    'walkthrough':
        'https://dsqqu7oxq6o1v.cloudfront.net/motion-array-1095556-xtuAfnXvIQ-high.mp4',
  };

  fetchGarbhSanskarArticles({required String accessToken}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchGarbhSanskarArticles']}';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Article> fetchedArticles = (response['articles'] as List)
            .map((article) => Article.jsonToArticle(article))
            .toList();

        _garbhSanskarArticles = fetchedArticles;

        if (response['gbVideos'] != null) {
          garbhSanskarVideos = response['walkthrough']['value'];
        }
        notifyListeners();
      }

      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetArticles'};
    }
  }

  List<Article> _recommendedArticles = [];
  List<Article> get recommendedArticles => [..._recommendedArticles];

  fetchArticles({required String accessToken, required String query}) async {
    try {
      final url =
          '${webApi['domain']}${endPoint['fetchRecommendedArticles']}$query';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Article> fetchedRecommendedArticles = (response['result'] as List)
            .map((article) => Article.jsonToArticle(article))
            .toList();

        _recommendedArticles = fetchedRecommendedArticles;
        cacheImages(_recommendedArticles.map((e) => e.imageUrl).toList());
        notifyListeners();
      }

      return response;
    } catch (error) {
      print(error);
      return {
        'success': false,
        'message': 'failed to get recommended articles'
      };
    }
  }

  List<Facts> _facts = [];
  List<Facts> get facts => [..._facts];

  fetchFacts({required String accessToken, required String query}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchFacts']}$query';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Facts> fetchedFacts = (response['result'] as List)
            .map((facts) => Facts.jsonToFacts(facts))
            .toList();

        _facts = fetchedFacts;
        cacheImages(_facts.map((e) => e.imageUrl).toList());
        notifyListeners();
      }

      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'message': 'failed to get facts'};
    }
  }

  List<Testimonial> _testimonials = [];
  List<Testimonial> get testimonials => [..._testimonials];

  fetchTestimonial({required String accessToken, required String query}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchTestimonials']}$query';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Testimonial> fetchedtestimonial = (response['result'] as List)
            .map((testimonials) => Testimonial.jsonToTestimonial(testimonials))
            .toList();

        _testimonials = fetchedtestimonial;
        cacheImages(_testimonials.map((e) => e.imageUrl).toList());
        notifyListeners();
      }

      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'message': 'failed to get testimonials'};
    }
  }

  List<Faq> _faqs = [];
  List<Faq> get faqs => [..._faqs];

  fetchFaq({required String accessToken, required String query}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchFaqs']}$query';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Faq> fetchedfaq = (response['result'] as List)
            .map((faqs) => Faq.jsonToFaq(faqs))
            .toList();

        _faqs = fetchedfaq;
        notifyListeners();
      }

      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'message': 'failed to get faqs'};
    }
  }

  List<Facts> _lesserKnowns = [];
  List<Facts> get lesserKnowns => [..._lesserKnowns];

  cacheLesserKnown() async {
    final Directory cacheDir = await getTemporaryDirectory();

    _lesserKnowns.forEach((element) async {
      final String fileName = element.imageUrl.split('/').last;
      final File imageFile = File('${cacheDir.path}/$fileName');

      if (!await imageFile.exists()) {
        final response = await http.get(Uri.parse(element.imageUrl));
        if (response.statusCode == 200) {
          await imageFile.writeAsBytes(response.bodyBytes);
        }
      }
    });
  }

  fetchLesserKnown({required String accessToken, required String query}) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchLessorKnown']}$query';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<Facts> fetchedLesserKnown = (response['result'] as List)
            .map((lesserKnown) => Facts.jsonToFacts(lesserKnown))
            .toList();

        _lesserKnowns = fetchedLesserKnown;
        cacheImages(_lesserKnowns.map((e) => e.imageUrl).toList());
        notifyListeners();

        if (_lesserKnowns.isNotEmpty) {
          // cacheLesserKnown();
        }
      }

      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'message': 'failed to get lesser known'};
    }
  }

  fetchBabyTrackerWeek({
    required String accessToken,
    required List<num> weeks,
  }) async {
    try {
      if (_babyTrackers.any((element) => weeks.contains(element.week))) {
        return {'success': true};
      }

      final url =
          '${webApi['domain']}${endPoint['fetchBabyTrackerWeek']}?weeks=${json.encode(weeks)}';
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: accessToken,
      );

      if (response['success']) {
        List<BabyTracker> fetchedTrackers = (response['result'] as List)
            .map((babyTracker) => BabyTracker.jsonToBabyTracker(babyTracker))
            .toList();

        _babyTrackers.addAll(fetchedTrackers);
        notifyListeners();
      }

      return response;
    } catch (error) {
      print(error);
      return {'success': false, 'message': 'failedToGetBabyData'};
    }
  }

  List<Preferences> _upcomingPreference = [];

  List<Preferences> get upcomingPreference => [..._upcomingPreference];

  fetchUpcomingPreferences({
    required String accessToken,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['fetchUpcomingPreferences']}';
      final response = await RemoteServices.httpRequest(
          method: 'GET', url: url, accessToken: accessToken);

      if (response['success']) {
        List<Preferences> fetchedUpcomingPreference = [];

        response['result'].forEach((preference) {
          fetchedUpcomingPreference
              .add(Preferences.jsonToPreferences(preference));
        });
        _upcomingPreference = fetchedUpcomingPreference;
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to upcoming preference',
      };
    }
  }
}
