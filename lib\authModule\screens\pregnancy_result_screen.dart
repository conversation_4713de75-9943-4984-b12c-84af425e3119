// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../colors.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/user_model.dart';
import '../providers/auth_provider.dart';

class PregnancyResultScreen extends StatefulWidget {
  const PregnancyResultScreen({super.key});

  @override
  State<PregnancyResultScreen> createState() => _PregnancyResultScreenState();
}

class _PregnancyResultScreenState extends State<PregnancyResultScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  late User user;
  bool isLoading = false;
  late DateTime deliveryDate;
  late DateTime firstDate;
  late DateTime secondDate;
  late int pregnancyWeek;

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;

    deliveryDate = user.conceptionDate!.add(const Duration(days: 266));
    int days = DateTime.now().difference(user.conceptionDate!).inDays;
    pregnancyWeek = (days / 7).ceil();

    firstDate = deliveryDate.subtract(const Duration(days: 5));
    secondDate = deliveryDate.add(const Duration(days: 5));
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      backgroundColor: white,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(left: 38, right: 38, top: dW * 0.15),
                    child: Image.asset(
                      'assets/images/baby_graphic.png',
                      height: dW * 0.7,
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 38, right: 38, top: dW * 0.15),
                    child: Text(
                      language['congratulations'],
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontSize: 16,
                          color: Color(0XFF1D1E22),
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Cantora One'),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 38, right: 38, top: dW * 0.05),
                    child: Text(
                      language['youAre'] +
                          (pregnancyWeek < 0 ? ' 0 ' : ' $pregnancyWeek ') +
                          language['weeksPregnant'],
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headline1!.copyWith(
                            fontSize: tS * 20,
                            color: const Color(0XFF975EFF),
                          ),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 38, right: 38, top: dW * 0.09),
                    child: Text(
                      language['expctdDDateIs'] +
                          ': \n${DateFormat('d MMM').format(firstDate)} - ${DateFormat('d MMM, yyyy').format(secondDate)}',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headline3!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF6B6C75),
                          ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: GradientButton(
                isLoading: isLoading,
                onPressed: () {
                  pushAndRemoveUntil(NamedRoute.personalizeLoaderScreen,
                      arguments: PersonalizeLoaderArguments());
                },
                buttonText: language['continue'],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
