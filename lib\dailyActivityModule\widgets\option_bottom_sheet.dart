// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../dynamic_Link_api.dart';
import '../model/daily_activity_model.dart';
import '../providers/daily_activity_provider.dart';

class OptionBottomSheet extends StatefulWidget {
  final Chant? chant;
  // final Meditation? meditation;
  final RagaMusic? ragaMusic;
  final BedtimeStory? bedtimeStory;
  final int currentIndex;
  final String activityTitle;
  final int? selectedPeriod;
  final bool? minimised;
  final Function? toggleAudioPlayer;
  final Function? playAudio;
  final Function? playOrView;
  final bool? fromLikeScreen;

  const OptionBottomSheet({
    super.key,
    this.chant,
    // this.meditation,
    this.ragaMusic,
    this.bedtimeStory,
    required this.currentIndex,
    required this.activityTitle,
    this.selectedPeriod,
    this.minimised,
    this.toggleAudioPlayer,
    this.playAudio,
    this.playOrView,
    this.fromLikeScreen,
  });

  @override
  State<OptionBottomSheet> createState() => _OptionBottomSheetState();
}

class _OptionBottomSheetState extends State<OptionBottomSheet> {
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late String name;
  late String albumArt;
  late String duration;

  List<Chant> chants = [];
  List<Meditation> meditations = [];
  List<RagaMusic> ragaMusic = [];
  List<BedtimeStory> bedtimeStory = [];
  late User user;

  _getImageUrl() {
    // if (widget.meditation != null) {
    //   return widget.meditation!.albumArt;
    // }
    // else
    if (widget.chant != null) {
      return widget.chant!.albumArt;
    } else if (widget.ragaMusic != null) {
      return widget.ragaMusic!.albumArt;
    } else if (widget.bedtimeStory != null) {
      return widget.bedtimeStory!.albumArt;
    }
    return '';
  }

  _getName() {
    // if (widget.meditation != null) {
    //   return widget.meditation!.name;
    // }
    // else
    if (widget.chant != null) {
      return widget.chant!.name;
    } else if (widget.ragaMusic != null) {
      return widget.ragaMusic!.name;
    } else if (widget.bedtimeStory != null) {
      return widget.bedtimeStory!.name;
    }
    return '';
  }

  _getDuration() {
    // if (widget.meditation != null) {
    //   return widget.meditation!.duration;
    // } else
    if (widget.chant != null) {
      return widget.chant!.duration;
    } else if (widget.ragaMusic != null) {
      return widget.ragaMusic!.duration;
    } else if (widget.bedtimeStory != null) {
      return widget.bedtimeStory!.duration;
    }
    return '';
  }

  checkAndShare() {
    if (widget.chant != null) {
      createActivityLink(chants[widget.currentIndex].id, 'Chant');
    }
    // else if (widget.meditation != null) {
    //   createActivityLink(meditations[widget.currentIndex].id, 'Meditation');
    // }
    else if (widget.ragaMusic != null) {
      createActivityLink(ragaMusic[widget.currentIndex].id, 'Raga Music');
    } else if (widget.bedtimeStory != null) {
      createActivityLink(bedtimeStory[widget.currentIndex].id, 'Bedtime Story');
    }
  }

  likeUnlike() async {
    setState(() {
      widget.bedtimeStory!.isLiked = !widget.bedtimeStory!.isLiked;
    });
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .likeUnlike(
      body: {
        'bedTimeStory': widget.bedtimeStory!.id,
        'like': widget.bedtimeStory!.isLiked,
      },
      accessToken: user.accessToken,
    );

    if (!response['success']) {
      setState(() {
        widget.bedtimeStory!.isLiked = !widget.bedtimeStory!.isLiked;
      });
    }
  }

  @override
  void initState() {
    super.initState();

    name = _getName();
    albumArt = _getImageUrl();
    duration = _getDuration();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    chants = Provider.of<DailyActivityProvider>(context).chants;
    meditations = Provider.of<DailyActivityProvider>(context).meditation;
    ragaMusic = Provider.of<DailyActivityProvider>(context).ragaMusic;
    bedtimeStory =
        Provider.of<DailyActivityProvider>(context, listen: false).bedtimeStory;

    final likeBedtimeStory =
        Provider.of<DailyActivityProvider>(context, listen: true)
            .likedBedtimeStories;

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            pop();
          },
          child: Container(
            padding: EdgeInsets.only(
                left: dW * 0.05, right: dW * 0.05, top: dW * 0.15),
            alignment: Alignment.centerLeft,
            child: const AssetSvgIcon('collaps'),
          ),
        ),
        Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: dW * 0.25, bottom: dW * 0.05),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  repeat: ImageRepeat.repeat,
                  fit: BoxFit.cover,
                  width: dW * 0.6,
                  height: dW * 0.65,
                  imageUrl: albumArt,
                  placeholder: (_, __) => Image.asset(
                    'assets/placeholders/placeholder.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            if ([
              'Chants',
              // 'Meditation',
              'Raga Music',
            ].contains(widget.activityTitle))
              Positioned(
                top: dW * 0.25,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: dW * 0.06),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                    color: const Color(0XFF000000).withOpacity(0.16),
                  ),
                ),
              ),
            if ([
              'Chants',
              // 'Meditation',
              'Raga Music',
            ].contains(widget.activityTitle))
              Positioned(
                top: dW * 0.275,
                left: dW * 0.155,
                right: 0,
                child: Row(
                  children: [
                    const AssetSvgIcon('music_box'),
                    SizedBox(width: dW * 0.015),
                    Text(
                      widget.activityTitle,
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 15.18,
                        color: const Color(0XFFFFFFFF),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        if (widget.bedtimeStory != null)
          Padding(
            padding: EdgeInsets.only(bottom: dW * 0.025),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                widget.bedtimeStory!.type == 'Read'
                    ? const AssetSvgIcon('read', color: Color(0XFFFF328B))
                    : const AssetSvgIcon('listen', color: Color(0XFFFF328B)),
                SizedBox(width: dW * 0.01),
                Text(
                  widget.bedtimeStory!.type,
                  style: TextStyle(
                      color: const Color(0XFFFF668E),
                      fontFamily: 'Poppins',
                      fontSize: tS * 10,
                      fontWeight: FontWeight.w400),
                ),
              ],
            ),
          ),
        Text(
          name,
          style: textTheme.headline1!.copyWith(
            fontSize: tS * 14,
            color: const Color(0XFF1D1E22),
          ),
          textAlign: TextAlign.center,
        ),
        Padding(
          padding: EdgeInsets.symmetric(vertical: dW * 0.025),
          child: Text(
            duration,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 10,
              color: const Color(0XFF84858E),
            ),
          ),
        ),
        const Spacer(),
        Padding(
          padding: EdgeInsets.only(left: dW * 0.1, right: dW * 0.1),
          child: GestureDetector(
            onTap: () {
              pop();
              if (widget.chant != null ||
                  //  widget.meditation != null ||
                  widget.ragaMusic != null) {
                if (widget.playAudio != null) {
                  widget.playAudio!();
                }
              } else if (widget.bedtimeStory != null) {
                if (widget.playOrView != null) {
                  widget.playOrView!(
                      widget.bedtimeStory, widget.fromLikeScreen);
                }
              }
            },
            child: Container(
              color: transparentColor,
              child: Row(
                children: [
                  const AssetSvgIcon('play_now'),
                  SizedBox(width: dW * 0.03),
                  if ([
                    'Chants',
                    // 'Meditation',
                    'Raga Music',
                  ].contains(widget.activityTitle))
                    Text(
                      language['play'],
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 14,
                        color: const Color(0XFF1D1E22),
                      ),
                    ),
                  if ([
                    'Bedtime Stories',
                  ].contains(widget.activityTitle))
                    if (widget.bedtimeStory!.type == 'Read')
                      Text(
                        language['startReading'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF1D1E22),
                        ),
                      ),
                  if ([
                    'Bedtime Stories',
                  ].contains(widget.activityTitle))
                    if (widget.bedtimeStory!.type == 'Listen')
                      Text(
                        language['play'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF1D1E22),
                        ),
                      ),
                ],
              ),
            ),
          ),
        ),
        if ([
          'Bedtime Stories',
        ].contains(widget.activityTitle))
          if (widget.bedtimeStory!.type == 'Read')
            GestureDetector(
              onTap: likeUnlike,
              child: Container(
                color: transparentColor,
                margin: EdgeInsets.only(
                  left: dW * 0.1,
                  right: dW * 0.1,
                  top: dW * 0.1,
                ),
                child: Container(
                  color: transparentColor,
                  child: Row(
                    children: [
                      Icon(
                        widget.bedtimeStory!.isLiked
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: widget.bedtimeStory!.isLiked
                            ? const Color(0XFFFF668E)
                            : blackColor,
                        size: 23,
                      ),
                      SizedBox(width: dW * 0.03),
                      Text(
                        language['like'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 14,
                          color: const Color(0XFF1D1E22),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        GestureDetector(
          onTap: checkAndShare,
          child: Container(
            color: transparentColor,
            margin: EdgeInsets.only(
                left: dW * 0.1,
                right: dW * 0.1,
                top: dW * 0.1,
                bottom: dW * 0.1),
            child: Row(
              children: [
                const AssetSvgIcon('share_now'),
                SizedBox(width: dW * 0.03),
                Text(
                  'Share',
                  style: textTheme.headline2!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0XFF1D1E22),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
