// ignore_for_file: avoid_print, prefer_final_fields

import 'package:flutter/material.dart';
import '../../api.dart';
import '../../http_helper.dart';
import '../model/diary_model.dart';

class DiaryProvider with ChangeNotifier {
  List<Recording> _recordings = [];

  List<Recording> get recordings => [..._recordings];

  saveRecording(
      {required Map<String, String> body,
      required Map<String, String> files,
      required String accessToken}) async {
    try {
      final url = '${webApi['domain']}${endPoint['saveRecording']}';
      final response = await RemoteServices.formDataRequest(
        method: 'POST',
        url: url,
        body: body,
        files: files,
        accessToken: accessToken,
      );
      if (response['success']) {
        final newRecording = Recording.jsonToRecording(response['result']);
        _recordings.insert(0, newRecording);
      }
      notifyListeners();
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to save recording',
      };
    }
  }

  fetchRecordings({
    required String accessToken,
    required String query,
    bool refresh = false,
  }) async {
    try {
      int skip = refresh ? 0 : _recordings.length;
      if (query == '') {
        query = 'skip=$skip';
      } else {
        query += '&skip=$skip';
      }
      final url = '${webApi['domain']}${endPoint['fetchRecordings']}?$query';
      final response = await RemoteServices.httpRequest(
          method: 'GET', url: url, accessToken: accessToken);

      if (response['success']) {
        List<Recording> fetchedRecordings = [];

        response['result'].forEach((recording) {
          fetchedRecordings.add(Recording.jsonToRecording(recording));
        });

        if (skip == 0) {
          _recordings = fetchedRecordings;
        } else {
          _recordings.addAll(fetchedRecordings);
        }
        notifyListeners();
      }
      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get recordings',
      };
    }
  }

  updateRecording({
    required String accessToken,
    required Map body,
  }) async {
    try {
      final url = '${webApi['domain']}${endPoint['updateRecording']}';
      final response = await RemoteServices.httpRequest(
          method: 'PUT', url: url, accessToken: accessToken, body: body);

      if (response['success']) {
        if (body['deleteRecording']) {
          _recordings
              .removeWhere((element) => element.id == body['recordingId']);
        } else {
          int i = _recordings
              .indexWhere((element) => element.id == body['recordingId']);
          _recordings[i].name = body['name'];
        }

        notifyListeners();
      }
      return response;
    } catch (e) {
      print(e);
      return {
        'success': false,
        'message': 'Failed to update name',
      };
    }
  }
}
