// ignore_for_file: deprecated_member_use, unused_element
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/widgets/gradient_edit_button.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/dotted_seperator.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';
import '../providers/daily_activity_provider.dart';

class SetPreferenceWidget extends StatefulWidget {
  final Preferences preference;
  final List<Preferences> preferences;

  const SetPreferenceWidget({
    super.key,
    required this.preference,
    required this.preferences,
  });

  @override
  State<SetPreferenceWidget> createState() => _SetPreferenceWidgetState();
}

class _SetPreferenceWidgetState extends State<SetPreferenceWidget> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  skipPreference() async {
    setState(() {
      isLoading = true;
    });

    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .savePreference(accessToken: user.accessToken, body: {
      'preferenceId': widget.preference.id,
      'isActive': !widget.preference.isActive,
    });

    setState(() {
      isLoading = false;
    });

    if (response['success']) {
    } else {
      showSnackbar(response['message']);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final isLastItem = widget.preference == widget.preferences.last;

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(
              left: dW * 0.07, right: dW * 0.03, top: dW * 0.06),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: CachedImageWidget(
                  widget.preference.dailyActivity.icon,
                  boxFit: BoxFit.cover,
                  width: dW * 0.1,
                  height: dW * 0.1,
                ),
              ),
              SizedBox(width: dW * 0.05),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.preference.dailyActivity.title,
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0XFF212121),
                      )),
                  SizedBox(height: dW * 0.02),
                  Text(
                    widget.preference.timeSlots.map((e) {
                      return convertToTime(e);
                    }).join(', '),
                    style: widget.preference.isActive == true
                        ? textTheme.headline3!.copyWith(
                            fontSize: tS * 10,
                            color: const Color(0XFF707070),
                          )
                        : textTheme.headline3!.copyWith(
                            fontSize: tS * 10,
                            color: const Color(0XFF9798A3),
                          ),
                  ),
                ],
              ),
              const Spacer(),
              widget.preference.isActive == true
                  ? SizedBox(
                      width: dW * 0.1,
                      height: dW * 0.1,
                      child: GradientEditButton(
                        gradient: const LinearGradient(
                          colors: [
                            Color(0XFF0036B4),
                            Color(0XFF06ACFF),
                          ],
                        ),
                        buttonText: language['edit'],
                        onPressed: () {
                          push(
                            NamedRoute.editSetPreferenceScreen,
                            arguments: EditSetPreferenceArguments(
                              appBarTitle: language['setPreference'],
                              preference: widget.preference,
                            ),
                          );
                        },
                      ),
                    )
                  : SizedBox(
                      width: dW * 0.1,
                      height: dW * 0.1,
                      child: GradientEditButton(
                        buttonColor: const Color(0XFFBFC0C8),
                        buttonText: language['edit'],
                        onPressed: () {},
                      ),
                    ),
              SizedBox(
                  width: dW * 0.16,
                  height: dW * 0.1,
                  child: Padding(
                    padding: EdgeInsets.only(top: dW * 0.01),
                    child: TextButton(
                      onPressed: skipPreference,
                      child: Text(
                          widget.preference.isActive == true
                              ? language['skipPreference']
                              : language['activate'],
                          style: textTheme.headline2!.copyWith(
                            fontSize: tS * 10,
                            color: const Color(0XFFD72137),
                          )),
                    ),
                  )),
            ],
          ),
        ),
        if (!isLastItem)
          Padding(
            padding: EdgeInsets.only(
                top: dW * 0.06, left: dW * 0.07, right: dW * 0.07),
            child: const DottedSeperator(
              height: 1,
              width: 3,
              color: Color(0XFFDBDBE3),
            ),
          ),
      ],
    );
  }
}
