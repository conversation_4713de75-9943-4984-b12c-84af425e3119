// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';

class GradientEditButton extends StatefulWidget {
  final String? icon;
  final String buttonText;
  final bool isLoading;
  final Function onPressed;
  final double elevation;
  final Gradient? gradient;
  final Color? buttonColor;

  const GradientEditButton({
    super.key,
    this.icon,
    required this.buttonText,
    this.isLoading = false,
    required this.onPressed,
    this.elevation = 2,
    this.gradient,
    this.buttonColor,
  });

  @override
  State<GradientEditButton> createState() => _GradientEditButtonState();
}

class _GradientEditButtonState extends State<GradientEditButton> {
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    return Container(
      padding: EdgeInsets.only(top: dW * 0.02),
      child: ElevatedButton(
        onPressed: () => widget.onPressed(),
        style: ElevatedButton.styleFrom(
          elevation: widget.elevation,
          padding: const EdgeInsets.all(0),
          fixedSize: Size(dW * 0.87, 42),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          primary: widget.gradient != null ? null : widget.buttonColor,
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: widget.gradient,
          ),
          child: Center(
            child: widget.isLoading
                ? SizedBox(
                    height: dW * 0.055,
                    width: dW * 0.055,
                    child: circularForButton(dW * 0.05),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (widget.icon != null)
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: AssetSvgIcon(widget.icon!),
                        ),
                      Text(
                        widget.buttonText,
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 10,
                          color: const Color(0XFFFFFFFF),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
