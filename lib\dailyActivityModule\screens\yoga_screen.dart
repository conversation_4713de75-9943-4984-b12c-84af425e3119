// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../widgets/activity_illustration_image.dart';
import '../widgets/yoga_widget.dart';

class YogaScreen extends StatefulWidget {
  final YogaArguments args;
  const YogaScreen({super.key, required this.args});

  @override
  State<YogaScreen> createState() => _YogaScreenState();
}

class _YogaScreenState extends State<YogaScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  List trimester = [1, 2, 3];

  int selectedTrimester = 1;

  List<Yoga> yogas = [];

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  fetchYoga() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .yoga
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedYoga(
      accessToken: user.accessToken,
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    selectedTrimester = user.pregnancyTrimester.toInt();
    fetchYoga();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    yogas = Provider.of<DailyActivityProvider>(context)
        .getYogaUsingTrimester(selectedTrimester);

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(title: widget.args.title, dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            children: [
              SizedBox(height: dW * 0.05),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      SizedBox(
                        height: dW * 0.08,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: [
                            Row(
                              children: [
                                ...trimester.map(
                                  (i) => GestureDetector(
                                    onTap: () {
                                      setState(
                                        () {
                                          selectedTrimester = i;
                                        },
                                      );
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(left: dW * 0.03),
                                      width: dW * 0.3,
                                      decoration: BoxDecoration(
                                        color: selectedTrimester == i
                                            ? const Color(0XFF975EFF)
                                            : const Color(0XFFF4F4F4),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Center(
                                        child: Text(
                                          '${trimesterText(i)} ' +
                                              language['trimester'],
                                          style: textTheme.headline2!.copyWith(
                                            fontSize: tS * 12,
                                            color: selectedTrimester == i
                                                ? const Color(0XFFFFFFFF)
                                                : const Color(0XFF000000),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.07),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: yogas.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, i) => YogaWidget(
                          yoga: yogas[i],
                          selectedTrimester: selectedTrimester,
                        ),
                      ),
                      ActivityIllustrationImage('yoga_illus.png'),
                    ],
                  ),
                ),
              )
            ],
          );
  }
}
