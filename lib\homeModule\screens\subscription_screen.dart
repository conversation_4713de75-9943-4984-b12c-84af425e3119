// ignore_for_file: unrelated_type_equality_checks, deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/banner_widget_shimmer.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  bool isLoading = false;

  late User user;

  String subscriptionScreenImage = '';

  Map? transaction;
  bool isPaying = false;

  Map plan = {};

  url() async {
    const url = 'https://pages.razorpay.com/9b-course';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  razorpayBuyProcess() async {
    if (transaction != null) {
      push(NamedRoute.paymentScreen,
          arguments: PaymentScreenArguments(
            orderId: transaction!['rzpOrderId'],
            amount: transaction!['totalAmount'],
            type: 'Subscription',
            transaction: transaction!,
          ));
    } else {
      setState(() => isPaying = true);
      final response =
          await Provider.of<DailyActivityProvider>(context, listen: false)
              .createSubscription(accessToken: user.accessToken, body: {
        'user': user.id,
        'planId': plan['planId'],
        'total': plan['planAmount'],
      });

      setState(() => isPaying = false);

      if (response['success']) {
        transaction = response['result'];
        push(NamedRoute.paymentScreen,
            arguments: PaymentScreenArguments(
              orderId: transaction!['rzpOrderId'],
              amount: transaction!['totalAmount'],
              type: 'Subscription',
              transaction: transaction!,
            ));
      }
    }
  }

  @override
  void initState() {
    super.initState();
    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    subscriptionScreenImage =
        Provider.of<AuthProvider>(context).subscriptionScreenImage;
    plan = Provider.of<AuthProvider>(context).plan;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(dW * 0.04),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xffCE1B69),
                const Color(0xffFF328B).withOpacity(0.71),
              ],
            ),
          ),
          child: Row(
            children: [
              Image.asset(
                'assets/images/9B.png',
                height: dW * 0.1,
              ),
              const Spacer(),
              GestureDetector(
                onTap: pop,
                child: Container(
                  padding: EdgeInsets.all(dW * 0.01),
                  decoration: BoxDecoration(
                    color: const Color(0XFFF2F2F2),
                    borderRadius: BorderRadius.circular(100),
                  ),
                  child: Icon(
                    Icons.clear,
                    color: const Color(0XFF000000),
                    size: dW * 0.05,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            child: CachedNetworkImage(
              imageUrl: subscriptionScreenImage,
              placeholder: (_, __) => BannerWidgetShimmer(),
            ),
          ),
        ),
        BottomAlignedWidget(
            dW: dW,
            dH: dH,
            child: Column(
              children: [
                GradientButton(
                  isLoading: false,
                  buttonText: 'Register Now at ₹ ${plan['planAmount']}/-',
                  onPressed: () {
                    razorpayBuyProcess();
                    // Navigator.of(context).push(MaterialPageRoute(
                    //     builder: (ctx) => const InAppBrowserScreen()));
                  },
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.02),
                  child: Text(
                    plan['date'],
                    style: textTheme.headline1!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF515259),
                    ),
                  ),
                ),
              ],
            )),
      ],
    );
  }
}
