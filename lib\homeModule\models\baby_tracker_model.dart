class BabyTracker {
  final num week;
  final String weeklyImage;
  final String sizeDescription;
  final String sizeImage;
  final String weight;
  final String length;
  final String content;
  final bool hideSize;

  BabyTracker({
    required this.week,
    required this.weeklyImage,
    required this.sizeDescription,
    required this.sizeImage,
    required this.weight,
    required this.length,
    required this.content,
    this.hideSize = false,
  });

  static BabyTracker jsonToBabyTracker(Map babyTracker) => BabyTracker(
        week: babyTracker['week'],
        weeklyImage: babyTracker['weeklyImage'],
        sizeDescription: babyTracker['sizeDescription'],
        sizeImage: babyTracker['sizeImage'],
        weight: babyTracker['weight'],
        length: babyTracker['length'],
        content: babyTracker['content'],
        hideSize: babyTracker['hideSize'] ?? false,
      );
}

class PositionalDate {
  final DateTime date;
  final int position;

  PositionalDate({
    required this.date,
    required this.position,
  });
}
