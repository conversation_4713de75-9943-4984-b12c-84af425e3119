// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';

class MasterClassWidget extends StatefulWidget {
  final MasterClass masterClass;
  final VoidCallback? onVideoProgress;
  final User user;
  final int index;
  final Map videoPercentage;
  const MasterClassWidget({
    super.key,
    required this.masterClass,
    this.onVideoProgress,
    required this.user,
    required this.index,
    required this.videoPercentage,
  });

  @override
  State<MasterClassWidget> createState() => _MasterClassWidgetState();
}

class _MasterClassWidgetState extends State<MasterClassWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  // Define masterClassIndex with a default value

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final masterClass =
        Provider.of<DailyActivityProvider>(context, listen: false)
            .masterClasses;

    return Padding(
      padding:
          EdgeInsets.only(left: dW * 0.04, right: dW * 0.04, top: dW * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              if ((int.tryParse(widget.user.masterClassCount) ?? 0) >=
                  widget.index) {
                push(
                  NamedRoute.masterClassDetailsScreen,
                  arguments: MasterClassDetailsScreenArguments(
                    masterClass: masterClass,
                    selectedMasterClass: widget.masterClass,
                    onVideoProgress: widget.onVideoProgress,
                    videoPercentage: widget.videoPercentage,
                  ),
                );
              } else {
                showSnackbar(
                    'To unlock this Class, please complete at least ${(widget.videoPercentage['videoPercentage'] * 100).toInt()}% of the previous videos.');
              }
            },
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: CachedImageWidget(
                    widget.masterClass.thumbnail,
                    boxFit: BoxFit.cover,
                    width: dW,
                    height: dW * 0.5,
                  ),
                ),
                Positioned(
                  top: dW * 0.175,
                  left: 0,
                  right: dW * 0.03,
                  child: Column(
                    children: [
                      AssetSvgIcon(
                        (int.tryParse(widget.user.masterClassCount) ?? 0) >=
                                widget.index
                            ? 'play_in_circle_yoga'
                            : 'locked',
                      ),
                      SizedBox(height: dW * 0.02),
                      Text(
                        (int.tryParse(widget.user.masterClassCount) ?? 0) >=
                                widget.index
                            ? language['start']
                            : language['locked'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFFFFFFFF),
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.masterClass.video.isEmpty)
                  Positioned(
                    top: dW * 0.03,
                    right: dW * 0.03,
                    child: const AssetSvgIcon(
                      's_lock',
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.04),
          Text(
            widget.masterClass.name,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 14,
              color: const Color(0XFF1D1E22),
            ),
          ),
          SizedBox(height: dW * 0.025),
          Text(
            widget.masterClass.duration,
            style: textTheme.titleSmall!.copyWith(
              fontSize: tS * 12,
              color: const Color(0XFF6B6C75),
            ),
          ),
          SizedBox(height: dW * 0.07),
        ],
      ),
    );
  }
}
