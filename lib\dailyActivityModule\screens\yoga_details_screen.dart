// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, must_be_immutable, unrelated_type_equality_checks

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_widget.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../dynamic_Link_api.dart';
import '../../homeModule/widgets/custom_video_player.dart';
import '../../navigation/arguments.dart';
import '../model/daily_activity_model.dart';
import '../widgets/yoga_widget.dart';

class YogaDetailsScreen extends StatefulWidget {
  final YogaDetailsArguments args;

  const YogaDetailsScreen({super.key, required this.args});

  @override
  State<YogaDetailsScreen> createState() => _YogaDetailsScreenState();
}

class _YogaDetailsScreenState extends State<YogaDetailsScreen> {
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  TextTheme get textTheme => Theme.of(bContext).textTheme;

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final filteredYoga = widget.args.yoga
        .where((y) =>
            y.trimester.contains(int.parse(widget.args.trimester)) &&
            y.id != widget.args.selectedYoga.id)
        .toList();

    return Scaffold(
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            SafeArea(
              child: Stack(
                children: [
                  CustomVideoPlayer(link: widget.args.selectedYoga.video),
                  Positioned(
                      left: 2,
                      top: 1,
                      child: Row(
                        children: [
                          const BackButton(),
                          Text(
                            language['back'],
                            style: Theme.of(context)
                                .textTheme
                                .headline2!
                                .copyWith(fontSize: tS * 12, color: blackColor),
                          ),
                        ],
                      )),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: dW * 0.05,
                  right: dW * 0.05,
                  top: dW * 0.08,
                  bottom: dW * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GradientWidget(
                    gradient: LinearGradient(colors: gradientColors),
                    child: Text(
                      '${trimesterText(int.parse(widget.args.trimester))} ' +
                          language['trimester'],
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 12,
                      ),
                    ),
                  ),
                  SizedBox(height: dW * 0.02),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          widget.args.selectedYoga.name,
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 14,
                            color: const Color(0XFF1D1E22),
                          ),
                        ),
                      ),
                      SizedBox(width: dW * 0.1),
                      GestureDetector(
                        onTap: () => createActivityLink(
                            widget.args.selectedYoga.id, 'Yoga'),
                        child: Container(
                          color: transparentColor,
                          child: Column(
                            children: [
                              const AssetSvgIcon('share',
                                  color: Color(0XFF6B6C75)),
                              SizedBox(height: dW * 0.01),
                              Text(
                                language['share'],
                                style: textTheme.headline2!.copyWith(
                                  fontSize: tS * 10,
                                  color: const Color(0XFF6B6C75),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  // SizedBox(height: dW * 0.02),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //   children: [
                  //     Row(
                  //       children: [
                  //         const AssetSvgIcon('views', color: Color(0XFF6B6C75)),
                  //         SizedBox(width: dW * 0.01),
                  //         Text(
                  //           widget.args.selectedYoga.views,
                  //           style: textTheme.headline2!.copyWith(
                  //             fontSize: tS * 10,
                  //             color: const Color(0XFF6B6C75),
                  //           ),
                  //         ),
                  //         SizedBox(width: dW * 0.005),
                  //         Text(
                  //           language['views'],
                  //           style: textTheme.headline2!.copyWith(
                  //             fontSize: tS * 10,
                  //             color: const Color(0XFF6B6C75),
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //   ],
                  // ),
                  SizedBox(height: dW * 0.2),
                  if (filteredYoga.isNotEmpty)
                    Row(
                      children: [
                        const AssetSvgIcon(
                          'more_yoga',
                          color: Color(0XFFFF328B),
                        ),
                        SizedBox(width: dW * 0.03),
                        Text(
                          '${language['moreYoga']} '
                                  '${trimesterText(int.parse(widget.args.trimester))} ' +
                              language['trimester'],
                          style: textTheme.headline2!.copyWith(
                            fontSize: tS * 14,
                            color: const Color(0XFF1D1E22),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            for (Yoga yoga in filteredYoga)
              YogaWidget(
                yoga: yoga,
                selectedTrimester: int.parse(widget.args.trimester),
              ),
          ],
        ),
      ),
    );
  }
}
