// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../widgets/custom_video_player.dart';

class GbVideoDetailScreen extends StatefulWidget {
  final GbVideoDetailScreenArguments args;
  const GbVideoDetailScreen({super.key, required this.args});

  @override
  State<GbVideoDetailScreen> createState() => _GbVideoDetailScreenState();
}

class _GbVideoDetailScreenState extends State<GbVideoDetailScreen> {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
      appBar: CustomAppBar(title: language['grbSnskr'], dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return Column(
      children: [
        CustomVideoPlayer(link: widget.args.article.videoUrl),
        Container(
          width: dW,
          padding: screenHorizontalPadding(dW, verticalF: 0.06),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                language['grbSnskr'],
                style: textTheme.headline2!.copyWith(
                  fontSize: tS * 10,
                  color: const Color(0xFFCE1B69),
                ),
              ),
              SizedBox(height: dW * 0.03),
              Text(
                widget.args.article.title,
                style: textTheme.headline1!.copyWith(
                  fontSize: tS * 14,
                  color: lightBlack,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
