// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';

class DefaultPreferenceWidget extends StatelessWidget {
  final Preferences preference;
  DefaultPreferenceWidget({super.key, required this.preference});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      margin: EdgeInsets.only(bottom: dW * 0.05),
      child: Row(
        children: [
          CachedNetworkImage(
            repeat: ImageRepeat.repeat,
            fit: BoxFit.cover,
            width: 24,
            height: 24,
            imageUrl: preference.dailyActivity.icon,
            placeholder: (_, __) => Image.asset(
                'assets/placeholders/placeholder.png',
                fit: BoxFit.cover),
          ),
          Padding(
            padding: EdgeInsets.only(left: dW * 0.035),
            child: Text(
              preference.dailyActivity.title,
              style: textTheme.headline2!.copyWith(
                fontSize: tS * 11,
                color: blackColor,
              ),
            ),
          ),
          const Spacer(),
          Container(
            padding: EdgeInsets.symmetric(
              vertical: dW * 0.02,
              horizontal: dW * 0.035,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F8FC),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const AssetSvgIcon(
                  'timer',
                  color: blackColor,
                  height: 10,
                  width: 10,
                ),
                SizedBox(width: dW * 0.015),
                Text(
                  convertToTime(preference.timeSlots[0]),
                  style: textTheme.headline2!.copyWith(
                    fontSize: tS * 10,
                    color: blackColor,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
