// ignore_for_file: deprecated_member_use, unused_local_variable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:nine_and_beyond/homeModule/models/article_model.dart';
import 'package:nine_and_beyond/homeModule/providers/home_provider.dart';
import 'package:nine_and_beyond/homeModule/widgets/ask_anything_widget.dart';
import 'package:nine_and_beyond/homeModule/widgets/facts_widget.dart';
import 'package:nine_and_beyond/homeModule/widgets/testimonial_widget_shimmer.dart';
import 'package:nine_and_beyond/homeModule/widgets/upcomming_activity_widget_shimmer.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../chatGPTModule/screens/chat_gpt_screen.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../dailyActivityModule/model/daily_activity_model.dart';
import '../../dailyActivityModule/providers/daily_activity_provider.dart';
import '../../navigation/arguments.dart';
import '../widgets/ask_anything_widget_shimmer.dart';
import '../widgets/baby_tracker_widget.dart';
import '../widgets/baby_tracker_widget_shimmer.dart';
import '../widgets/banner_widget.dart';
import '../widgets/facts_widget_shimmer.dart';
import '../widgets/faq_widget.dart';
import '../widgets/faq_widget_shimmer.dart';
import '../widgets/lesser_known_widget.dart';
import '../widgets/lesser_known_widget_shimmer.dart';
import '../widgets/locked_activities_widget.dart';
import '../widgets/master_class_banner_widget.dart';
import '../widgets/recommended_article_widget.dart';
import '../widgets/garbh_sanskar_widget.dart';
import '../widgets/recommended_article_widget_shimmer.dart';
import '../widgets/testimonial_widget.dart';
import '../widgets/upcoming_activity_widget.dart';

class HomeScreen extends StatefulWidget {
  final Function changeIndex;
  const HomeScreen({super.key, required this.changeIndex});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  bool isLoading = false;
  late User user;

  bool showGarbhSanskarAlways = false;
  bool blockDailyActivities = false;

  List<Article> recommendedArticles = [];
  List<Facts> facts = [];
  List<Testimonial> testimonials = [];
  List<Faq> faqs = [];
  List<Facts> lesserKnowns = [];
  List<Preferences> preferences = [];

  bool isPreferenceLoading = true;
  bool isArticleLoading = true;
  bool isBabyTrackersLoading = true;
  bool isFactsLoading = true;
  bool isLesserKnownsLoading = true;
  bool isTestimonialsLoading = true;
  bool isFaqsLoading = true;

  fetchPreference() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .preference
        .isEmpty) {
      setState(() {
        isPreferenceLoading = true;
      });
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchPreferences(
      accessToken: user.accessToken,
    );
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
    setState(() => isPreferenceLoading = false);
  }

  fetchRecommendedArticles() async {
    if (Provider.of<HomeProvider>(context, listen: false)
        .recommendedArticles
        .isEmpty) {
      setState(() {
        isArticleLoading = true;
      });
    }
    final response =
        await Provider.of<HomeProvider>(context, listen: false).fetchArticles(
      accessToken: user.accessToken,
      query:
          '?week=${user.pregnancyWeek}&month=${user.pregnancyMonth}&trimester=${user.pregnancyTrimester}',
    );
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
    setState(() => isArticleLoading = false);
  }

  fetchBabyTrackerWeek() async {
    if (Provider.of<HomeProvider>(context, listen: false)
            .getBabyDataByWeek(user.pregnancyWeek) ==
        null) {
      setState(() => isBabyTrackersLoading = true);
      final response = await Provider.of<HomeProvider>(context, listen: false)
          .fetchBabyTrackerWeek(
        accessToken: user.accessToken,
        weeks: [user.pregnancyWeek],
      );

      // if (!response['success']) {
      //   showSnackbar(language[response['message']]);
      // }
    }
    setState(() => isBabyTrackersLoading = false);
  }

  fetchFacts() async {
    if (Provider.of<HomeProvider>(context, listen: false).facts.isEmpty) {
      setState(() {
        isFactsLoading = true;
      });
    }
    final response =
        await Provider.of<HomeProvider>(context, listen: false).fetchFacts(
      accessToken: user.accessToken,
      query:
          '?week=${user.pregnancyWeek}&month=${user.pregnancyMonth}&trimester=${user.pregnancyTrimester}',
    );
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
    setState(() => isFactsLoading = false);
  }

  fetchLesserKnown() async {
    if (Provider.of<HomeProvider>(context, listen: false)
        .lesserKnowns
        .isEmpty) {
      setState(() {
        isLesserKnownsLoading = true;
      });
    }
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .fetchLesserKnown(
      accessToken: user.accessToken,
      query:
          '?week=${user.pregnancyWeek}&month=${user.pregnancyMonth}&trimester=${user.pregnancyTrimester}',
    );
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
    setState(() => isLesserKnownsLoading = false);
  }

  fetchTestimonial() async {
    if (Provider.of<HomeProvider>(context, listen: false)
        .testimonials
        .isEmpty) {
      setState(() {
        isTestimonialsLoading = true;
      });
    }
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .fetchTestimonial(
      accessToken: user.accessToken,
      query:
          '?week=${user.pregnancyWeek}&month=${user.pregnancyMonth}&trimester=${user.pregnancyTrimester}',
    );
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
    setState(() => isTestimonialsLoading = false);
  }

  fetchFaq() async {
    if (Provider.of<HomeProvider>(context, listen: false).faqs.isEmpty) {
      setState(() {
        isFaqsLoading = true;
      });
    }
    final response =
        await Provider.of<HomeProvider>(context, listen: false).fetchFaq(
      accessToken: user.accessToken,
      query:
          '?week=${user.pregnancyWeek}&month=${user.pregnancyMonth}&trimester=${user.pregnancyTrimester}',
    );
    // if (!response['success']) {
    //   showSnackbar(response['message']);
    // }
    setState(() => isFaqsLoading = false);
  }

  // logout() async {
  //   Provider.of<AuthProvider>(context, listen: false).logout();
  //   pushAndRemoveUntil(NamedRoute.loginScreen);
  // }

  myInit() async {
    await fetchPreference();
    await fetchRecommendedArticles();
    if (user.pregnancyWeek != 0) {
      await fetchBabyTrackerWeek();
    }
    await fetchFacts();
    await fetchLesserKnown();
    await fetchTestimonial();
    await fetchFaq();
  }

  setLoaders() {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .preference
        .isNotEmpty) {
      isPreferenceLoading = false;
    }
    if (Provider.of<HomeProvider>(context, listen: false)
        .recommendedArticles
        .isNotEmpty) {
      isArticleLoading = false;
    }
    if (Provider.of<HomeProvider>(context, listen: false)
            .getBabyDataByWeek(user.pregnancyWeek) !=
        null) {
      isBabyTrackersLoading = false;
    }
    if (Provider.of<HomeProvider>(context, listen: false).facts.isNotEmpty) {
      isFactsLoading = false;
    }
    if (Provider.of<HomeProvider>(context, listen: false)
        .lesserKnowns
        .isNotEmpty) {
      isLesserKnownsLoading = false;
    }
    if (Provider.of<HomeProvider>(context, listen: false)
        .testimonials
        .isNotEmpty) {
      isTestimonialsLoading = false;
    }
    if (Provider.of<HomeProvider>(context, listen: false).faqs.isNotEmpty) {
      isFaqsLoading = false;
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    showGarbhSanskarAlways = Provider.of<AuthProvider>(context, listen: false)
        .showGarbhSanskarAlways;
    blockDailyActivities =
        Provider.of<AuthProvider>(context, listen: false).blockDailyActivities;
    setLoaders();
    myInit();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    recommendedArticles =
        Provider.of<HomeProvider>(context).recommendedArticles;
    facts = Provider.of<HomeProvider>(context).facts;
    testimonials = Provider.of<HomeProvider>(context).testimonials;
    faqs = Provider.of<HomeProvider>(context).faqs;
    lesserKnowns = Provider.of<HomeProvider>(context).lesserKnowns;
    preferences = Provider.of<DailyActivityProvider>(context).preference;

    user = Provider.of<AuthProvider>(context).user;

    return screenBody();
  }

  screenBody() {
    return Scaffold(
      floatingActionButton: GestureDetector(
        onTap: () => widget.changeIndex(3),
        child: Container(
          padding: EdgeInsets.all(dW * 0.03),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(26),
              gradient: const LinearGradient(colors: [
                Color(0XFF019FED),
                Color(0XFF0036B4),
              ])),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: dW * 0.015,
                  right: dW * 0.01,
                ),
                child: Text(
                  language['record'],
                  style: Theme.of(context).textTheme.headline1!.copyWith(
                        fontSize: tS * 14,
                        color: const Color(0xFFFFFFFF),
                      ),
                ),
              ),
              const Icon(
                Icons.mic_none_outlined,
                color: Color(0xFFFFFFFF),
                size: 19,
              )
            ],
          ),
        ),
      ),
      body: Container(
        color: const Color(0XFFF5F7FD),
        height: dH,
        width: dW,
        child: Stack(
          children: [
            Image.asset('assets/images/home_bg.png', width: dW),
            Column(
              children: [
                SafeArea(
                  child: Container(
                    margin: EdgeInsets.only(
                      top: dW * (iOSCondition(dH) ? 0.03 : 0.07),
                      bottom: dW * 0.025,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: dW * 0.05),
                    child: Row(
                      children: [
                        user.avatar.isNotEmpty
                            ? Container(
                                decoration: const BoxDecoration(
                                  color: Color(0XFF975EFF),
                                  shape: BoxShape.circle,
                                ),
                                padding: const EdgeInsets.all(2),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(100),
                                  child: CachedImageWidget(
                                    user.avatar,
                                    boxFit: BoxFit.cover,
                                    width: 40,
                                    height: 40,
                                  ),
                                ),
                              )
                            : Image.asset(
                                'assets/images/hp_lady.png',
                                width: 40,
                                height: 40,
                              ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                language['welcomeBack'],
                                style: Theme.of(context)
                                    .textTheme
                                    .headline3!
                                    .copyWith(
                                      fontSize: tS * 9,
                                      color: const Color(0xFF515259),
                                    ),
                              ),
                              SizedBox(height: dW * 0.01),
                              Text(
                                user.fullName,
                                style: Theme.of(context)
                                    .textTheme
                                    .headline2!
                                    .copyWith(
                                      fontSize: tS * 12.5,
                                      color: lightBlack,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        if (user.plan == null) const BannerWidget(),
                        if (!user.completedWalkThrough ||
                            showGarbhSanskarAlways)
                          GarbhSanskarWidget(),
                        if (!user.completedWalkThrough && blockDailyActivities)
                          LockedActivitiesWidget(),
                        if (user.completedWalkThrough)
                          isPreferenceLoading
                              ? UpcommingActivityWidgetShimmer()
                              : Provider.of<DailyActivityProvider>(context)
                                          .upcomingActivity !=
                                      null
                                  ? const UpcomingActivityWidget()
                                  : const SizedBox.shrink(),
                        if (user.plan != null) const MasterClassBannerWidget(),
                        isArticleLoading
                            ? RecommendedArticleWidgetShimmer()
                            : recommendedArticles.isNotEmpty
                                ? Container(
                                    margin: EdgeInsets.only(top: dW * 0.05),
                                    child: RecommendedArticleWidget(
                                        recommendedArticles:
                                            recommendedArticles),
                                  )
                                : const SizedBox.shrink(),
                        isBabyTrackersLoading
                            ? BabyTrackerWidgetShimmer()
                            : !isBabyTrackersLoading &&
                                    user.pregnancyWeek != 0 &&
                                    Provider.of<HomeProvider>(context)
                                            .getBabyDataByWeek(
                                                user.pregnancyWeek) !=
                                        null
                                ? Container(
                                    margin: EdgeInsets.only(top: dW * 0.05),
                                    child: BabyTrackerWidget())
                                : const SizedBox.shrink(),
                        // if (!isBabyTrackersLoading &&
                        //     user.pregnancyWeek != 0 &&
                        //     Provider.of<HomeProvider>(context)
                        //             .getBabyDataByWeek(user.pregnancyWeek) !=
                        //         null) ...[
                        //   SizedBox(height: dW * 0.05),
                        //   BabyTrackerWidget(),
                        // ],
                        isFactsLoading
                            ? FactsWidgetShimmer()
                            : facts.isNotEmpty
                                ? Container(
                                    margin: EdgeInsets.only(top: dW * 0.05),
                                    child: FactsWidget(facts: facts))
                                : const SizedBox.shrink(),
                        // if (facts.isNotEmpty) ...[
                        //   SizedBox(height: dW * 0.05),
                        //   FactsWidget(facts: facts),
                        // ],
                        // SizedBox(height: dW * 0.01),
                        isFactsLoading
                            ? AskAnythingWidgetShimmer()
                            : GestureDetector(
                                onTap: () => Navigator.push(
                                    context,
                                    PageTransition(
                                      duration:
                                          const Duration(milliseconds: 500),
                                      child: ChatGptScreen(
                                        args: ChatGptScreenArguments(),
                                      ),
                                      type: PageTransitionType.bottomToTop,
                                    )),
                                // widget.changeIndex(2),
                                child: Container(
                                    margin: EdgeInsets.only(top: dW * 0.01),
                                    child: AskAnythingWidget())),
                        isLesserKnownsLoading
                            ? LesserKnownWidgetShimmer()
                            : lesserKnowns.isNotEmpty
                                ? LesserKnownWidget(lesserKnowns: lesserKnowns)
                                : const SizedBox.shrink(),
                        // if (lesserKnowns.isNotEmpty)
                        //   LesserKnownWidget(lesserKnowns: lesserKnowns),
                        isTestimonialsLoading
                            ? TestimonialWidgetShimmer()
                            : testimonials.isNotEmpty
                                ? Container(
                                    margin: EdgeInsets.only(top: dW * 0.03),
                                    child: TestimonialWidget(
                                        testimonials: testimonials),
                                  )
                                : const SizedBox.shrink(),
                        // if (testimonials.isNotEmpty) ...[
                        //   SizedBox(height: dW * 0.03),
                        //   TestimonialWidget(testimonials: testimonials),
                        // ],
                        isFaqsLoading
                            ? FaqWidgetShimmer()
                            : faqs.isNotEmpty
                                ? Container(
                                    margin: EdgeInsets.only(top: dW * 0.02),
                                    child: FaqWidget(faqs: faqs),
                                  )
                                : const SizedBox.shrink(),
                        // if (faqs.isNotEmpty) ...[
                        //   SizedBox(height: dW * 0.02),
                        //   FaqWidget(faqs: faqs),
                        // ],
                        SizedBox(height: dW * 0.05),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
