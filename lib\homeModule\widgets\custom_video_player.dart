// import 'package:better_player/better_player.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import '../../commonWidgets/circular_loader.dart';
// import 'custom_player_control.dart';

// class CustomVideoPlayer extends StatefulWidget {
//   final String link;
//   final Function? toggleContentVisibility;
//   final bool fromMeditaion;

//   const CustomVideoPlayer(
//       {Key? key,
//       required this.link,
//       this.toggleContentVisibility,
//       this.fromMeditaion = false})
//       : super(key: key);

//   @override
//   CustomVideoPlayerState createState() => CustomVideoPlayerState();
// }

// class CustomVideoPlayerState extends State<CustomVideoPlayer> {
//   double dW = 0.0;
//   double dH = 0.0;
//   double tS = 0.0;

//   bool isLoading = false;
//   BetterPlayerController? _controller;

//   Orientation? _orientationBeforeFullScreen;

//   final List<DeviceOrientation> _portraitOrientation = [
//     DeviceOrientation.portraitUp,
//   ];
//   final List<DeviceOrientation> _landscapeOrientation = [
//     DeviceOrientation.landscapeLeft,
//     DeviceOrientation.landscapeRight
//   ];

//   orientationForIOS(event) async {
//     if (event.betterPlayerEventType == BetterPlayerEventType.openFullscreen) {
//       _orientationBeforeFullScreen = MediaQuery.of(context).orientation;
//       await SystemChrome.setPreferredOrientations(_landscapeOrientation);
//     } else if (event.betterPlayerEventType ==
//         BetterPlayerEventType.hideFullscreen) {
//       if (_orientationBeforeFullScreen == Orientation.portrait) {
//         await SystemChrome.setPreferredOrientations(_landscapeOrientation);
//       }
//       if (_orientationBeforeFullScreen == Orientation.landscape) {
//         await SystemChrome.setPreferredOrientations(_portraitOrientation);
//       }
//     }
//   }

//   setController() async {
//     try {
//       await SystemChrome.setPreferredOrientations([
//         DeviceOrientation.landscapeLeft,
//         DeviceOrientation.landscapeRight,
//         DeviceOrientation.portraitUp,
//       ]);
//       setState(() => isLoading = true);

//       _controller = BetterPlayerController(
//         BetterPlayerConfiguration(
//           autoDispose: true,
//           aspectRatio: widget.fromMeditaion ? 10 / 9 : 16 / 9,
//           looping: true,
//           autoPlay: true,
//           handleLifecycle: true,
//           // deviceOrientationsOnFullScreen: [
//           //   DeviceOrientation.landscapeLeft,
//           //   DeviceOrientation.landscapeRight
//           // ],
//           deviceOrientationsAfterFullScreen: [DeviceOrientation.portraitUp],
//           controlsConfiguration: BetterPlayerControlsConfiguration(
//             controlsHideTime: const Duration(seconds: 1),
//             playerTheme: BetterPlayerTheme.custom,
//             customControlsBuilder:
//                 (videoController, onPlayerVisibilityChanged) =>
//                     CustomPlayerControl(
//               controller: videoController,
//               toggleContentVisibility: widget.toggleContentVisibility,
//             ),
//             // showControlsOnInitialize: widget.fromMeditaion ? false : true,
//             // showControls: widget.fromMeditaion ? false : true,
//           ),
//         ),
//         betterPlayerDataSource: BetterPlayerDataSource(
//             BetterPlayerDataSourceType.network, widget.link),
//       );

//       // if (Platform.isIOS) {
//       //   _controller!.addEventsListener((event) async {
//       //     await orientationForIOS(event);
//       //   });
//       // }
//     } catch (e) {
//       print(e);
//     } finally {
//       if (mounted) setState(() => isLoading = false);
//     }
//   }

//   @override
//   void initState() {
//     super.initState();
//     setController();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     if (_controller != null) {
//       _controller!.removeEventsListener((p0) => orientationForIOS(p0));
//       _controller!.dispose(forceDispose: true);
//       _controller!.dispose();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     dH = MediaQuery.of(context).size.height;
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;

//     _orientationBeforeFullScreen = MediaQuery.of(context).orientation;

//     return SizedBox(
//       // width: dW,
//       // height: dH,
//       child: !isLoading && _controller != null
//           ? BetterPlayer(controller: _controller!)
//           : const Center(child: CircularLoader()),
//     );
//   }
// }

// import 'package:better_player/better_player.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import '../../commonWidgets/circular_loader.dart';
// import 'custom_player_control.dart';

// class CustomVideoPlayer extends StatefulWidget {
//   final String link;
//   final Function? toggleContentVisibility;
//   final bool showControlsOnVideo;
//   final Function(Duration)? onPositionChanged;
//   final Function(Duration)? onDurationChanged;

//   const CustomVideoPlayer({
//     Key? key,
//     required this.link,
//     this.toggleContentVisibility,
//     this.showControlsOnVideo = true,
//     this.onPositionChanged,
//     this.onDurationChanged,
//   }) : super(key: key);

//   @override
//   CustomVideoPlayerState createState() => CustomVideoPlayerState();
// }

// class CustomVideoPlayerState extends State<CustomVideoPlayer> {
//   double dW = 0.0;
//   double dH = 0.0;
//   double tS = 0.0;

//   bool isLoading = false;
//   BetterPlayerController? _controller;

//   Orientation? _orientationBeforeFullScreen;

//   final List<DeviceOrientation> _portraitOrientation = [
//     DeviceOrientation.portraitUp,
//   ];
//   final List<DeviceOrientation> _landscapeOrientation = [
//     DeviceOrientation.landscapeLeft,
//     DeviceOrientation.landscapeRight
//   ];

//   orientationForIOS(event) async {
//     if (event.betterPlayerEventType == BetterPlayerEventType.openFullscreen) {
//       _orientationBeforeFullScreen = MediaQuery.of(context).orientation;
//       await SystemChrome.setPreferredOrientations(_landscapeOrientation);
//     } else if (event.betterPlayerEventType ==
//         BetterPlayerEventType.hideFullscreen) {
//       if (_orientationBeforeFullScreen == Orientation.portrait) {
//         await SystemChrome.setPreferredOrientations(_landscapeOrientation);
//       }
//       if (_orientationBeforeFullScreen == Orientation.landscape) {
//         await SystemChrome.setPreferredOrientations(_portraitOrientation);
//       }
//     }
//   }

//   setController() async {
//     try {
//       await SystemChrome.setPreferredOrientations([
//         DeviceOrientation.landscapeLeft,
//         DeviceOrientation.landscapeRight,
//         DeviceOrientation.portraitUp,
//       ]);
//       setState(() => isLoading = true);

//       _controller = BetterPlayerController(
//         BetterPlayerConfiguration(
//           autoDispose: true,
//           aspectRatio: widget.showControlsOnVideo == false ? 10 / 9 : 16 / 9,
//           looping: true,
//           autoPlay: true,
//           handleLifecycle: true,
//           deviceOrientationsAfterFullScreen: [DeviceOrientation.portraitUp],
//           controlsConfiguration: BetterPlayerControlsConfiguration(
//             controlsHideTime: const Duration(seconds: 1),
//             playerTheme: BetterPlayerTheme.custom,
//             customControlsBuilder:
//                 (videoController, onPlayerVisibilityChanged) =>
//                     CustomPlayerControl(
//               controller: videoController,
//               toggleContentVisibility: widget.toggleContentVisibility,
//             ),
//             showControlsOnInitialize: widget.showControlsOnVideo,
//             showControls: widget.showControlsOnVideo,
//           ),
//         ),
//         betterPlayerDataSource: BetterPlayerDataSource(
//             BetterPlayerDataSourceType.network, widget.link),
//       );

//       _controller?.addEventsListener((event) {
//         if (event.betterPlayerEventType == BetterPlayerEventType.progress) {
//           if (widget.onPositionChanged != null) {
//             widget
//                 .onPositionChanged!(event.parameters!['progress'] as Duration);
//           }
//           if (widget.onDurationChanged != null) {
//             widget
//                 .onDurationChanged!(event.parameters!['duration'] as Duration);
//           }
//         }
//       });
//     } catch (e) {
//       print(e);
//     } finally {
//       if (mounted) setState(() => isLoading = false);
//     }
//   }

//   @override
//   void initState() {
//     super.initState();
//     setController();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     if (_controller != null) {
//       _controller!.removeEventsListener((p0) => orientationForIOS(p0));
//       _controller!.dispose(forceDispose: true);
//       _controller!.dispose();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     dH = MediaQuery.of(context).size.height;
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;

//     _orientationBeforeFullScreen = MediaQuery.of(context).orientation;

//     return SizedBox(
//       child: !isLoading && _controller != null
//           ? BetterPlayer(controller: _controller!)
//           : const Center(child: CircularLoader()),
//     );
//   }
// }

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../commonWidgets/circular_loader.dart';
import 'custom_player_control.dart';

class CustomVideoPlayer extends StatefulWidget {
  final String link;
  final Function? toggleContentVisibility;
  final bool showControlsOnVideo;
  final Function(Duration)? onPositionChanged;
  final Function(Duration)? onDurationChanged;
  final Function(BetterPlayerController)? onControllerInitialized;

  const CustomVideoPlayer({
    Key? key,
    required this.link,
    this.toggleContentVisibility,
    this.showControlsOnVideo = true,
    this.onPositionChanged,
    this.onDurationChanged,
    this.onControllerInitialized,
  }) : super(key: key);

  @override
  CustomVideoPlayerState createState() => CustomVideoPlayerState();
}

class CustomVideoPlayerState extends State<CustomVideoPlayer> {
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;

  bool isLoading = false;
  BetterPlayerController? _controller;

  Orientation? _orientationBeforeFullScreen;

  final List<DeviceOrientation> _portraitOrientation = [
    DeviceOrientation.portraitUp,
  ];
  final List<DeviceOrientation> _landscapeOrientation = [
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight
  ];

  orientationForIOS(event) async {
    if (event.betterPlayerEventType == BetterPlayerEventType.openFullscreen) {
      _orientationBeforeFullScreen = MediaQuery.of(context).orientation;
      await SystemChrome.setPreferredOrientations(_landscapeOrientation);
    } else if (event.betterPlayerEventType ==
        BetterPlayerEventType.hideFullscreen) {
      if (_orientationBeforeFullScreen == Orientation.portrait) {
        await SystemChrome.setPreferredOrientations(_landscapeOrientation);
      }
      if (_orientationBeforeFullScreen == Orientation.landscape) {
        await SystemChrome.setPreferredOrientations(_portraitOrientation);
      }
    }
  }

  setController() async {
    try {
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
        DeviceOrientation.portraitUp,
      ]);
      setState(() => isLoading = true);

      _controller = BetterPlayerController(
        BetterPlayerConfiguration(
          autoDispose: true,
          aspectRatio: widget.showControlsOnVideo == false ? 10 / 9 : 16 / 9,
          looping: true,
          autoPlay: true,
          handleLifecycle: true,
          deviceOrientationsAfterFullScreen: [DeviceOrientation.portraitUp],
          controlsConfiguration: BetterPlayerControlsConfiguration(
            controlsHideTime: const Duration(seconds: 1),
            playerTheme: BetterPlayerTheme.custom,
            customControlsBuilder:
                (videoController, onPlayerVisibilityChanged) =>
                    CustomPlayerControl(
              controller: videoController,
              toggleContentVisibility: widget.toggleContentVisibility,
            ),
            showControlsOnInitialize: widget.showControlsOnVideo,
            showControls: widget.showControlsOnVideo,
          ),
        ),
        betterPlayerDataSource: BetterPlayerDataSource(
            BetterPlayerDataSourceType.network, widget.link),
      );

      _controller?.addEventsListener((event) {
        if (event.betterPlayerEventType == BetterPlayerEventType.progress) {
          if (widget.onPositionChanged != null) {
            widget
                .onPositionChanged!(event.parameters!['progress'] as Duration);
          }
          if (widget.onDurationChanged != null) {
            widget
                .onDurationChanged!(event.parameters!['duration'] as Duration);
          }
        }
      });
      if (widget.onControllerInitialized != null) {
        widget.onControllerInitialized!(_controller!);
      }
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();
    setController();
  }

  @override
  void dispose() {
    super.dispose();
    if (_controller != null) {
      _controller!.removeEventsListener((p0) => orientationForIOS(p0));
      _controller!.dispose(forceDispose: true);
      _controller!.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;

    _orientationBeforeFullScreen = MediaQuery.of(context).orientation;

    return SizedBox(
      child: !isLoading && _controller != null
          ? BetterPlayer(controller: _controller!)
          : const Center(child: CircularLoader()),
    );
  }
}
