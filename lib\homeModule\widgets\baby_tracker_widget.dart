// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/dotted_seperator.dart';
import 'package:nine_and_beyond/homeModule/providers/home_provider.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../common_functions.dart';
import '../models/baby_tracker_model.dart';

class BabyTrackerWidget extends StatelessWidget {
  BabyTrackerWidget({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    final User user = Provider.of<AuthProvider>(context).user;

    final BabyTracker babyData =
        Provider.of<HomeProvider>(context, listen: false)
            .getBabyDataByWeek(user.pregnancyWeek)!;

    return Container(
      width: dW,
      color: white,
      padding: EdgeInsets.only(
        top: dW * 0.07,
        bottom: dW * 0.05,
        left: dW * 0.04,
        right: dW * 0.04,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            language['babyTracker'],
            style: Theme.of(context).textTheme.headline1!.copyWith(
                  fontSize: tS * 14,
                  color: const Color(0XFF1D1E22),
                ),
          ),
          GestureDetector(
            onTap: () => push(NamedRoute.babyTrackerScreen),
            child: Container(
              width: dW,
              margin: EdgeInsets.only(top: dW * 0.03),
              padding: EdgeInsets.all(dW * 0.04),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F7FD),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 1, color: const Color(0xFFD6EAFF)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    padding: const EdgeInsets.all(0),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.1),
                        gradient: const LinearGradient(
                          colors: [
                            Color(0xFFFFE6F2),
                            Color(0xFFFAE3E2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: CachedImageWidget(babyData.weeklyImage),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: dW * 0.05),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${language['bYouAre']} ${babyData.week} ${(babyData.week == 1 ? language['week'] : language['weeks']).toLowerCase()} ${(language['pregnant']).toLowerCase()}',
                          style: textTheme.headline2!.copyWith(
                            fontSize: tS * 14,
                            color: lightBlack,
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.symmetric(vertical: dW * 0.03),
                          width: dW * 0.55,
                          child: const DottedSeperator(
                            width: 3,
                            height: 0.8,
                            color: Color(0xFFDBDBE3),
                          ),
                        ),
                        Text(
                          language['seeBabyGrowth'],
                          style: textTheme.headline3!.copyWith(
                            fontSize: tS * 10,
                            color: lightBlack,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
