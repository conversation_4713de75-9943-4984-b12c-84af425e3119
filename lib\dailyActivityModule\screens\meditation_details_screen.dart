// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, must_be_immutable, unrelated_type_equality_checks

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_widget.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../homeModule/widgets/custom_video_player.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';

class MeditationDetailsScreen extends StatefulWidget {
  final MeditationDetailsArguments args;

  const MeditationDetailsScreen({super.key, required this.args});

  @override
  State<MeditationDetailsScreen> createState() =>
      _MeditationDetailsScreenState();
}

class _MeditationDetailsScreenState extends State<MeditationDetailsScreen> {
  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  TextTheme get textTheme => Theme.of(bContext).textTheme;

  BetterPlayerController? _controller;

  Duration currentPos = Duration.zero;
  Duration duration = Duration.zero;

  bool isPlaying = true;

  playPause() {
    if (_controller != null) {
      if (isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
      setState(() {
        isPlaying = !isPlaying;
      });
    }
  }

  seekToDuration(double value) {
    setState(() {
      currentPos = Duration(milliseconds: value.toInt());
    });
    _controller?.seekTo(currentPos);
  }

  seekForward() {
    if (_controller != null) {
      final newPosition = currentPos + const Duration(seconds: 10);
      _controller!.seekTo(newPosition);
      setState(() {
        currentPos = newPosition;
      });
    }
  }

  seekBackward() {
    if (_controller != null) {
      final newPosition = currentPos - const Duration(seconds: 10);
      _controller!.seekTo(newPosition);
      setState(() {
        currentPos = newPosition;
      });
    }
  }

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    String currentMinutes = currentPos.inMinutes.remainder(60).toString();
    String currentSeconds = currentPos.inSeconds.remainder(60).toString();
    String durationMinutes = duration.inMinutes.remainder(60).toString();
    String durationSeconds = duration.inSeconds.remainder(60).toString();

    return Scaffold(
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SafeArea(
              child: GestureDetector(
                onTap: pop,
                child: Container(
                    padding: EdgeInsets.only(left: dW * 0.05, top: dW * 0.05),
                    color: transparentColor,
                    child: const Icon(Icons.arrow_back)),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(dW * 0.05),
              child: CustomVideoPlayer(
                link: widget.args.selectedMeditation.video,
                showControlsOnVideo: false,
                onPositionChanged: (position) {
                  setState(() {
                    currentPos = position;
                  });
                },
                onDurationChanged: (dur) {
                  setState(() {
                    duration = dur;
                  });
                },
                onControllerInitialized: (controller) {
                  _controller = controller;
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: dW * 0.05, right: dW * 0.05, top: dW * 0.04),
              child: GradientWidget(
                gradient: LinearGradient(colors: gradientColors),
                child: Text(
                  '${trimesterText(int.parse(widget.args.trimester))} ' +
                      language['trimester'],
                  style: textTheme.headline2!.copyWith(
                    fontSize: tS * 12,
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: dW * 0.05,
                  right: dW * 0.05,
                  top: dW * 0.02,
                  bottom: dW * 0.1),
              child: Text(
                widget.args.selectedMeditation.name,
                style: textTheme.headline1!.copyWith(
                  fontSize: tS * 14,
                  color: const Color(0XFF1D1E22),
                ),
              ),
            ),
            Slider(
              activeColor: const Color(0xff975EFF),
              inactiveColor: const Color(0xffF4F4F4),
              value: currentPos.inMilliseconds
                  .toDouble()
                  .clamp(0, duration.inMilliseconds.toDouble()),
              onChanged: seekToDuration,
              max: duration.inMilliseconds.toDouble(),
            ),
            Padding(
              padding: EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
              child: Row(
                children: [
                  Text(
                    '$currentMinutes:${currentSeconds.toString().padLeft(2, '0')}',
                    style: textTheme.headline3!.copyWith(
                      fontSize: tS * 14,
                      color: const Color(0XFF515259),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '$durationMinutes:${durationSeconds.toString().padLeft(2, '0')}',
                    style: textTheme.headline3!.copyWith(
                      fontSize: tS * 14,
                      color: const Color(0XFF515259),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: dW * 0.05,
                right: dW * 0.05,
                top: dW * 0.1,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                      onTap: seekBackward,
                      child: AssetSvgIcon(
                        'backW',
                        height: dW * 0.08,
                      )),
                  GestureDetector(
                    onTap: playPause,
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.1),
                      height: dW * 0.15,
                      width: dW * 0.15,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: gradientColors),
                        borderRadius: BorderRadius.circular(108),
                      ),
                      child: AssetSvgIcon(
                        isPlaying ? 'pause' : 'play',
                        color: const Color(0XFFFFFFFF),
                      ),
                    ),
                  ),
                  GestureDetector(
                      onTap: seekForward,
                      child: AssetSvgIcon(
                        'forW',
                        height: dW * 0.08,
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
