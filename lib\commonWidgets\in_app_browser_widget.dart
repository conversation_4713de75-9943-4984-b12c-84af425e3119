// ignore_for_file: avoid_print

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_webview_pro/webview_flutter.dart';
import 'circular_loader.dart';

class InAppBrowserScreen extends StatefulWidget {
  const InAppBrowserScreen({super.key});

  @override
  State<InAppBrowserScreen> createState() => _InAppBrowserScreenState();
}

class _InAppBrowserScreenState extends State<InAppBrowserScreen> {
  bool isLoading = true;

  bool iOSCondition(double dH) => Platform.isIOS && dH > 850;

  WebViewController? controller;

  String link = 'https://pages.razorpay.com/9b-course/';

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    if (controller != null) {
      controller!.clearCache().catchError((error) {
        print("Error clearing cache: $error");
      });
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dH = MediaQuery.of(context).size.height;
    final dW = MediaQuery.of(context).size.width;

    return Scaffold(
      body: iOSCondition(dH) ? screenBody(dW) : SafeArea(child: screenBody(dW)),
    );
  }

  Widget screenBody(double dW) {
    return Stack(
      children: [
        Column(
          children: [
            Container(
              padding: EdgeInsets.all(dW * 0.04),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xffCE1B69),
                    const Color(0xffFF328B).withOpacity(0.71),
                  ],
                ),
              ),
              child: Row(
                children: [
                  Image.asset(
                    'assets/images/9B.png',
                    height: dW * 0.1,
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: EdgeInsets.all(dW * 0.01),
                      decoration: BoxDecoration(
                        color: const Color(0XFFF2F2F2),
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: Icon(
                        Icons.clear,
                        color: const Color(0XFF000000),
                        size: dW * 0.05,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: WebView(
                initialUrl: link,
                zoomEnabled: false,
                javascriptMode: JavascriptMode.unrestricted,
                onPageFinished: (String url) {
                  setState(() => isLoading = false);
                },
                onWebViewCreated: (WebViewController webViewController) {
                  controller = webViewController;
                },
              ),
            ),
          ],
        ),
        if (isLoading) const Center(child: CircularLoader()),
      ],
    );
  }
}
