// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/dailyActivityModule/widgets/raga_music_widget.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/activity_illustration_image.dart';
import '../widgets/audio_player_widget.dart';

class RagaMusicScreen extends StatefulWidget {
  final RagaMusicArguments args;
  const RagaMusicScreen({super.key, required this.args});

  @override
  State<RagaMusicScreen> createState() => _RagaMusicScreenState();
}

class _RagaMusicScreenState extends State<RagaMusicScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  int currentIndex = 0;
  bool playing = false;

  late RagaMusic playingRagaMusic;

  bool minimised = false;

  late User user;
  bool isLoading = false;

  List trimester = [1, 2, 3];

  int selectedTrimester = 1;

  List<RagaMusic> ragaMusic = [];

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  toggleAudioPlayer(bool val, [bool killPlayer = false]) {
    if (killPlayer) {
      setState(() {
        playing = false;
        minimised = false;
      });
      return;
    }
    setState(() {
      minimised = val;
    });
  }

  fetchRagaMusic() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .ragaMusic
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedRagaMusic(
      accessToken: user.accessToken,
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
    if (widget.args.ragaMusic != null) {
      final allRagaMusic =
          Provider.of<DailyActivityProvider>(context, listen: false).ragaMusic;
      int i = allRagaMusic
          .indexWhere((element) => element.id == widget.args.ragaMusic!.id);
      if (i != -1) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15.0),
              topRight: Radius.circular(15.0),
            ),
          ),
          builder: (BuildContext context) => AudioPlayerWidget(
            ragaMusic: allRagaMusic[i],
            currentIndex: i,
            selectedPeriod: selectedTrimester,
            minimised: minimised,
            toggleAudioPlayer: toggleAudioPlayer,
          ),
        );
      }
    }
  }

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    selectedTrimester = user.pregnancyTrimester.toInt();
    fetchRagaMusic();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    ragaMusic = Provider.of<DailyActivityProvider>(context)
        .getRagaMusicUsingTrimester(selectedTrimester);

    return Scaffold(
      body: Stack(
        alignment: AlignmentDirectional.bottomCenter,
        children: [
          SizedBox(
            height: dH,
            width: dW,
            child: Scaffold(
              backgroundColor: const Color(0XFFF7F8FC),
              appBar: CustomAppBar(title: widget.args.title, dW: dW),
              body: iOSCondition(dH)
                  ? screenBody()
                  : SafeArea(child: screenBody()),
            ),
          ),
          if (playing)
            AudioPlayerWidget(
              ragaMusic: playingRagaMusic,
              currentIndex: currentIndex,
              minimised: minimised,
              toggleAudioPlayer: toggleAudioPlayer,
              selectedPeriod: selectedTrimester,
            )
        ],
      ),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : SizedBox(
            height: dH,
            width: dW,
            child: Column(
              children: [
                SizedBox(height: dW * 0.04),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: dW * 0.08,
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            children: [
                              Row(
                                children: [
                                  ...trimester.map(
                                    (i) => GestureDetector(
                                      onTap: () {
                                        setState(
                                          () {
                                            selectedTrimester = i;
                                          },
                                        );
                                      },
                                      child: Container(
                                        margin:
                                            EdgeInsets.only(left: dW * 0.03),
                                        width: dW * 0.3,
                                        decoration: BoxDecoration(
                                          color: selectedTrimester == i
                                              ? const Color(0XFF975EFF)
                                              : const Color(0XFFFFFFFF),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        child: Center(
                                          child: Text(
                                            '${trimesterText(i)} ' +
                                                language['trimester'],
                                            style:
                                                textTheme.headline2!.copyWith(
                                              fontSize: tS * 12,
                                              color: selectedTrimester == i
                                                  ? const Color(0XFFFFFFFF)
                                                  : const Color(0XFF1D1E22),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.06),
                        Padding(
                          padding: EdgeInsets.only(
                              left: dW * 0.04, right: dW * 0.04),
                          child: Row(
                            children: [
                              const AssetSvgIcon(
                                'raga_music_rec',
                                color: Color(0XFFFF328B),
                              ),
                              SizedBox(width: dW * 0.02),
                              Text(
                                language['ragaMusicRec'],
                                style: textTheme.headline2!.copyWith(
                                  fontSize: tS * 12,
                                  color: const Color(0XFF1D1E22),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: dW * 0.05),
                        ListView.builder(
                          shrinkWrap: true,
                          itemCount: ragaMusic.length,
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, i) => GestureDetector(
                            onTap: () {
                              if (ragaMusic[i].audioVersions.isNotEmpty) {
                                setState(() {
                                  playingRagaMusic = ragaMusic[i];
                                  playing = true;
                                  currentIndex = i;
                                });
                              } else {
                                buySubscriptionDialog();
                              }
                            },
                            child: RagaMusicWidget(
                              playAudio: () {
                                setState(() {
                                  playingRagaMusic = ragaMusic[i];
                                  playing = true;
                                  currentIndex = i;
                                });
                              },
                              ragaMusic: ragaMusic[i],
                              activityTitle: widget.args.title,
                              selectedPeriod: selectedTrimester,
                              currentIndex: currentIndex,
                              minimised: minimised,
                              toggleAudioPlayer: toggleAudioPlayer,
                            ),
                          ),
                        ),
                        ActivityIllustrationImage('raga_illus.png'),
                      ],
                    ),
                  ),
                )
              ],
            ),
          );
  }
}
