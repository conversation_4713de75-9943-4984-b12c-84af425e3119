import 'package:flutter/material.dart';
import 'package:nine_and_beyond/authModule/screens/login_screen.dart';
import 'package:nine_and_beyond/authModule/screens/choose_pragnancy_status_screen.dart';
import 'package:nine_and_beyond/authModule/screens/pregnancy_result_screen.dart';
import 'package:nine_and_beyond/authModule/screens/privacy_policy_or%20_t&c_screen.dart';
import 'package:nine_and_beyond/dailyActivityModule/screens/bedtime_story_screen.dart';
import 'package:nine_and_beyond/dailyActivityModule/screens/raga_music_screen.dart';
import 'package:nine_and_beyond/dailyActivityModule/screens/yoga_screen.dart';
import 'package:nine_and_beyond/diaryModule/screens/play_audio_screen.dart';
import 'package:nine_and_beyond/diaryModule/screens/start_recording_screen.dart';
import 'package:nine_and_beyond/homeModule/screens/gb_video_detail_screen.dart';
import 'package:nine_and_beyond/homeModule/screens/lessor_known_detail_screen.dart';
import 'package:nine_and_beyond/homeModule/screens/subscription_screen.dart';
import 'package:nine_and_beyond/moreModule/screens/switch_preference_screen.dart';
import '../authModule/screens/onboarding_screen.dart';
import '../authModule/screens/personalize_loader_screen.dart';
import '../authModule/screens/personalize_prompt_screen.dart';
import '../authModule/screens/select_period_date_screen.dart';
import '../authModule/screens/select_pragnancy_week_screen.dart';
import '../authModule/screens/register_user_screen.dart';
import '../authModule/screens/splash_screen.dart';
import '../authModule/screens/verify_otp_acreen.dart';
import '../chatGPTModule/screens/chat_gpt_history_screen.dart';
import '../chatGPTModule/screens/chat_gpt_screen.dart';
import '../chatGPTModule/screens/payment_screen.dart';
import '../chatGPTModule/screens/phonepe_gateway.dart';
import '../chatGPTModule/screens/recharge_wallet_screen.dart';
import '../chatGPTModule/screens/recharge_wallet_summary_screen.dart';
import '../chatGPTModule/screens/wallet_screen.dart';
import '../commonWidgets/bottom_nav_bar.dart';
import '../dailyActivityModule/screens/bedtime_story_audio_screen.dart';
import '../dailyActivityModule/screens/bedtime_story_html_content_screen.dart';
import '../dailyActivityModule/screens/chants_screen.dart';
import '../dailyActivityModule/screens/diet_details_screen.dart';
import '../dailyActivityModule/screens/diet_screen.dart';
import '../dailyActivityModule/screens/edit_set_preference_screen.dart';
import '../dailyActivityModule/screens/master_class_details_screen.dart';
import '../dailyActivityModule/screens/master_class_screen.dart';
import '../dailyActivityModule/screens/meditation_details_screen.dart';
import '../dailyActivityModule/screens/meditation_screen.dart';
import '../dailyActivityModule/screens/pregnancy_guide_on_diet_screen.dart';
import '../dailyActivityModule/screens/set_preference_screen.dart';
import '../dailyActivityModule/screens/yoga_details_screen.dart';
import '../homeModule/screens/app_walkthrough_video_screen.dart';
import '../homeModule/screens/baby_tracker_screen.dart';
import '../homeModule/screens/recommended_article_detail_screen.dart';
import '../homeModule/screens/garbh_lady_screen.dart';
import '../homeModule/screens/garbh_sanskar_screen.dart';
import '../homeModule/screens/welcome_to_journey_screen.dart';
import '../loading_screen.dart';
import '../moreModule/screens/edit_profile_screen.dart';
import 'arguments.dart';
import 'routes.dart';

Route<dynamic> generateRoute(RouteSettings settings) {
  switch (settings.name) {
    //
    // Auth Screens
    case NamedRoute.onBoardingScreen:
      return _getPageRoute(const OnBoardingScreen());

    // Bottom Nav
    case NamedRoute.bottomNavBarScreen:
      return _getPageRoute(
          BottomNavBar(args: settings.arguments as BottomNavArgumnets));

    // Login Screen
    case NamedRoute.loginScreen:
      return _getPageRoute(const LoginScreen());

    // Privacy Policy Or Terms And Conditions Screen
    case NamedRoute.privacyPolicyOrTermsAndConditionsScreen:
      return _getPageRoute(PrivacyPolicyOrTermsAndConditionsScreen(
        args: settings.arguments as PrivacyPolicyOrTermsAndConditionsArguments,
      ));

    // OTP Screen
    case NamedRoute.verifyOtpScreen:
      return _getPageRoute(
          VerifyOtpScreen(args: settings.arguments as VerifyOtpArguments));
    // Register Screen
    case NamedRoute.registerUserScreen:
      return _getPageRoute(RegisterUserScreen(
          args: settings.arguments as RegistrationArguments));

    // Personalize app Screen
    case NamedRoute.personalizePromptScreen:
      return _getPageRoute(PersonalizePromptScreen());

    // Personalize app step one Screen
    case NamedRoute.choosePragnancyStatusScreen:
      return _getPageRoute(const ChoosePragnancyStatusScreen());

    // Personalize app step two Screen
    case NamedRoute.selectPragnancyWeekScreen:
      return _getPageRoute(SelectPragnancyWeekScreen(
          args: settings.arguments as SelectPragnancyWeekArguments));

    // Pregnancy detail Screen
    case NamedRoute.pregnancyResultScreen:
      return _getPageRoute(const PregnancyResultScreen());

    // Personalize loader Screen
    case NamedRoute.personalizeLoaderScreen:
      return _getPageRoute(const PersonalizeLoaderScreen());

    // Select period date Screen
    case NamedRoute.selectPeriodDateScreen:
      return _getPageRoute(const SelectPeriodDateScreen());

    // Start recording Screen
    case NamedRoute.startRecordingScreen:
      return _getPageRoute(const StartRecordingScreen());

    // Play audio Screen
    case NamedRoute.playAudioScreen:
      return _getPageRoute(
          PlayAudioScreen(args: settings.arguments as PlayAudioArguments));

    // Set Preference Screen
    case NamedRoute.setPreferenceScreen:
      return _getPageRoute(SetPreferenceScreen(
          args: settings.arguments as SetPreferenceArguments));

    // Edit Set Preference Screen
    case NamedRoute.editSetPreferenceScreen:
      return _getPageRoute(EditSetPreferenceScreen(
          args: settings.arguments as EditSetPreferenceArguments));

    // Yoga Screen
    case NamedRoute.yogaScreen:
      return _getPageRoute(
          YogaScreen(args: settings.arguments as YogaArguments));

    // Yoga Detail Screen
    case NamedRoute.yogaDetailsScreen:
      return _getPageRoute(
          YogaDetailsScreen(args: settings.arguments as YogaDetailsArguments));

    // Chants Screen
    case NamedRoute.chantsScreen:
      return _getPageRoute(
          ChantsScreen(args: settings.arguments as ChantsArguments));

    // Meditation Screen
    case NamedRoute.meditationScreen:
      return _getPageRoute(
          MeditationScreen(args: settings.arguments as MeditationArguments));

    // Bedtime Story Screen
    case NamedRoute.bedtimeStoryScreen:
      return _getPageRoute(BedtimeStoryScreen(
          args: settings.arguments as BedtimeStoryArguments));

    // Bedtime Story Audio Screen
    case NamedRoute.bedtimeStoryAudioScreen:
      return _getPageRoute(BedtimeStoryAudioScreen(
          args: settings.arguments as BedtimeStoryAudioArguments));

    // Bedtime Story Html Content Screen
    case NamedRoute.bedtimeStoryHtmlContentScreen:
      return _getPageRoute(BedtimeStoryHtmlContentScreen(
          args: settings.arguments as BedtimeStoryHtmlContentArguments));

    // Raga Music Screen
    case NamedRoute.ragaMusicScreen:
      return _getPageRoute(
          RagaMusicScreen(args: settings.arguments as RagaMusicArguments));

    // Diet Screen
    case NamedRoute.dietScreen:
      return _getPageRoute(
          DietScreen(args: settings.arguments as DietArguments));

    // Diet Detail Screen
    case NamedRoute.dietDetailsScreen:
      return _getPageRoute(
          DietDetailsScreen(args: settings.arguments as DietDetailsArguments));

    // Meditation Detail Screen
    case NamedRoute.meditationDetailsScreen:
      return _getPageRoute(MeditationDetailsScreen(
          args: settings.arguments as MeditationDetailsArguments));

    // Liked Story Screen
    // case NamedRoute.likedStoryScreen:
    //   return _getPageRoute(const LikedStoryScreen());

    // Chat GPT Screen
    case NamedRoute.chatGptScreen:
      return _getPageRoute(
          ChatGptScreen(args: settings.arguments as ChatGptScreenArguments));
    case NamedRoute.chatGptHistoryScreen:
      return _getPageRoute(const ChatGptHistoryScreen());
    case NamedRoute.rechargeWalletScreen:
      return _getPageRoute(const RechargeWalletScreen());
    case NamedRoute.rechargeWalletSummaryScreen:
      return _getPageRoute(RechargeWalletSummaryScreen(
          args: settings.arguments as RechargeWalletSummaryScreenArguments));
    case NamedRoute.walletScreen:
      return _getPageRoute(const WalletScreen());

    // Switch Preference Screen
    case NamedRoute.switchPreferenceScreen:
      return _getPageRoute(const SwitchPreferenceScreen());

    // Edit Profile Screen
    case NamedRoute.editProfileScreen:
      return _getPageRoute(const EditProfileScreen());

    // Loading Screen
    case NamedRoute.loadingScreen:
      return _getPageRoute(
          LoadingScreen(args: settings.arguments as LoadingScreenArguments));

    // Payment Screen
    case NamedRoute.paymentScreen:
      return _getPageRoute(
          PaymentScreen(args: settings.arguments as PaymentScreenArguments));

    // Garbh Sanskar Screen
    case NamedRoute.garbhSanskarScreen:
      return _getPageRoute(const GarbhSanskarScreen());
    case NamedRoute.articleDetailScreen:
      return _getPageRoute(ArticleDetailScreen(
          args: settings.arguments as ArticleDetailScreenArguments));
    case NamedRoute.gbVideoDetailScreen:
      return _getPageRoute(GbVideoDetailScreen(
          args: settings.arguments as GbVideoDetailScreenArguments));
    case NamedRoute.appWalkThroughVideoScreen:
      return _getPageRoute(const AppWalkThroughVideoScreen());
    case NamedRoute.garbhLadyScreen:
      return _getPageRoute(const GarbhLadyScreen());
    case NamedRoute.welcomeToJourneyScreen:
      return _getPageRoute(const WelcomeToJourneyScreen());

    case NamedRoute.lesserKnownDetailScreen:
      return _getPageRoute(LesserKnownDetailScreen(
          args: settings.arguments as LesserKnownDetailScreenArguments));

    // Baby Tacker
    case NamedRoute.babyTrackerScreen:
      return _getPageRoute(const BabyTrackerScreen());

    // Pregnancy Guide On Diet Screen
    case NamedRoute.pregnancyGuideOnDietScreen:
      return _getPageRoute(const PregnancyGuideOnDietScreen());

    // Phone pe
    case NamedRoute.phonePeGatewayScreen:
      return _getPageRoute(PhonePeGatewayScreen(
          args: settings.arguments as PhonePeGatewayScreenArguments));

    // Subscription Screen
    case NamedRoute.subscriptionScreen:
      return _getPageRoute(const SubscriptionScreen());

    // Master Class Screen
    case NamedRoute.masterClassScreen:
      return _getPageRoute(MasterClassScreen(
          args: settings.arguments as MasterClassScreenArguments));

    // Master Class Detail Screen
    case NamedRoute.masterClassDetailsScreen:
      return _getPageRoute(MasterClassDetailsScreen(
          args: settings.arguments as MasterClassDetailsScreenArguments));

    default:
      return _getPageRoute(const SplashScreen());
  }
}

PageRoute _getPageRoute(Widget screen) {
  return MaterialPageRoute(builder: (context) => screen);
}
