// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_button.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../common_functions.dart';

class GarbhSanskarWidget extends StatelessWidget {
  GarbhSanskarWidget({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final User user = Provider.of<AuthProvider>(context, listen: false).user;

    return Container(
      width: dW,
      padding: EdgeInsets.symmetric(horizontal: dW * 0.04),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.03),
          offset: const Offset(0, 4),
          spreadRadius: 0,
          blurRadius: 15,
        )
      ]),
      child: Column(children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: dW * 0.03),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            color: Color(0xFFF5F7FD),
          ),
          alignment: Alignment.center,
          child: Text(
            language['knwAbtGrbSnskr'],
            style: Theme.of(context).textTheme.headline2!.copyWith(
                  fontSize: tS * 10,
                  color: lightBlack,
                ),
          ),
        ),
        Container(
          width: dW,
          decoration: const BoxDecoration(
            color: white,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: dW * 0.06,
            vertical: dW * 0.06,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                          top: dW * 0.01,
                          right: dW * 0.045,
                        ),
                        width: 28,
                        height: 28,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                              width: 1.7, color: const Color(0xFFB86FDD)),
                        ),
                        child: Center(
                          child: Text(
                            '1',
                            style:
                                Theme.of(context).textTheme.headline1!.copyWith(
                                      fontSize: tS * 12.3,
                                      color: const Color(0xFFFF668E),
                                    ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => push(NamedRoute.garbhSanskarScreen),
                        child: Container(
                          color: transparentColor,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                language['wtIsGrbSnskr'],
                                style: Theme.of(context)
                                    .textTheme
                                    .headline1!
                                    .copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0xFF37383F),
                                    ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  language['grbSnskrIsScitfclyProvnCncpt'],
                                  style: Theme.of(context)
                                      .textTheme
                                      .headline3!
                                      .copyWith(
                                        fontSize: tS * 10,
                                        color: const Color(0xFF7A7A7A),
                                      ),
                                ),
                              ),
                              SizedBox(height: dW * 0.08),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Positioned(
                    top: dW * 0.01 + 28,
                    bottom: 0,
                    left: 13,
                    child: Container(
                      width: 2,
                      color: const Color(0xFFDBDBE3),
                    ),
                  ),
                ],
              ),
              Stack(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                          top: dW * 0.01,
                          right: dW * 0.045,
                        ),
                        width: 28,
                        height: 28,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: const Color(0xFF649711),
                          border: Border.all(
                              width: 1.7, color: const Color(0xFFD6EAFF)),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.check_rounded,
                            color: white,
                            size: 16,
                          ),
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            language['prvideYrDeets'],
                            style:
                                Theme.of(context).textTheme.headline2!.copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0xFF515259),
                                    ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              language['completed'],
                              style: Theme.of(context)
                                  .textTheme
                                  .headline3!
                                  .copyWith(
                                    fontSize: tS * 10,
                                    color: const Color(0xFF257A1B),
                                  ),
                            ),
                          ),
                          SizedBox(height: dW * 0.08),
                        ],
                      ),
                    ],
                  ),
                  // bottom stepper
                  Positioned(
                    top: dW * 0.01 + 28,
                    bottom: 0,
                    left: 13,
                    child: Container(
                      width: 2,
                      color: const Color(0xFFDBDBE3),
                    ),
                  ),
                  // top stepper
                  Positioned(
                    top: 0,
                    bottom: 28 + (dW * 0.08) + (dW * 0.01),
                    left: 13,
                    child: Container(
                      width: 2,
                      color: const Color(0xFFDBDBE3),
                    ),
                  ),
                ],
              ),
              Stack(
                children: [
                  Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: dW * 0.045),
                        width: 28,
                        height: 28,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                              width: 1.7, color: const Color(0xFFDBDBE3)),
                        ),
                        child: Center(
                          child: Text(
                            '3',
                            textAlign: TextAlign.center,
                            style:
                                Theme.of(context).textTheme.headline1!.copyWith(
                                      fontSize: tS * 12.3,
                                      color: const Color(0xFFDBDBE3),
                                    ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => push(NamedRoute.appWalkThroughVideoScreen),
                        child: Container(
                          color: transparentColor,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                language['watchWalkThruVid'],
                                style: Theme.of(context)
                                    .textTheme
                                    .headline2!
                                    .copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0xFF515259),
                                    ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  language['toGetBestOfApp'],
                                  style: Theme.of(context)
                                      .textTheme
                                      .headline3!
                                      .copyWith(
                                        fontSize: tS * 10,
                                        color: const Color(0xFF7A7A7A),
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  // top stepper
                  Positioned(
                    top: 0,
                    bottom: 28 + (dW * 0.01),
                    left: 13,
                    child: Container(
                      width: 2,
                      color: const Color(0xFFDBDBE3),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(top: dW * 0.06),
                child: GradientButton(
                  elevation: 0,
                  buttonText: language[
                      user.completedWalkThrough ? 'knowMore' : 'startNow'],
                  onPressed: () => push(NamedRoute.garbhSanskarScreen),
                ),
              ),
            ],
          ),
        ),
      ]),
    );
  }
}
