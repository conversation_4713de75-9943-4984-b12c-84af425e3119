// ignore_for_file: deprecated_member_use, use_build_context_synchronously, prefer_typing_uninitialized_variables, unnecessary_null_comparison

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../colors.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../commonWidgets/my_text_button.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/user_model.dart';
import '../providers/auth_provider.dart';

class SelectPragnancyWeekScreen extends StatefulWidget {
  final SelectPragnancyWeekArguments args;
  const SelectPragnancyWeekScreen({Key? key, required this.args})
      : super(key: key);

  @override
  State<SelectPragnancyWeekScreen> createState() =>
      _SelectPragnancyWeekScreenState();
}

class _SelectPragnancyWeekScreenState extends State<SelectPragnancyWeekScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');

  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;

  bool isLoading = false;

  int selectedWeek = 1;

  editProfile() async {
    hideKeyBoard();

    setState(() => isLoading = true);
    final Map<String, String> body = {
      "status": widget.args.status,
      "conceptionDate": DateTime(
              DateTime.now().year, DateTime.now().month, DateTime.now().day, 3)
          .subtract(Duration(days: selectedWeek * 7))
          .toString(),
    };

    final Map<String, String> files = {};

    final response =
        await Provider.of<AuthProvider>(context, listen: false).editProfile(
      body: body,
      files: files,
    );
    setState(() => isLoading = false);

    if (!response['success']) {
      showSnackbar(language[response['failedToSave']]);
    } else {
      push(NamedRoute.pregnancyResultScreen);
      // final updatedUser =
      //     Provider.of<AuthProvider>(context, listen: false).user;
      // widget.args.status = updatedUser.status.toString();
      // selectedWeek = updatedUser.pregnancyWeek.toString();
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
      backgroundColor: white,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  user.pregnancyWeek > 0
                      ? SizedBox(
                          height: dW * 0.14,
                        )
                      : Padding(
                          padding: EdgeInsets.only(
                              left: 16, right: 16, top: dW * 0.1),
                          child: Text.rich(
                            TextSpan(children: [
                              TextSpan(text: language['personalize']),
                              TextSpan(
                                text: language['2'],
                                style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'Inter',
                                    color: Color(0xff1D1E22),
                                    fontSize: 12),
                              ),
                              TextSpan(text: language['step']),
                            ]),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: tS * 12,
                              color: const Color(0xff6B6C75),
                              fontFamily: 'Inter',
                            ),
                          ),
                        ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 16, right: 16, top: dW * 0.03),
                    child: Text(
                      language['pregnantWeeks'],
                      style: Theme.of(context).textTheme.headline1!.copyWith(
                            fontSize: tS * 16,
                            color: const Color(0XFF1D1E22),
                          ),
                    ),
                  ),
                  SizedBox(height: dW * 0.4),
                  Stack(
                    children: [
                      SizedBox(
                        width: dW,
                        height: dW * 0.3,
                        child: CupertinoPicker(
                          looping: true,
                          itemExtent: 60,
                          scrollController: FixedExtentScrollController(
                              initialItem: user.pregnancyWeek > 0
                                  ? (user.pregnancyWeek.toInt()) - 1
                                  : 0),
                          onSelectedItemChanged: (int value) {
                            setState(() {
                              selectedWeek = value + 1;
                            });
                          },
                          children: List<Widget>.generate(
                            40,
                            (index) => Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  (index + 1).toString().padLeft(2, '0'),
                                  style: textTheme.headline1!.copyWith(
                                    fontSize: tS * 24,
                                    color: const Color(0XFF975EFF),
                                  ),
                                ),
                                SizedBox(width: dW * 0.15),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: dW * 0.12,
                        left: dW * 0.14,
                        right: 0,
                        child: Text(
                          language['week'],
                          style: textTheme.headline2!.copyWith(
                            fontSize: tS * 17,
                            color: const Color(0XFF000000),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 4),
              child: GradientButton(
                isLoading: isLoading,
                onPressed: editProfile,
                buttonText: language['next'],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: MyTextButton(
                color: Colors.grey,
                text: language['dontRemember'],
                onPressed: () {
                  push(NamedRoute.selectPeriodDateScreen,
                      arguments: SelectPeriodDateArguments());
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
