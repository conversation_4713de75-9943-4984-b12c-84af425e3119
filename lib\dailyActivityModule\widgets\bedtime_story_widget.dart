// ignore_for_file: deprecated_member_use, unused_element, unrelated_type_equality_checks, unused_local_variable, must_be_immutable
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';
import '../providers/daily_activity_provider.dart';
import 'option_bottom_sheet.dart';

class BedtimeStoryWidget extends StatefulWidget {
  final BedtimeStory bedtimeStory;
  final String? activityTitle;
  final bool fromLikeScreen;
  final bool? minimised;
  final Function? toggleAudioPlayer;
  final Function? playOrView;

  const BedtimeStoryWidget({
    super.key,
    required this.bedtimeStory,
    this.activityTitle,
    this.fromLikeScreen = false,
    this.minimised,
    this.toggleAudioPlayer,
    this.playOrView,
  });

  @override
  State<BedtimeStoryWidget> createState() => _BedtimeStoryWidgetState();
}

class _BedtimeStoryWidgetState extends State<BedtimeStoryWidget> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  likeUnlike() async {
    setState(() {
      widget.bedtimeStory.isLiked = !widget.bedtimeStory.isLiked;
    });
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .likeUnlike(
      body: {
        'bedTimeStory': widget.bedtimeStory.id,
        'like': widget.bedtimeStory.isLiked,
      },
      accessToken: user.accessToken,
    );

    if (!response['success']) {
      setState(() {
        widget.bedtimeStory.isLiked = !widget.bedtimeStory.isLiked;
      });
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final bedtimeStory =
        Provider.of<DailyActivityProvider>(context, listen: true).bedtimeStory;

    final likeBedtimeStory =
        Provider.of<DailyActivityProvider>(context, listen: true)
            .likedBedtimeStories;

    return Padding(
      padding: EdgeInsets.only(left: dW * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            child: Container(
              color: transparentColor,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(3),
                        child: CachedImageWidget(
                          widget.bedtimeStory.albumArt,
                          boxFit: BoxFit.cover,
                          width: dW * 0.25,
                          height: dW * 0.25,
                        ),
                      ),
                      if (widget.bedtimeStory.audio.isEmpty &&
                          widget.bedtimeStory.htmlContent.isEmpty)
                        Positioned(
                          top: dW * 0.09,
                          right: dW * 0.09,
                          child: const AssetSvgIcon(
                            's_lock',
                          ),
                        ),
                    ],
                  ),
                  SizedBox(width: dW * 0.035),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: dW * 0.03),
                      Text(
                        widget.bedtimeStory.name,
                        style: textTheme.headline1!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFF1D1E22),
                        ),
                      ),
                      SizedBox(height: dW * 0.03),
                      Text(
                        widget.bedtimeStory.duration,
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 10,
                          color: const Color(0XFF84858E),
                        ),
                      ),
                      SizedBox(height: dW * 0.03),
                      Row(
                        children: [
                          widget.bedtimeStory.type == 'Read'
                              ? const AssetSvgIcon('read',
                                  color: Color(0XFFFF328B))
                              : const AssetSvgIcon('listen',
                                  color: Color(0XFFFF328B)),
                          SizedBox(width: dW * 0.01),
                          Text(
                            widget.bedtimeStory.type,
                            style: TextStyle(
                                color: const Color(0XFFFF668E),
                                fontFamily: 'Poppins',
                                fontSize: tS * 10,
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  Padding(
                      padding: EdgeInsets.only(top: dW * 0.03),
                      child: GestureDetector(
                        onTap: () {
                          if (widget.bedtimeStory.audio.isNotEmpty ||
                              widget.bedtimeStory.htmlContent.isNotEmpty) {
                            likeUnlike();
                          } else {
                            buySubscriptionDialog();
                          }
                        },
                        child: Icon(
                          widget.bedtimeStory.isLiked
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: widget.bedtimeStory.isLiked
                              ? const Color(0XFFFF668E)
                              : blackColor,
                          size: 20,
                        ),
                      )),
                  IconButton(
                    onPressed: () {
                      if (widget.bedtimeStory.audio.isNotEmpty ||
                          widget.bedtimeStory.htmlContent.isNotEmpty) {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15.0),
                              topRight: Radius.circular(15.0),
                            ),
                          ),
                          builder: (BuildContext context) => OptionBottomSheet(
                            fromLikeScreen: widget.fromLikeScreen,
                            playOrView: widget.playOrView,
                            bedtimeStory: widget.bedtimeStory,
                            currentIndex:
                                bedtimeStory.indexOf(widget.bedtimeStory),
                            activityTitle: widget.activityTitle != null
                                ? widget.activityTitle!
                                : 'Bedtime Stories',
                            minimised: widget.minimised,
                            toggleAudioPlayer: widget.toggleAudioPlayer,
                          ),
                        );
                      } else {
                        buySubscriptionDialog();
                      }
                    },
                    icon: Container(
                      alignment: Alignment.topCenter,
                      child: const Icon(
                        Icons.more_vert_rounded,
                        size: 22,
                        color: Color(0xff1D1E22),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
