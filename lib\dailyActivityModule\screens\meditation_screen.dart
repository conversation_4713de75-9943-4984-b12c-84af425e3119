// // ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings, use_build_context_synchronously

// import 'package:flutter/material.dart';
// import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
// import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
// import 'package:provider/provider.dart';
// import '../../authModule/model/user_model.dart';
// import '../../authModule/providers/auth_provider.dart';
// import '../../colors.dart';
// import '../../commonWidgets/circular_loader.dart';
// import '../../commonWidgets/custom_app_bar.dart';
// import '../../common_functions.dart';
// import '../../navigation/arguments.dart';
// import '../widgets/activity_illustration_image.dart';
// import '../widgets/audio_player_widget.dart';
// import '../widgets/meditation_widget.dart';

// class MeditationScreen extends StatefulWidget {
//   final MeditationArguments args;
//   const MeditationScreen({super.key, required this.args});

//   @override
//   State<MeditationScreen> createState() => _MeditationScreenState();
// }

// class _MeditationScreenState extends State<MeditationScreen> {
//   //
//   Map language = {};
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;
//   TextTheme get textTheme => Theme.of(context).textTheme;

//   int currentIndex = 0;
//   bool playing = false;

//   late Meditation playingMeditation;

//   bool minimised = false;

//   late User user;
//   bool isLoading = false;

//   List trimester = [1, 2, 3];

//   int selectedTrimester = 1;

//   List<Meditation> meditations = [];

//   trimesterText(int selectedTrimester) {
//     switch (selectedTrimester) {
//       case 1:
//         return language['1st'];
//       case 2:
//         return language['2nd'];
//       case 3:
//         return language['3rd'];
//       default:
//         return '';
//     }
//   }

//   toggleAudioPlayer(bool val, [bool killPlayer = false]) {
//     if (killPlayer) {
//       setState(() {
//         playing = false;
//         minimised = false;
//       });
//       return;
//     }
//     setState(() {
//       minimised = val;
//     });
//   }

//   fetchMeditation() async {
//     if (Provider.of<DailyActivityProvider>(context, listen: false)
//         .meditation
//         .isEmpty) {
//       setState(() => isLoading = true);
//     }
//     final response =
//         await Provider.of<DailyActivityProvider>(context, listen: false)
//             .fetchedMeditation(
//       accessToken: user.accessToken,
//     );
//     if (!response['success']) {
//       showSnackbar(response['message']);
//     }
//     setState(() => isLoading = false);
//     if (widget.args.meditation != null) {
//       final allMeditation =
//           Provider.of<DailyActivityProvider>(context, listen: false).meditation;
//       int i = allMeditation
//           .indexWhere((element) => element.id == widget.args.meditation!.id);
//       if (i != -1) {
//         showModalBottomSheet(
//           context: context,
//           isScrollControlled: true,
//           shape: const RoundedRectangleBorder(
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(15.0),
//               topRight: Radius.circular(15.0),
//             ),
//           ),
//           builder: (BuildContext context) => AudioPlayerWidget(
//             meditation: allMeditation[i],
//             currentIndex: i,
//             selectedPeriod: selectedTrimester,
//             minimised: minimised,
//             toggleAudioPlayer: toggleAudioPlayer,
//           ),
//         );
//       }
//     }
//   }

//   @override
//   void initState() {
//     super.initState();

//     user = Provider.of<AuthProvider>(context, listen: false).user;
//     selectedTrimester = user.pregnancyTrimester.toInt();
//     fetchMeditation();
//   }

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     dH = MediaQuery.of(context).size.height;
//     tS = MediaQuery.of(context).textScaleFactor;
//     language = Provider.of<AuthProvider>(context).selectedLanguage;

//     meditations = Provider.of<DailyActivityProvider>(context)
//         .getMeditationUsingTrimester(selectedTrimester);

//     return Scaffold(
//       body: Stack(
//         alignment: AlignmentDirectional.bottomCenter,
//         children: [
//           SizedBox(
//             height: dH,
//             width: dW,
//             child: Scaffold(
//               backgroundColor: white,
//               appBar: CustomAppBar(title: widget.args.title, dW: dW),
//               body: iOSCondition(dH)
//                   ? screenBody()
//                   : SafeArea(child: screenBody()),
//             ),
//           ),
//           if (playing)
//             AudioPlayerWidget(
//               meditation: playingMeditation,
//               currentIndex: currentIndex,
//               minimised: minimised,
//               toggleAudioPlayer: toggleAudioPlayer,
//               selectedPeriod: selectedTrimester,
//             )
//         ],
//       ),
//     );
//   }

//   screenBody() {
//     return isLoading
//         ? const Center(child: CircularLoader())
//         : SizedBox(
//             height: dH,
//             width: dW,
//             child: Column(
//               children: [
//                 SizedBox(height: dW * 0.04),
//                 Expanded(
//                   child: SingleChildScrollView(
//                     physics: const AlwaysScrollableScrollPhysics(),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         SizedBox(
//                           height: dW * 0.08,
//                           child: ListView(
//                             scrollDirection: Axis.horizontal,
//                             children: [
//                               Row(
//                                 children: [
//                                   ...trimester.map(
//                                     (i) => GestureDetector(
//                                       onTap: () {
//                                         setState(
//                                           () {
//                                             selectedTrimester = i;
//                                           },
//                                         );
//                                       },
//                                       child: Container(
//                                         margin:
//                                             EdgeInsets.only(left: dW * 0.03),
//                                         width: dW * 0.3,
//                                         decoration: BoxDecoration(
//                                           color: selectedTrimester == i
//                                               ? const Color(0XFF975EFF)
//                                               : const Color(0XFFF4F4F4),
//                                           borderRadius:
//                                               BorderRadius.circular(20),
//                                         ),
//                                         child: Center(
//                                           child: Text(
//                                             '${trimesterText(i)} ' +
//                                                 language['trimester'],
//                                             style:
//                                                 textTheme.headline2!.copyWith(
//                                               fontSize: tS * 12,
//                                               color: selectedTrimester == i
//                                                   ? const Color(0XFFFFFFFF)
//                                                   : const Color(0XFF000000),
//                                               overflow: TextOverflow.ellipsis,
//                                             ),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ],
//                           ),
//                         ),
//                         SizedBox(height: dW * 0.07),
//                         Padding(
//                           padding: EdgeInsets.only(
//                               left: dW * 0.04, right: dW * 0.04),
//                           child: Text(
//                             language['startMeditation'],
//                             style: textTheme.headline2!.copyWith(
//                               fontSize: tS * 14,
//                               color: const Color(0XFF000000),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: dW * 0.03),
//                         Padding(
//                           padding: EdgeInsets.only(
//                               left: dW * 0.04, right: dW * 0.04),
//                           child: Text(
//                             language['recommendedContent'],
//                             style: textTheme.headline3!.copyWith(
//                               fontSize: tS * 12,
//                               color: const Color(0XFF6B6C75),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: dW * 0.06),
//                         ListView.builder(
//                           shrinkWrap: true,
//                           itemCount: meditations.length,
//                           physics: const BouncingScrollPhysics(),
//                           itemBuilder: (context, i) => GestureDetector(
//                             onTap: () {
//                               setState(() {
//                                 playingMeditation = meditations[i];
//                                 playing = true;
//                                 currentIndex = i;
//                               });
//                             },
//                             child: MeditationWidget(
//                               playAudio: () {
//                                 setState(() {
//                                   playingMeditation = meditations[i];
//                                   playing = true;
//                                   currentIndex = i;
//                                 });
//                               },
//                               meditation: meditations[i],
//                               activityTitle: widget.args.title,
//                               selectedPeriod: selectedTrimester,
//                               currentIndex: currentIndex,
//                               minimised: minimised,
//                               toggleAudioPlayer: toggleAudioPlayer,
//                             ),
//                           ),
//                         ),
//                         ActivityIllustrationImage('meditation_illus.png'),
//                       ],
//                     ),
//                   ),
//                 )
//               ],
//             ),
//           );
//   }
// }

// ignore_for_file: deprecated_member_use, prefer_interpolation_to_compose_strings

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/dailyActivityModule/widgets/meditation_widget.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../widgets/activity_illustration_image.dart';

class MeditationScreen extends StatefulWidget {
  final MeditationArguments args;
  const MeditationScreen({super.key, required this.args});

  @override
  State<MeditationScreen> createState() => _MeditationScreenState();
}

class _MeditationScreenState extends State<MeditationScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  List trimester = [1, 2, 3];

  int selectedTrimester = 1;

  List<Meditation> meditations = [];

  trimesterText(int selectedTrimester) {
    switch (selectedTrimester) {
      case 1:
        return language['1st'];
      case 2:
        return language['2nd'];
      case 3:
        return language['3rd'];
      default:
        return '';
    }
  }

  fetchMeditation() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .meditation
        .isEmpty) {
      setState(() => isLoading = true);
    }
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedMeditation(
      accessToken: user.accessToken,
      body: {
        'content': user.plan!.content,
      },
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    selectedTrimester = user.pregnancyTrimester.toInt();
    fetchMeditation();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    meditations = Provider.of<DailyActivityProvider>(context)
        .getMeditationUsingTrimester(selectedTrimester);

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(title: widget.args.title, dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            children: [
              SizedBox(height: dW * 0.05),
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      SizedBox(
                        height: dW * 0.08,
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: [
                            Row(
                              children: [
                                ...trimester.map(
                                  (i) => GestureDetector(
                                    onTap: () {
                                      setState(
                                        () {
                                          selectedTrimester = i;
                                        },
                                      );
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(left: dW * 0.03),
                                      width: dW * 0.3,
                                      decoration: BoxDecoration(
                                        color: selectedTrimester == i
                                            ? const Color(0XFF975EFF)
                                            : const Color(0XFFF4F4F4),
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: Center(
                                        child: Text(
                                          '${trimesterText(i)} ' +
                                              language['trimester'],
                                          style: textTheme.headline2!.copyWith(
                                            fontSize: tS * 12,
                                            color: selectedTrimester == i
                                                ? const Color(0XFFFFFFFF)
                                                : const Color(0XFF000000),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.07),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: meditations.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, i) => MeditationWidget(
                          meditation: meditations[i],
                          selectedTrimester: selectedTrimester,
                        ),
                      ),
                      ActivityIllustrationImage('meditation_illus.png'),
                    ],
                  ),
                ),
              )
            ],
          );
  }
}
