// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';

class QuestionChargeWidget extends StatelessWidget {
  QuestionChargeWidget({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final num questionRate =
        Provider.of<AuthProvider>(context, listen: false).questionRate;

    return Container(
      padding: EdgeInsets.symmetric(vertical: dW * 0.02),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7DF),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(width: 1, color: const Color(0xFFFFD035)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            language['note'],
            style: Theme.of(context).textTheme.headline3!.copyWith(
                  fontSize: tS * 11,
                  color: const Color(0xFF6B6C75),
                ),
          ),
          Text(
            ' : 1 ${language['question']}  =  \u20B9 ${questionRate.toString()}',
            style: Theme.of(context).textTheme.headline1!.copyWith(
                  fontSize: tS * 11,
                  color: lightBlack,
                ),
          ),
        ],
      ),
    );
  }
}
