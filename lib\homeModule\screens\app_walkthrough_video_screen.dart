// ignore_for_file: prefer_const_constructors_in_immutables, deprecated_member_use

import 'dart:async';
import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../widgets/video_scrubber.dart';

class AppWalkThroughVideoScreen extends StatefulWidget {
  const AppWalkThroughVideoScreen({Key? key}) : super(key: key);

  @override
  AppWalkThroughVideoScreenState createState() =>
      AppWalkThroughVideoScreenState();
}

class AppWalkThroughVideoScreenState extends State<AppWalkThroughVideoScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  BetterPlayerController? _controller;

  setController() async {
    try {
      setState(() => isLoading = true);

      _controller = BetterPlayerController(
        BetterPlayerConfiguration(
            autoDispose: true,
            controlsConfiguration: BetterPlayerControlsConfiguration(
              controlsHideTime: const Duration(seconds: 1),
              playerTheme: BetterPlayerTheme.custom,
              customControlsBuilder:
                  (videoController, onPlayerVisibilityChanged) =>
                      CustomControls(controller: videoController),
            ),
            aspectRatio: 9 / 16,
            looping: true,
            autoPlay: true),
        betterPlayerDataSource: BetterPlayerDataSource(
            BetterPlayerDataSourceType.network,
            Provider.of<AuthProvider>(context, listen: false)
                .garbhSanskarVideos['walkthrough']),
      );
    } catch (e) {
      print(e);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    setController();
  }

  @override
  void dispose() {
    super.dispose();
    if (_controller != null) {
      _controller!.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      appBar: CustomAppBar(
        title: language['appWalkThrough'],
        dW: dW,
        leadingWidth: dW * 0.08,
        leading: const SizedBox.shrink(),
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading || _controller != null
          ? Column(
              children: [
                Expanded(child: BetterPlayer(controller: _controller!)),
                BottomAlignedWidget(
                    dW: dW,
                    dH: dH,
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: dW * 0.05),
                      child: GradientButton(
                        isLoading: false,
                        buttonText: language['next'],
                        onPressed: () => push(NamedRoute.garbhLadyScreen),
                      ),
                    )),
              ],
            )
          : const Center(child: CircularLoader()),
    );
  }
}

class CustomControls extends StatefulWidget {
  CustomControls({required this.controller, super.key});

  final BetterPlayerController controller;

  @override
  State<CustomControls> createState() => _CustomControlsState();
}

class _CustomControlsState extends State<CustomControls> {
  Timer? _timer;

  quickSeek(int value) async {
    widget.controller.setControlsVisibility(true);
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }
    Duration? currentPosition =
        await widget.controller.videoPlayerController!.position;
    if (currentPosition != null) {
      Duration targetPosition = currentPosition + Duration(seconds: value);
      widget.controller.seekTo(targetPosition);

      if (widget.controller.isPlaying()!) {
        _timer = Timer(const Duration(seconds: 4), () {
          widget.controller.setControlsVisibility(false);
          _timer!.cancel();
        });
      }
    }
  }

  void pausePlay() {
    widget.controller.setControlsVisibility(true);

    if (widget.controller.isPlaying()!) {
      widget.controller.pause();
      if (_timer != null && _timer!.isActive) {
        _timer!.cancel();
      }
    } else {
      widget.controller.play();
      Future.delayed(const Duration(milliseconds: 500),
          () => widget.controller.setControlsVisibility(false));
    }
  }

  void _controlVisibility() {
    if (_timer != null && _timer!.isActive) {
      widget.controller.setControlsVisibility(false);
      _timer!.cancel();
    } else {
      widget.controller.setControlsVisibility(true);
      _timer = Timer(const Duration(seconds: 4), () {
        if (mounted) {
          widget.controller.setControlsVisibility(false);
        }
        _timer!.cancel();
      });
    }
  }

  String _formatDuration(Duration? duration) {
    if (duration != null) {
      String minutes = duration.inMinutes.toString().padLeft(2, '0');
      String seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
      return '$minutes:$seconds';
    } else {
      return '00:00';
    }
  }

  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(seconds: 1), _controlVisibility);
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _controlVisibility,
      child: StreamBuilder(
        initialData: false,
        stream: widget.controller.controlsVisibilityStream,
        builder: (context, snapshot) {
          return Stack(
            clipBehavior: Clip.none,
            children: [
              Visibility(
                visible: snapshot.data!,
                child: Positioned(
                  child: Center(
                    child: GestureDetector(
                      onTap: pausePlay,
                      child: Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: white, width: 4),
                        ),
                        child: widget.controller.isPlaying()!
                            ? const Icon(
                                Icons.pause_sharp,
                                color: Colors.white,
                                size: 45,
                              )
                            : const Icon(
                                Icons.play_arrow_sharp,
                                color: Colors.white,
                                size: 55,
                              ),
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                left: -10,
                right: -10,
                bottom: 1,
                child: ValueListenableBuilder(
                  valueListenable: widget.controller.videoPlayerController!,
                  builder: (context, value, child) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.only(left: 18),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(50),
                                  shape: BoxShape.rectangle,
                                ),
                                child: Text(_formatDuration(value.position),
                                    style: Theme.of(context)
                                        .textTheme
                                        .headline3!
                                        .copyWith(
                                          fontSize: 12,
                                          color: !snapshot.data!
                                              ? transparentColor
                                              : const Color(0xFFF4F4F4),
                                        )),
                              ),
                              Container(
                                padding: const EdgeInsets.only(right: 18),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(50),
                                  shape: BoxShape.rectangle,
                                ),
                                child: Text(_formatDuration(value.duration),
                                    style: Theme.of(context)
                                        .textTheme
                                        .headline3!
                                        .copyWith(
                                          fontSize: 12,
                                          color: !snapshot.data!
                                              ? transparentColor
                                              : const Color(0xFFF4F4F4),
                                        )),
                              ),
                            ],
                          ),
                        ),
                        if (widget.controller.isVideoInitialized() ?? false)
                          CustomScrubber(
                            controller: widget.controller,
                            playerValue: value,
                            visible: snapshot.data!,
                            timer: _timer,
                          )
                      ],
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class CustomScrubber extends StatefulWidget {
  const CustomScrubber({
    required this.playerValue,
    required this.controller,
    required this.visible,
    required this.timer,
    super.key,
  });
  final VideoPlayerValue playerValue;
  final BetterPlayerController controller;
  final bool visible;
  final Timer? timer;

  @override
  CustomScrubberState createState() => CustomScrubberState();
}

class CustomScrubberState extends State<CustomScrubber> {
  double _value = 0.0;

  Timer? seekTimer;
  bool seeking = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant CustomScrubber oldWidget) {
    super.didUpdateWidget(oldWidget);
    int position = oldWidget.playerValue.position.inSeconds;
    int duration = oldWidget.playerValue.duration?.inSeconds ?? 0;
    setState(() {
      _value = position / duration;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        trackShape: const RectangularSliderTrackShape(),
        rangeTrackShape: const RectangularRangeSliderTrackShape(),
        trackHeight: 5,
        thumbShape:
            const CustomThumbShape(thumbRadius: 8), // Custom thumb shape
        overlayShape: SliderComponentShape.noOverlay,
      ),
      child: Slider(
        value: _value,
        activeColor:
            widget.visible ? const Color(0xFF975EFF) : transparentColor,
        inactiveColor: widget.visible ? Colors.grey.shade300 : transparentColor,
        min: 0.0,
        max: 1.0,
        onChanged: (newValue) {
          setState(() {
            _value = newValue;
          });
          if (widget.timer != null && widget.timer!.isActive) {
            widget.timer!.cancel();
          }
          widget.controller.setControlsVisibility(true);
          if (seekTimer != null && seekTimer!.isActive) {
            seekTimer!.cancel();
          }

          final newProgress = Duration(
              milliseconds: (_value *
                      widget.controller.videoPlayerController!.value.duration!
                          .inMilliseconds)
                  .toInt());
          widget.controller.seekTo(newProgress).then((value) {
            seekTimer = Timer(const Duration(seconds: 4), () {
              widget.controller.setControlsVisibility(false);
            });
          });
        },
      ),
    );
  }
}
