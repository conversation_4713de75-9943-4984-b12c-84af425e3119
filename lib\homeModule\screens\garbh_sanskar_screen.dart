// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/homeModule/models/article_model.dart';
import 'package:nine_and_beyond/homeModule/widgets/custom_video_player.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../providers/home_provider.dart';
import '../widgets/garbh_sanskar_article_widget.dart';

class GarbhSanskarScreen extends StatefulWidget {
  const GarbhSanskarScreen({Key? key}) : super(key: key);

  @override
  GarbhSanskarScreenState createState() => GarbhSanskarScreenState();
}

class GarbhSanskarScreenState extends State<GarbhSanskarScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  bool hideContent = false;

  List<Article> articles = [];

  fetchGarbhSanskarArticles() async {
    setState(() => isLoading = true);
    final response = await Provider.of<HomeProvider>(context, listen: false)
        .fetchGarbhSanskarArticles(accessToken: user.accessToken);
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchGarbhSanskarArticles();
  }

  toggleContentVisibility() {
    setState(() => hideContent = !hideContent);
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    articles = Provider.of<HomeProvider>(context).garbhSanskarArticles;

    return Scaffold(
      appBar: CustomAppBar(title: language['grbSnskr'], dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child:
          //  isLoading
          //     ? const Center(child: CircularLoader())
          // : CustomVideoPlayer(
          //     link: Provider.of<HomeProvider>(context, listen: false)
          // .garbhSanskarVideos['gbVideo'])
          isLoading
              ? const Center(child: CircularLoader())
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            CustomVideoPlayer(
                              link: Provider.of<AuthProvider>(context,
                                      listen: false)
                                  .garbhSanskarVideos['gbVideo'],
                              toggleContentVisibility: toggleContentVisibility,
                            ),
                            if (!hideContent) ...[
                              Container(
                                width: dW,
                                padding: screenHorizontalPadding(dW,
                                    verticalF: 0.06),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      language['grbSnskr'],
                                      style: textTheme.headline2!.copyWith(
                                        fontSize: tS * 10,
                                        color: const Color(0xFFCE1B69),
                                      ),
                                    ),
                                    SizedBox(height: dW * 0.03),
                                    Text(
                                      language['wtIsGrbSnskrNdWhyImp'],
                                      style: textTheme.headline1!.copyWith(
                                        fontSize: tS * 14,
                                        color: lightBlack,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                height: dW * 0.018,
                                width: dW,
                                color: white,
                              ),
                              Container(
                                width: dW,
                                padding: screenHorizontalPadding(dW,
                                    verticalF: 0.08),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        const AssetSvgIcon('articles',
                                            color: Color(0xFFCE1B69)),
                                        SizedBox(width: dW * 0.025),
                                        Text(
                                          language['readArtclToKnowGB'],
                                          style: textTheme.headline2!.copyWith(
                                            fontSize: tS * 10,
                                            color: const Color(0xFF84858E),
                                          ),
                                        )
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              // Articles
                              ListView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  padding: screenHorizontalPadding(dW),
                                  itemCount: articles.length,
                                  itemBuilder: ((context, i) => Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              if (articles[i]
                                                  .htmlContent
                                                  .isNotEmpty) {
                                                push(
                                                    NamedRoute
                                                        .articleDetailScreen,
                                                    arguments:
                                                        ArticleDetailScreenArguments(
                                                            article:
                                                                articles[i]));
                                              } else if (articles[i]
                                                  .videoUrl
                                                  .isNotEmpty) {
                                                push(
                                                    NamedRoute
                                                        .gbVideoDetailScreen,
                                                    arguments:
                                                        GbVideoDetailScreenArguments(
                                                            article:
                                                                articles[i]));
                                              }
                                            },
                                            child: GarbhSanskarArticleWidget(
                                              key: ValueKey(articles[i].id),
                                              article: articles[i],
                                            ),
                                          ),
                                        ],
                                      ))),
                            ]
                          ],
                        ),
                      ),
                    ),
                    if (!hideContent)
                      BottomAlignedWidget(
                          dW: dW,
                          dH: dH,
                          child: GradientButton(
                            isLoading: false,
                            buttonText: language['next'],
                            onPressed: Provider.of<HomeProvider>(context,
                                            listen: false)
                                        .garbhSanskarVideos['walkthrough'] !=
                                    ''
                                ? () =>
                                    push(NamedRoute.appWalkThroughVideoScreen)
                                : () => push(NamedRoute.garbhLadyScreen),
                          )),
                  ],
                ),
    );
  }
}
