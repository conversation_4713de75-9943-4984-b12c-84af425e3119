// ignore_for_file: deprecated_member_use, must_be_immutable

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';

class MasterClassBannerWidget extends StatefulWidget {
  const MasterClassBannerWidget({super.key});

  @override
  State<MasterClassBannerWidget> createState() =>
      MasterClassBannerWidgetState();
}

class MasterClassBannerWidgetState extends State<MasterClassBannerWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    final masterClassBanner = Provider.of<AuthProvider>(context).masterClass;

    return GestureDetector(
      onTap: () {
        push(NamedRoute.masterClassScreen,
            arguments:
                MasterClassScreenArguments(title: language['masterClass']));
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImageWidget(
          masterClassBanner['banner'],
          boxFit: BoxFit.cover,
        ),
      ),
    );
  }
}
