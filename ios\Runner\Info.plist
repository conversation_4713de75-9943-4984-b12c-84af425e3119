<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>9&amp;Beyond</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>nine_and_beyond</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>com.nineandbeyond.userapp</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>app-1-85881474676-ios-404b19be858057813e4b21</string>
				</array>
			</dict>
		</array>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>ppemerchantsdkv1</string>
			<string>ppemerchantsdkv2</string>
			<string>ppemerchantsdkv3</string>
			<string>paytmmp</string>
			<string>gpay</string>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FirebaseDynamicLinksCustomDomains</key>
		<array>
			<string>com.nineandbeyond.userapp/launchapp</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
		</dict>
		<key>NSCameraUsageDescription</key>
		<string>To capture images to set as profile picture</string>
		<key>NSLocalNetworkUsageDescription</key>
		<string>Recyclink uses the local network for internet access.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>To record audio for your personal diary</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>To upload a photo to use as a profile picture</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
	</dict>
</plist>