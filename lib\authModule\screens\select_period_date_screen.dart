// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:provider/provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/custom_bottom_sheet.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/custom_text_field.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/user_model.dart';
import '../providers/auth_provider.dart';
import 'package:intl/intl.dart';

class SelectPeriodDateScreen extends StatefulWidget {
  const SelectPeriodDateScreen({super.key});

  @override
  State<SelectPeriodDateScreen> createState() => _SelectPeriodDateScreenState();
}

class _SelectPeriodDateScreenState extends State<SelectPeriodDateScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;

  bool isLoading = false;
  TextEditingController dateController = TextEditingController();
  late DateTime selectedDate;
  TextEditingController menstrualCycleController = TextEditingController();

  List menstrualCycleOptions = [
    '2 days',
    '3 days',
    '4 days',
    '6 days',
    '8 days',
    '10 days',
    '12 days',
  ];

  void _datePicker() async {
    hideKeyBoard();
    final now = DateTime.now();
    final firstDate = DateTime(now.year - 100, now.month, now.day);
    final lastDate = DateTime(now.year, now.month, now.day);

    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: dateController.text.isEmpty ? lastDate : selectedDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (pickedDate != null) {
      selectedDate = pickedDate;
      setState(() {
        dateController.text = DateFormat('dd/MM/yyyy').format(pickedDate);
      });
    }
  }

  selectMenstrualCycleSheet() {
    hideKeyBoard();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      constraints:
          BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.5),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
        topLeft: Radius.circular(15.0),
        topRight: Radius.circular(15.0),
      )),
      builder: (BuildContext context) => CustomBottomSheet(
        title: language['lngthOfMenstruCycle'],
        selectedOption: menstrualCycleController.text,
        options: menstrualCycleOptions,
      ),
    ).then((value) {
      if (value != null) {
        setState(() {
          menstrualCycleController.text = value;
        });
      }
    });
  }

  void _goToDoneScreen() async {
    final isValid = _formKey.currentState!.validate();

    if (!isValid) {
      return;
    }
    _formKey.currentState!.save();

    hideKeyBoard();

    DateTime conceptionDate = selectedDate.add(const Duration(days: 14));
    int days = DateTime.now().difference(conceptionDate).inDays;

    if ((days / 7).ceil() > 40) {
      showDialog(
        context: context,
        builder: ((context) => CustomDialogBox(
              buttonsAxisAligment: MainAxisAlignment.center,
              title: language['cnntBePregIfMensDate'] +
                  ' ' +
                  DateFormat('MMM d, yyyy').format(conceptionDate),
              firstButton: FilledDialogButton(
                onPressed: pop,
                text: language['ok'],
              ),
            )),
      );
      return;
    }

    setState(() => isLoading = true);
    final Map<String, String> body = {
      "status": 'Pregnant',
      "conceptionDate": conceptionDate.toString(),
    };

    final Map<String, String> files = {};

    final response =
        await Provider.of<AuthProvider>(context, listen: false).editProfile(
      body: body,
      files: files,
    );
    setState(() => isLoading = false);

    if (!response['success']) {
      showSnackbar(language[response['message']]);
    } else {
      // final updatedUser =
      //     Provider.of<AuthProvider>(context, listen: false).user;
      // widget.args.status = updatedUser.status.toString();
      // selectedWeek = updatedUser.pregnancyWeek.toString();

      // setState(() {});
      push(NamedRoute.pregnancyResultScreen);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  void dispose() {
    super.dispose();

    dateController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    bool valid = dateController.text.isNotEmpty &&
        menstrualCycleController.text.isNotEmpty;
    return Scaffold(
      backgroundColor: white,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      user.pregnancyWeek > 0
                          ? SizedBox(
                              height: dW * 0.14,
                            )
                          : Padding(
                              padding: EdgeInsets.only(
                                  left: 16, right: 16, top: dW * 0.1),
                              child: Text.rich(
                                TextSpan(children: [
                                  TextSpan(text: language['personalize']),
                                  TextSpan(
                                    text: language['2'],
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'Inter',
                                        color: Color(0xff1D1E22),
                                        fontSize: 12),
                                  ),
                                  TextSpan(text: language['step']),
                                ]),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: tS * 12,
                                  color: const Color(0xff6B6C75),
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 16, right: 16, top: dW * 0.03),
                        child: Text(
                          language['periodDate'],
                          style:
                              Theme.of(context).textTheme.headline1!.copyWith(
                                    fontSize: tS * 16,
                                    color: const Color(0XFF1D1E22),
                                  ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 16,
                            right: 16,
                            top: dW * 0.07,
                            bottom: dW * 0.06),
                        child: Image.asset(
                          'assets/images/calender_graphic.png',
                          height: 148,
                          width: 264,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 16, right: 16, top: dW * 0.06),
                        child: CustomTextFieldWithLabel(
                          label: language['lastPeriodDate'],
                          hintText: language['dateFormat'],
                          onTap: _datePicker,
                          controller: dateController,
                          borderColor: greyBorderColor,
                          inputType: TextInputType.none,
                          suffixIcon: const Padding(
                            padding: EdgeInsets.only(right: 12),
                            child: AssetSvgIcon(
                              'calender',
                            ),
                          ),
                          suffixIconConstraints:
                              const BoxConstraints(maxHeight: 24),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please select date';
                            }
                            return null;
                          },
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: 16, right: 16, top: dW * 0.06),
                        child: CustomTextFieldWithLabel(
                          label: language['menstrualCycle'],
                          hintText: language['lngthOfMenstruCycle'],
                          inputType: TextInputType.none,
                          onTap: selectMenstrualCycleSheet,
                          controller: menstrualCycleController,
                          borderColor: greyBorderColor,
                          minLines: 1,
                          maxLines: 2,
                          validator: (String? val) {
                            if (val!.isEmpty) {
                              return language['emptyIssueValidation'];
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
              child: Stack(
                children: [
                  GradientButton(
                    elevation: valid ? 2 : 0,
                    isLoading: isLoading,
                    onPressed: valid ? _goToDoneScreen : () {},
                    buttonText: language['next'],
                  ),
                  if (!valid)
                    Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0,
                        top: 0,
                        child: Container(
                          color: Colors.white.withOpacity(.7),
                        )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
