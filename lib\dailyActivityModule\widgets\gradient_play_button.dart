import 'package:flutter/material.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../common_functions.dart';

class GradientPlayButton extends StatefulWidget {
  final String? icon;
  final String buttonText;
  final bool isLoading;
  final Function onPressed;
  final double elevation;

  const GradientPlayButton({
    super.key,
    this.icon,
    required this.buttonText,
    this.isLoading = false,
    required this.onPressed,
    this.elevation = 2,
  });

  @override
  State<GradientPlayButton> createState() => _GradientPlayButtonState();
}

class _GradientPlayButtonState extends State<GradientPlayButton> {
  double dW = 0.0;
  double tS = 0.0;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    return Container(
      width: dW,
      padding: EdgeInsets.only(top: dW * 0.02),
      child: ElevatedButton(
        onPressed: () => widget.onPressed(),
        style: ElevatedButton.styleFrom(
          elevation: widget.elevation,
          padding: const EdgeInsets.all(0),
          fixedSize: Size(dW * 0.87, 42),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Container(
          // width: dW * 0.87,
          height: dW * 0.145,
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(4),
            gradient: linearGradient,
          ),
          child: Center(
            child: widget.isLoading
                ? SizedBox(
                    height: dW * 0.055,
                    width: dW * 0.055,
                    child: circularForButton(dW * 0.05),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (widget.icon != null)
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: AssetSvgIcon(widget.icon!),
                        ),
                      Text(
                        widget.buttonText,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: tS * 14,
                          // letterSpacing: .3,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
