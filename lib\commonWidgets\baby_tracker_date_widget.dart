import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/colors.dart';

import 'text_widget.dart';

class BabyTrackerDateWidget extends StatelessWidget {
  final DateTime date;
  final bool isSelected;
  final double dW;
  final bool applyRightMargin;
  const BabyTrackerDateWidget({
    super.key,
    required this.date,
    required this.isSelected,
    required this.dW,
    this.applyRightMargin = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: dW * 0.125,
      margin: EdgeInsets.only(right: applyRightMargin ? dW * 0.01 : 0),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        gradient: !isSelected
            ? null
            : LinearGradient(
                colors: [
                  const Color(0xFFCE1B69),
                  const Color(0xFFFF328B).withOpacity(0.71),
                ],
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
              ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          TextWidget(
            title: DateFormat('EEE').format(date),
            fontSize: 10,
            color: isSelected ? Colors.white : const Color(0xFF6B6C75),
            fontWeight: FontWeight.w400,
          ),
          TextWidget(
            title: DateFormat('dd').format(date),
            fontSize: 16,
            color: isSelected ? Colors.white : lightBlack,
            fontWeight: FontWeight.w500,
          ),
        ],
      ),
    );
  }
}
