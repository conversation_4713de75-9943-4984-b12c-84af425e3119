import 'chat_gpt_content_model.dart';

class ChatGPTSession {
  final String id;
  final String question;
  final String answer;

  bool isLocal;
  final DateTime createdAt;

  ChatGPTSession({
    required this.id,
    required this.question,
    required this.answer,
    this.isLocal = false,
    required this.createdAt,
  });

  static ChatGPTSession jsonToChatGPTSession(Map session) => ChatGPTSession(
        id: session['_id'],
        question: session['firstQuestion'],
        answer: session['firstAnswer'],
        createdAt: DateTime.parse(session['createdAt']).toLocal(),
      );
}
