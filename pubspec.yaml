name: nine_and_beyond
description: Get guidance throughout pregnancy.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.12+12

environment:
  sdk: '>=2.19.4 <3.0.0'


dependencies:
  flutter:
    sdk: flutter
  http: ^0.13.3
  pin_code_fields: ^7.3.0
  flutter_svg: ^1.1.1+1
  localstorage: ^4.0.0+1
  shared_preferences: ^2.0.7
  provider: ^6.0.3
  carousel_slider: ^4.2.1
  intl: ^0.18.1
  firebase_messaging: 
  flutter_local_notifications: ^16.2.0
  firebase_core: ^2.8.0
  image_picker: ^0.8.3+3
  email_validator: ^2.0.1
  url_launcher: ^6.0.9
  flutter_html: ^3.0.0-alpha.5
  file_picker:
  razorpay_flutter: ^1.3.4
  permission_handler: ^10.0.0
  # webview_flutter: ^3.0.4
  # badges: ^2.0.3
  cached_network_image: ^3.2.2
  # custom_refresh_indicator: ^2.0.1
  open_file_safe: ^3.2.3
  path_provider: ^2.0.11
  scrollable_positioned_list: ^0.3.5
  firebase_dynamic_links: ^5.3.6
  share_plus: 
  dio: ^4.0.6
  video_player: ^2.4.10
  better_player:
    git:
      url: https://github.com/matthewrice345/betterplayer.git
      ref: Issue1057
  cupertino_icons: ^1.0.2
  audioplayers: ^1.0.1
  # record_mp3: ^3.0.0
  flutter_sound: ^9.2.0
  youtube_player_flutter: ^8.1.2
  card_swiper: ^2.0.4
  page_transition: ^2.1.0
  flutter_launcher_icons: ^0.13.1
  animate_do: ^3.0.2
  shimmer: ^3.0.0
  phonepe_payment_sdk: ^1.0.3
  device_info_plus: 
  flutter_webview_pro: ^3.0.1+4
  otp_autofill: ^4.0.0
  
 

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0

dependency_overrides:
  ffi:


flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_logo.png"

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgIcons/
    - assets/placeholders/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter/Inter-Thin.ttf
        - asset: assets/fonts/Inter/Inter-Black.ttf
        - asset: assets/fonts/Inter/Inter-Bold.ttf
        - asset: assets/fonts/Inter/Inter-Light.ttf
        - asset: assets/fonts/Inter/Inter-Medium.ttf
        - asset: assets/fonts/Inter/Inter-Regular.ttf
        - asset: assets/fonts/Inter/Inter-SemiBold.ttf
        - asset: assets/fonts/Inter/Inter-ExtraBold.ttf
        - asset: assets/fonts/Inter/Inter-ExtraLight.ttf
fonts:
    - family: Nunito
      fonts:
        - asset: assets/fonts/Nunito/Nunito-Black.ttf
        - asset: assets/fonts/Nunito/Nunito-BlackItalic.ttf
        - asset: assets/fonts/Nunito/Nunito-Bold.ttf
        - asset: assets/fonts/Nunito/Nunito-BoldItalic.ttf
        - asset: assets/fonts/Nunito/Nunito-ExtraBold.ttf
        - asset: assets/fonts/Nunito/Nunito-ExtraBoldItalic.ttf
        - asset: assets/fonts/Nunito/Nunito-ExtraLight.ttf
        - asset: assets/fonts/Nunito/Nunito-ExtraLightItalic.ttf
        - asset: assets/fonts/Nunito/Nunito-Italic.ttf
        - asset: assets/fonts/Nunito/Nunito-Light.ttf
        - asset: assets/fonts/Nunito/Nunito-LightItalic.ttf
        - asset: assets/fonts/Nunito/Nunito-Medium.ttf
        - asset: assets/fonts/Nunito/Nunito-MediumItalic.ttf
        - asset: assets/fonts/Nunito/Nunito-Regular.ttf
        - asset: assets/fonts/Nunito/Nunito-SemiBold.ttf
        - asset: assets/fonts/Nunito/Nunito-SemiBoldItalic.ttf
          fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto/Roboto-Black.ttf
        - asset: assets/fonts/Roboto/Roboto-BlackItalic.ttf
        - asset: assets/fonts/Roboto/Roboto-Bold.ttf
        - asset: assets/fonts/Roboto/Roboto-BoldItalic.ttf
        - asset: assets/fonts/Roboto/Roboto-Italic.ttf
        - asset: assets/fonts/Roboto/Roboto-Light.ttf
        - asset: assets/fonts/Roboto/Roboto-LightItalic.ttf
        - asset: assets/fonts/Roboto/Roboto-Medium.ttf
        - asset: assets/fonts/Roboto/Roboto-MediumItalic.ttf
        - asset: assets/fonts/Roboto/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto/Roboto-Thin.ttf  
        - asset: assets/fonts/Roboto/Roboto-ThinItalic.ttf
    - family: Cantora One  
      fonts:
       - asset: assets/fonts/CantoraOne/CantoraOne-Regular.ttf
    - family: Metropolis  
      fonts:
       - asset: assets/fonts/Metropolis/Metropolis-Regular.ttf
    - family: Pridi  
      fonts:
       - asset: assets/fonts/Pridi/Pridi-Bold.ttf
       - asset: assets/fonts/Pridi/Pridi-ExtraLight.ttf
       - asset: assets/fonts/Pridi/Pridi-Light.ttf
       - asset: assets/fonts/Pridi/Pridi-Medium.ttf
       - asset: assets/fonts/Pridi/Pridi-Regular.ttf
       - asset: assets/fonts/Pridi/Pridi-SemiBold.ttf
    - family: IBMPlexSansKR  
      fonts:
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-Bold.ttf
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-ExtraLight.ttf
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-Light.ttf
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-Medium.ttf
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-Regular.ttf
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-SemiBold.ttf
       - asset: assets/fonts/IBMPlexSansKR/IBMPlexSansKR-Thin.ttf  
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
