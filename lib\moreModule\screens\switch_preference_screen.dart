// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';

class SwitchPreferenceScreen extends StatefulWidget {
  const SwitchPreferenceScreen({super.key});

  @override
  State<SwitchPreferenceScreen> createState() => _SwitchPreferenceScreenState();
}

class _SwitchPreferenceScreenState extends State<SwitchPreferenceScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;

  bool isLoading = false;

  featureComingSoonDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => CustomDialogBox(
            buttonsAxisAligment: MainAxisAlignment.center,
            title: language['featureComingSoon'],
            firstButton: FilledDialogButton(
              onPressed: () {
                pop();
                pop();
              },
              text: language['goBack'],
            ),
          )),
    );
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
        backgroundColor: white,
        appBar: CustomAppBar(
          title: language['switchPreference'],
          dW: dW,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
                padding: EdgeInsets.only(left: 16, right: 16, top: dW * 0.05),
                child: Text(
                  '${language['yourCurrentPref']}${user.status}',
                  style: Theme.of(context).textTheme.headline3!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0XFF6B6C75),
                      ),
                )),
            Container(
              margin: EdgeInsets.symmetric(vertical: dW * 0.06),
              height: 4,
              color: const Color(0XFFF7F8FC),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: Text(
                language['areYouPregnant'],
                style: Theme.of(context).textTheme.headline1!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0XFF1D1E22),
                    ),
              ),
            ),
            GestureDetector(
              onTap: () {
                String status = 'Pregnant';
                push(NamedRoute.selectPragnancyWeekScreen,
                    arguments: SelectPragnancyWeekArguments(status: status));
              },
              child: Container(
                margin: EdgeInsets.only(left: 16, right: 16, top: dW * 0.04),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xffD0D0D0),
                  ),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/pregnant_graphic.png',
                      height: 196,
                    ),
                    Text(
                      language['yesIAm'],
                      style: Theme.of(context).textTheme.headline1!.copyWith(
                            fontSize: tS * 16,
                            color: const Color(0XFF84858E),
                          ),
                    ),
                  ],
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                featureComingSoonDialog();
              },
              child: Container(
                margin: EdgeInsets.only(left: 16, right: 16, top: dW * 0.05),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xffD0D0D0),
                  ),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/no_pregnant_graphic.png',
                      height: 196,
                    ),
                    Text(
                      language['noButWantTo'],
                      style: Theme.of(context).textTheme.headline1!.copyWith(
                            fontSize: tS * 14,
                            color: const Color(0XFF84858E),
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ));
  }
}
