// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:provider/provider.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  bool isLoggedOut = true;
  bool isFetchingFleetData = true;

  var referralCode = '';
  var referredByUserId = '';
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  Map language = {};

  Map? fleetData;

  goToOnboardingScreen() {
    Future.delayed(const Duration(seconds: 2),
        () => pushAndRemoveUntil(NamedRoute.onBoardingScreen));
  }

  tryAutoLogin() async {
    try {
      var authProvider = Provider.of<AuthProvider>(context, listen: false);

      await storage.ready;
      final accessTokenString = storage.getItem('accessToken');
      final Map response = {'success': true};
      // final response = await getLanguage();

      final appCongigResponse =
          await Provider.of<AuthProvider>(context, listen: false).getAppConfig([
        'showGarbhSanskarAlways',
        'gptQuestionCharge',
        'blockDailyActivities',
        'delete_feature',
        'Razorpay',
        'Banner',
        'SubscriptionScreenImage',
        'Plan',
        'SubscriptionDialog',
        'GarbhSanskarVideos',
        'MasterClass',
      ]);

      if (accessTokenString != null) {
        var accessToken = json.decode(accessTokenString);
        if (accessToken != null) {
          final user =
              await authProvider.login(query: "?phone=${accessToken['phone']}");

          if (user['success'] && user['login'] && response['success']) {
            final theUser =
                Provider.of<AuthProvider>(context, listen: false).user;
            if (theUser.status == '') {
              Future.delayed(const Duration(seconds: 2),
                  () => push(NamedRoute.personalizePromptScreen));
            } else {
              Future.delayed(
                  const Duration(seconds: 2),
                  () => pushAndRemoveUntil(NamedRoute.bottomNavBarScreen,
                      arguments: BottomNavArgumnets()));
            }
          } else {
            goToOnboardingScreen();
          }
        } else {
          goToOnboardingScreen();
        }
      } else {
        goToOnboardingScreen();
      }
    } catch (e) {
      goToOnboardingScreen();
    }
  }

  getLanguage() async {
    await storage.ready;
    var languageMap = storage.getItem('language');
    String language = 'english';

    if (languageMap != null) {
      languageMap = json.decode(languageMap);
      language = languageMap['language'];
    } else {
      Provider.of<AuthProvider>(context, listen: false)
          .setLanguageInStorage(language);
    }

    final response = await Provider.of<AuthProvider>(context, listen: false)
        .getAppConfig(
            ['user-$language', 'delete_feature', 'showGarbhSanskarAlways']);

    return response;
  }

  @override
  void initState() {
    super.initState();

    myInit();
  }

  myInit() async {
    await tryAutoLogin();
    // Future.delayed(
    //   const Duration(seconds: 2, milliseconds: 5),
    //   () => pushAndRemoveUntil(
    //     NamedRoute.onBoardingScreen,
    //   ),
    //   // () => pushAndRemoveUntil(NamedRoute.bottomNavBarScreen,
    //   //     arguments: BottomNavArgumnets()),
    // );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double dW = MediaQuery.of(context).size.width;
    final double dH = MediaQuery.of(context).size.height;
    // final double tS = MediaQuery.of(context).textScaleFactor;
    final language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      body: Stack(
        children: [
          Container(
            alignment: Alignment.centerRight,
            child: Image.asset(
              'assets/images/splash_lady.png',
            ),
          ),
          SizedBox(
            height: dH,
            width: dW,
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(top: dH * 0.3),
                    child: Column(
                      children: [
                        Image.asset(
                          'assets/images/splash_screen_logo.png',
                          // width: dW,s
                          height: dH * 0.1,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 14),
                          child: GradientWidget(
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xffCE1B69),
                                Color(0xffFF328B),
                              ],
                            ),
                            child: Text(
                              language['9AndBeyond'],
                              style: const TextStyle(
                                fontSize: 32.64,
                                fontWeight: FontWeight.w900,
                                letterSpacing: 3,
                                fontFamily: 'Nunito',
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10),
                          child: Text(
                            language['holisticApproachToPregnancy'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              letterSpacing: 0.3,
                              color: Color(0xff975EFF),
                              fontFamily: 'Cantora One',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: dH * 0.2),
                  child: Column(
                    children: [
                      Text(
                        language['knowledgePartner'],
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.w400,
                          color: Color(0xff6B6C75),
                          fontFamily: 'Roboto',
                        ),
                      ),
                      Text(
                        language['manashakti'],
                        style: const TextStyle(
                          fontSize: 21.44,
                          fontWeight: FontWeight.w700,
                          color: Color(0xffF33800),
                          fontFamily: 'Metropolis',
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(top: 2),
                        child: AssetSvgIcon('researchcentre'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
