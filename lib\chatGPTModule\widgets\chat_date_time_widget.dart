// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';

class ChatDateTime extends StatelessWidget {
  final DateTime dateTime;
  final bool isFirst;

  ChatDateTime(this.dateTime, {super.key, this.isFirst = false});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      margin: EdgeInsets.only(
        top: dW * (isFirst ? 0.06 : 0.06),
        bottom: dW * 0.01,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
              (isSameDay(dateTime, DateTime.now())
                      ? Provider.of<AuthProvider>(context, listen: false)
                          .selectedLanguage['today']!
                      : DateFormat('d MMM').format(dateTime)) +
                  ', ' +
                  DateFormat('h:mm a').format(dateTime),
              style: Theme.of(context).textTheme.headline2!.copyWith(
                    fontSize: tS * 12,
                    color: const Color(0xFF6B6C75),
                    letterSpacing: .3,
                  )),
        ],
      ),
    );
  }
}
