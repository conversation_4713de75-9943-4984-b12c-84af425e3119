// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';

class MasterClassDetailsScreen extends StatefulWidget {
  final MasterClassDetailsScreenArguments args;

  const MasterClassDetailsScreen({super.key, required this.args});

  @override
  State<MasterClassDetailsScreen> createState() =>
      _MasterClassDetailsScreenState();
}

class _MasterClassDetailsScreenState extends State<MasterClassDetailsScreen> {
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;

  TextTheme get textTheme => Theme.of(context).textTheme;
  bool isFullScreen = false;

  late YoutubePlayerController _controller;
  bool hasReachedHalfway = false;

  @override
  void initState() {
    super.initState();
    final videoID =
        YoutubePlayer.convertUrlToId(widget.args.selectedMasterClass.video);
    _controller = YoutubePlayerController(
      initialVideoId: videoID.toString(),
      flags: const YoutubePlayerFlags(
        autoPlay: true,
      ),
    );

    _controller.addListener(() {
      if (_controller.value.isFullScreen != isFullScreen) {
        setState(() {
          isFullScreen = _controller.value.isFullScreen;
        });
      }

      if (!hasReachedHalfway && _controller.value.position.inSeconds > 0) {
        double progress = _controller.value.position.inSeconds /
            _controller.metadata.duration.inSeconds;

        if (progress >= widget.args.videoPercentage['videoPercentage']) {
          hasReachedHalfway = true;
          widget.args.onVideoProgress?.call();
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      body: Stack(
        children: [
          screenBody(),
          if (isFullScreen)
            Positioned(
              top: dW * 0.01,
              left: dW * 0.02,
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () {
                  _controller.toggleFullScreenMode();
                },
              ),
            ),
        ],
      ),
    );
  }

  screenBody() {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isFullScreen)
            SafeArea(
              child: Container(
                color: white,
                child: Row(
                  children: const [
                    Spacer(),
                    IconButton(
                      onPressed: pop,
                      icon: Icon(
                        Icons.clear,
                        color: Color(0XFF000000),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          YoutubePlayerBuilder(
            player: YoutubePlayer(
              controller: _controller,
              showVideoProgressIndicator: true,
              progressIndicatorColor: const Color(0XFF975EFF),
              progressColors: const ProgressBarColors(
                playedColor: Color(0XFF975EFF),
                handleColor: Color(0XFF975EFF),
              ),
            ),
            builder: (context, player) {
              return player;
            },
          ),
          Padding(
            padding: EdgeInsets.only(
                left: dW * 0.05,
                right: dW * 0.05,
                top: dW * 0.08,
                bottom: dW * 0.02),
            child: Text(
              widget.args.selectedMasterClass.name,
              style: textTheme.headline1!.copyWith(
                fontSize: tS * 16,
                color: const Color(0XFF1D1E22),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: dW * 0.03,
              right: dW * 0.03,
              top: dW * 0.05,
            ),
            child: Html(data: widget.args.selectedMasterClass.description),
          )
        ],
      ),
    );
  }
}
