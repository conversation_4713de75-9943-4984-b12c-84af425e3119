// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/circular_loader.dart';

class GradientStartButton extends StatefulWidget {
  final String? icon;
  final String buttonText;
  final bool isLoading;
  final Function onPressed;
  final double elevation;

  const GradientStartButton({
    super.key,
    this.icon,
    required this.buttonText,
    this.isLoading = false,
    required this.onPressed,
    this.elevation = 2,
  });

  @override
  State<GradientStartButton> createState() => _GradientStartButtonState();
}

class _GradientStartButtonState extends State<GradientStartButton> {
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    return Container(
      padding: EdgeInsets.only(top: dW * 0.02),
      child: ElevatedButton(
        onPressed: () => widget.onPressed(),
        style: ElevatedButton.styleFrom(
          elevation: widget.elevation,
          padding: const EdgeInsets.all(0),
          fixedSize: Size(dW * 0.87, 42),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: const LinearGradient(
              colors: [
                Color(0XFF0036B4),
                Color(0XFF06ACFF),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
          child: Center(
            child: widget.isLoading
                ? SizedBox(
                    height: dW * 0.055,
                    width: dW * 0.055,
                    child: circularForButton(dW * 0.05),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.buttonText,
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 10,
                          color: const Color(0XFFFFFFFF),
                        ),
                      ),
                      if (widget.icon != null)
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: AssetSvgIcon(widget.icon!),
                        ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
