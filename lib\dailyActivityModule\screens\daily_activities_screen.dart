// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';
import '../widgets/daily_activity_widget.dart';

class DailyActivitiesScreen extends StatefulWidget {
  const DailyActivitiesScreen({super.key});

  @override
  State<DailyActivitiesScreen> createState() => _DailyActivitiesScreenState();
}

class _DailyActivitiesScreenState extends State<DailyActivitiesScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;
  bool isLoading = false;

  List<DailyActivity> dailyActivity = [];

  fetchDailyActivity() async {
    if (Provider.of<DailyActivityProvider>(context, listen: false)
        .dailyActivity
        .isEmpty) {
      setState(() => isLoading = true);
    }

    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchedDailyActivity(
      accessToken: user.accessToken,
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchDailyActivity();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    dailyActivity = Provider.of<DailyActivityProvider>(context).dailyActivity;

    return Scaffold(
      backgroundColor: const Color(0XFFFFFFFF),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }
  //   return Scaffold(
  //     appBar: CustomAppBar(
  //       title: '',
  //       dW: dW,
  //       leading: Text(
  //         language['dailyActivities'],
  //         style: textTheme.headline1!.copyWith(
  //           fontSize: tS * 12,
  //           color: const Color(0XFF1D1E22),
  //         ),
  //       ),
  //       //  leading: const SizedBox.shrink(),
  //       actions: [
  //         Padding(
  //           padding: EdgeInsets.only(right: dW * 0.05),
  //           child: GestureDetector(
  //             onTap: () {
  //               push(
  //                 NamedRoute.setPreferenceScreen,
  //                 arguments: SetPreferenceArguments(
  //                   title: language['setPreference'],
  //                 ),
  //               );
  //             },
  //             child: Row(
  //               children: [
  //                 const AssetSvgIcon(
  //                   'set_ref',
  //                   color: Color(0XFFFF328B),
  //                   height: 16,
  //                 ),
  //                 SizedBox(width: dW * 0.02),
  //                 GradientWidget(
  //                   gradient: linearGradient,
  //                   child: Text(
  //                     language['setPreference'],
  //                     style: const TextStyle(
  //                       fontSize: 10,
  //                       fontWeight: FontWeight.w500,
  //                       fontFamily: 'Inter',
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         )
  //       ],
  //     ),
  //     body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
  //   );
  // }

  screenBody() {
    return isLoading
        ? const Center(child: CircularLoader())
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    left: dW * 0.05, right: dW * 0.05, top: dW * 0.08),
                child: Row(
                  children: [
                    Text(
                      language['dailyActivities'],
                      style: textTheme.headline1!.copyWith(
                        fontSize: tS * 12,
                        color: const Color(0XFF1D1E22),
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        push(
                          NamedRoute.setPreferenceScreen,
                          arguments: SetPreferenceArguments(
                            title: language['setPreference'],
                          ),
                        );
                      },
                      child: Container(
                        color: transparentColor,
                        child: Row(
                          children: [
                            const AssetSvgIcon(
                              'set_ref',
                              color: Color(0XFFFF328B),
                              height: 16,
                            ),
                            SizedBox(width: dW * 0.02),
                            GradientWidget(
                              gradient: linearGradient,
                              child: Text(
                                language['setPreference'],
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(height: dW * 0.04),
              Divider(
                color: Colors.grey.withOpacity(0.1),
                thickness: dW * 0.01,
              ),
              SizedBox(height: dW * 0.03),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: dailyActivity.length,
                  physics: const BouncingScrollPhysics(),
                  itemBuilder: (context, i) => DailyActivityWidget(
                    dailyActivity: dailyActivity[i],
                    dailyActivities: dailyActivity,
                  ),
                ),
              ),
            ],
          );
  }
}
