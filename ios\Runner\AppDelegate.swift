import UIKit
import Flutter
import Firebase
import AVFoundation

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FirebaseApp.configure()
    GeneratedPluginRegistrant.register(with: self)


    // Notifications
    // application.registerForRemoteNotifications()
    
    if #available(iOS 10.0, *) {
     UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }

  //   do {
  //   if #available(iOS 10.0, *){
  //       try AVAudioSession.sharedinstance().setcategory(AVAudioSessionCategoryPlayback, mode: AVAudioSessionModeDefault)
  //       try AVAudioSession.sharedinstance().setactive(true)
  //   }
  // } catch {
  //   print(error)
  // }

    // Dev Code

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
