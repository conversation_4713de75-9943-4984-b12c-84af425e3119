// ignore_for_file: use_build_context_synchronously, file_names

import 'package:flutter/material.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import 'main.dart';

// class DynamicLinksApi {
final dynamicLink = FirebaseDynamicLinks.instance;
String get uriPrefix => Platform.isIOS
    ? 'https://nineandbeyond.page.link'
    : 'https://nineandbeyond.page.link';

createAppLink() async {
  final DynamicLinkParameters dynamicLinkParameters = DynamicLinkParameters(
    uriPrefix: uriPrefix,
    link: Uri.parse('$uriPrefix/share'),
    androidParameters:
        const AndroidParameters(packageName: 'com.nineandbeyond.app'),
    iosParameters: const IOSParameters(bundleId: 'com.nineandbeyond.userapp'),
    socialMetaTagParameters: const SocialMetaTagParameters(
      title: '9 & Beyond',
      description: 'Join the app now!!',
    ),
  );

  final ShortDynamicLink shortLink =
      await FirebaseDynamicLinks.instance.buildShortLink(
    dynamicLinkParameters,
    shortLinkType: ShortDynamicLinkType.unguessable,
  );

  final RenderBox? box =
      navigatorKey.currentContext!.findRenderObject() as RenderBox?;

  await Share.share(
    '${shortLink.shortUrl}',
    subject: '',
    sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
  );
}

createActivityLink(String activityId, String activityType) async {
  final DynamicLinkParameters dynamicLinkParameters = DynamicLinkParameters(
    uriPrefix: uriPrefix,
    link: Uri.parse(
        '$uriPrefix/activity?activityId=$activityId&activityType=$activityType'),
    androidParameters:
        const AndroidParameters(packageName: 'com.nineandbeyond.app'),
    iosParameters: const IOSParameters(bundleId: 'com.nineandbeyond.userapp'),
    socialMetaTagParameters: const SocialMetaTagParameters(
      title: '9 & Beyond',
      description: 'Join the app now!!',
    ),
  );

  final ShortDynamicLink shortLink =
      await FirebaseDynamicLinks.instance.buildShortLink(
    dynamicLinkParameters,
    shortLinkType: ShortDynamicLinkType.unguessable,
  );

  final RenderBox? box =
      navigatorKey.currentContext!.findRenderObject() as RenderBox?;

  await Share.share(
    '${shortLink.shortUrl}',
    subject: '',
    sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
  );
}
