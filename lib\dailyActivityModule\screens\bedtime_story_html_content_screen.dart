// ignore_for_file: deprecated_member_use, avoid_print, prefer_typing_uninitialized_variables
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:nine_and_beyond/commonWidgets/asset_svg_icon.dart';
import 'package:nine_and_beyond/dailyActivityModule/model/daily_activity_model.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../common_functions.dart';
import '../../dynamic_Link_api.dart';
import '../../navigation/navigators.dart';
import '../providers/daily_activity_provider.dart';
import '../widgets/bedtime_story_widget.dart';

class BedtimeStoryHtmlContentScreen extends StatefulWidget {
  final BedtimeStoryHtmlContentArguments args;

  const BedtimeStoryHtmlContentScreen({super.key, required this.args});

  @override
  State<BedtimeStoryHtmlContentScreen> createState() =>
      _BedtimeStoryHtmlContentScreenState();
}

class _BedtimeStoryHtmlContentScreenState
    extends State<BedtimeStoryHtmlContentScreen> {
  //
  Map language = {};
  double dW = 0.0;
  double dH = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late BedTimeCategory currentCategory;

  myInit() {
    currentCategory = widget.args.bedtimeStory.category;
  }

  @override
  void didUpdateWidget(covariant BedtimeStoryHtmlContentScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.args.bedtimeStory.id != widget.args.bedtimeStory.id) {
      myInit();
    }
  }

  @override
  void initState() {
    super.initState();
    // user = Provider.of<AuthProvider>(context, listen: false).user;

    myInit();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final bedtimeStories = Provider.of<DailyActivityProvider>(context)
        .getBedtimeStoryUsingCategory(widget.args.selectedPeriod!);

    return widget.args.minimised
        ? GestureDetector(
            onTap: () {
              if (widget.args.toggleAudioPlayer != null) {
                widget.args.toggleAudioPlayer!(false);
              }
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              color: const Color(0XFFD6EAFF),
              height: dW * 0.27,
              width: dW,
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(3),
                    child: CachedImageWidget(
                      widget.args.bedtimeStory.albumArt,
                      boxFit: BoxFit.cover,
                      width: dW * 0.26,
                      height: dW * 0.27,
                    ),
                  ),
                  SizedBox(width: dW * 0.04),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: dW * 0.4,
                        child: Text(
                          widget.args.bedtimeStory.name,
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF1D1E22),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      SizedBox(height: dW * 0.02),
                      Text(
                        language['continueRead'],
                        style: textTheme.headline3!.copyWith(
                          fontSize: tS * 10,
                          color: const Color(0XFF515259),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () {
                          if (widget.args.toggleAudioPlayer != null) {
                            widget.args.toggleAudioPlayer!(false, true);
                          }
                        },
                        child: Padding(
                          padding: EdgeInsets.only(right: dW * 0.04),
                          child: const Icon(Icons.clear,
                              color: Colors.black, size: 20),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )
        // onWillPop: () async {
        //   if (widget.args.toggleAudioPlayer != null) {
        //     widget.args.toggleAudioPlayer!(true);
        //   } else if (widget.args.bedtimeStory.htmlContent.isNotEmpty) {
        //     pop();
        //   }
        //   return false;
        // },
        : Scaffold(
            body: SizedBox(
            height: dH,
            width: dW,
            child: Column(
              children: [
                SizedBox(height: dW * 0.12),
                Padding(
                  padding: EdgeInsets.only(left: dW * 0.05, right: dW * 0.05),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          if (widget.args.toggleAudioPlayer != null) {
                            widget.args.toggleAudioPlayer!(true);
                          } else if (widget
                              .args.bedtimeStory.htmlContent.isNotEmpty) {
                            pop();
                          }
                        },
                        child: const Icon(Icons.arrow_back),
                      ),
                      SizedBox(width: dW * 0.04),
                      Text(
                        language['back'],
                        style: textTheme.headline1!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFF1D1E22),
                        ),
                      ),
                      const Spacer(),
                      widget.args.bedtimeStory.audio.isNotEmpty
                          ? GestureDetector(
                              onTap: () {},
                              child: const AssetSvgIcon('share'),
                            )
                          : Theme(
                              data: Theme.of(context).copyWith(
                                highlightColor: Colors.transparent,
                                splashColor: Colors.transparent,
                              ),
                              child: PopupMenuButton(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                padding: EdgeInsets.zero,
                                itemBuilder: (BuildContext bc) => [
                                  popupMenuItem(
                                    position: 1,
                                    title: language['like'],
                                    icon: 'like',
                                    dW: dW,
                                  ),
                                  popupMenuItem(
                                    position: 2,
                                    title: language['shareNow'],
                                    icon: 'share_now',
                                    dW: dW,
                                  ),
                                ],
                                onSelected: (value) {
                                  if (value == 1) {
                                  } else if (value == 2) {
                                    createActivityLink(
                                        widget.args.bedtimeStory.id,
                                        'Bedtime Story');
                                  }
                                },
                                child: const Icon(
                                  Icons.more_vert_rounded,
                                  size: 22,
                                  color: Color(0xff1D1E22),
                                ),
                              ),
                            ),
                    ],
                  ),
                ),
                SizedBox(height: dW * 0.07),
                Expanded(
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: dW * 0.05),
                        Padding(
                          padding: EdgeInsets.only(
                              left: dW * 0.03, right: dW * 0.03),
                          child:
                              Html(data: widget.args.bedtimeStory.htmlContent),
                        ),
                        SizedBox(height: dW * 0.1),
                        Padding(
                          padding: EdgeInsets.only(
                              left: dW * 0.05, right: dW * 0.05),
                          child: Text(
                            language['recommendedArticles'],
                            style: textTheme.headline1!.copyWith(
                              fontSize: tS * 14,
                              color: const Color(0XFF1D1E22),
                            ),
                          ),
                        ),
                        ListView.builder(
                          shrinkWrap: true,
                          itemCount: bedtimeStories
                              .where((story) =>
                                  story.type == 'Read' &&
                                  story.category.id == currentCategory.id &&
                                  story != widget.args.bedtimeStory)
                              .length,
                          physics: const BouncingScrollPhysics(),
                          itemBuilder: (context, i) {
                            final readBedtimeStories = bedtimeStories
                                .where((story) =>
                                    story.type == 'Read' &&
                                    story.category.id == currentCategory.id &&
                                    story != widget.args.bedtimeStory)
                                .toList();
                            return GestureDetector(
                              onTap: () {
                                if (widget.args.playOrView != null) {
                                  widget
                                      .args.playOrView!(readBedtimeStories[i]);
                                }
                              },
                              child: BedtimeStoryWidget(
                                playOrView: widget.args.playOrView,
                                minimised: widget.args.minimised,
                                toggleAudioPlayer:
                                    widget.args.toggleAudioPlayer,
                                bedtimeStory: readBedtimeStories[i],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ));
  }
}
