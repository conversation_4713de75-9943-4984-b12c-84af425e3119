// ignore_for_file: deprecated_member_use, unused_element

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';
import '../providers/daily_activity_provider.dart';
import 'option_bottom_sheet.dart';

class ChantsWidget extends StatefulWidget {
  final Chant chant;
  final String activityTitle;
  final int? selectedPeriod;
  final int? currentIndex;
  final bool? minimised;
  final Function? toggleAudioPlayer;
  final Function? playAudio;

  const ChantsWidget({
    super.key,
    required this.chant,
    required this.activityTitle,
    this.selectedPeriod,
    this.currentIndex,
    this.minimised,
    this.toggleAudioPlayer,
    this.playAudio,
  });

  @override
  State<ChantsWidget> createState() => _ChantsWidgetState();
}

class _ChantsWidgetState extends State<ChantsWidget> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final chant =
        Provider.of<DailyActivityProvider>(context, listen: false).chants;

    return Padding(
      padding: EdgeInsets.only(left: dW * 0.04),
      child: Column(
        children: [
          Container(
            color: transparentColor,
            child: Row(
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(3),
                      child: CachedImageWidget(
                        widget.chant.albumArt,
                        boxFit: BoxFit.cover,
                        width: dW * 0.175,
                        height: dW * 0.175,
                      ),
                    ),
                    if (widget.chant.audio.isEmpty)
                      Positioned(
                        top: dW * 0.055,
                        right: dW * 0.055,
                        child: const AssetSvgIcon(
                          's_lock',
                        ),
                      ),
                  ],
                ),
                SizedBox(width: dW * 0.035),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: dW * 0.5,
                      child: Text(
                        widget.chant.name,
                        style: textTheme.headline1!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFF1D1E22),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(height: dW * 0.03),
                    Text(
                      widget.chant.duration,
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 10,
                        color: const Color(0XFF84858E),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    if (widget.chant.audio.isNotEmpty) {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(15.0),
                            topRight: Radius.circular(15.0),
                          ),
                        ),
                        builder: (BuildContext context) => OptionBottomSheet(
                          playAudio: widget.playAudio,
                          chant: widget.chant,
                          currentIndex: widget.currentIndex!,
                          activityTitle: widget.activityTitle,
                          selectedPeriod: widget.selectedPeriod,
                          minimised: widget.minimised,
                          toggleAudioPlayer: widget.toggleAudioPlayer,
                        ),
                      );
                    } else {
                      buySubscriptionDialog();
                    }
                  },
                  icon: const Icon(
                    Icons.more_vert_rounded,
                    size: 22,
                    color: Color(0xff1D1E22),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.04),
        ],
      ),
    );
  }
}
