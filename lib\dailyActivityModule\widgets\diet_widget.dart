// // ignore_for_file: deprecated_member_use, unused_element

// import 'package:flutter/material.dart';
// import 'package:nine_and_beyond/colors.dart';
// import 'package:provider/provider.dart';
// import '../../authModule/providers/auth_provider.dart';
// import '../../commonWidgets/cached_image_widget.dart';
// import '../../commonWidgets/gradient_widget.dart';
// import '../../navigation/arguments.dart';
// import '../../navigation/navigators.dart';
// import '../../navigation/routes.dart';
// import '../model/daily_activity_model.dart';

// class DietWidget extends StatefulWidget {
//   final Diet diet;
//   const DietWidget({
//     super.key,
//     required this.diet,
//   });

//   @override
//   State<DietWidget> createState() => _DietWidgetState();
// }

// class _DietWidgetState extends State<DietWidget> {
//   //
//   Map language = {};
//   double dH = 0.0;
//   double dW = 0.0;
//   double tS = 0.0;
//   TextTheme get textTheme => Theme.of(context).textTheme;

//   bool isExpanded = false;

//   @override
//   Widget build(BuildContext context) {
//     dW = MediaQuery.of(context).size.width;
//     tS = MediaQuery.of(context).textScaleFactor;
//     language = Provider.of<AuthProvider>(context).selectedLanguage;

//     // final diet =
//     //     Provider.of<DailyActivityProvider>(context, listen: false).diet;

//     return Container(
//       margin: EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(4),
//         border: Border.all(
//           width: 1,
//           color: const Color(0XFF975EFF).withOpacity(0.09),
//         ),
//         boxShadow: [
//           BoxShadow(
//             offset: const Offset(0, 3.845613),
//             blurRadius: 6,
//             spreadRadius: 0,
//             color: const Color(0XFF975EFF).withOpacity(0.1),
//           ),
//         ],
//       ),
//       child: ExpansionTile(
//         backgroundColor: const Color(0XFFF5F7FD),
//         collapsedBackgroundColor: white,
//         onExpansionChanged: (expanded) {
//           setState(() {
//             isExpanded = expanded;
//           });
//         },
//         title: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             SizedBox(height: dW * 0.04),
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 ClipRRect(
//                   borderRadius: BorderRadius.circular(4),
//                   child: CachedImageWidget(
//                     widget.diet.pictureUrl,
//                     boxFit: BoxFit.cover,
//                     width: dW * 0.15,
//                     height: dW * 0.15,
//                   ),
//                 ),
//                 SizedBox(width: dW * 0.035),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     SizedBox(height: dW * 0.015),
//                     isExpanded == true
//                         ? GradientWidget(
//                             gradient: const LinearGradient(
//                               colors: [
//                                 Color(0xffCE1B69),
//                                 Color(0xffFF328B),
//                               ],
//                             ),
//                             child: Text(
//                               widget.diet.title,
//                               style: const TextStyle(
//                                 fontSize: 14,
//                                 fontWeight: FontWeight.w500,
//                                 fontFamily: 'Inter',
//                               ),
//                             ),
//                           )
//                         : Text(
//                             widget.diet.title,
//                             style: textTheme.headline2!.copyWith(
//                               fontSize: tS * 14,
//                               color: const Color(0XFF000000),
//                             ),
//                           ),
//                     SizedBox(height: dW * 0.03),
//                     Text(
//                       '(${widget.diet.calories.toString()} kcals)',
//                       style: textTheme.headline3!.copyWith(
//                         fontSize: tS * 10,
//                         color: const Color(0XFF515259),
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//             SizedBox(height: dW * 0.04),
//           ],
//         ),
//         childrenPadding: EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
//         children: [
//           Padding(
//             padding: EdgeInsets.only(top: dW * 0.03, bottom: dW * 0.08),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 for (int i = 0; i < 75; i++)
//                   Container(
//                     width: 4,
//                     height: 1,
//                     color: i % 2 == 0
//                         ? const Color(0XFFDBDBE3)
//                         : Colors.transparent,
//                   ),
//               ],
//             ),
//           ),
//           Column(
//             children: [
//               for (var product in widget.diet.products)
//                 Padding(
//                   padding: EdgeInsets.only(bottom: dW * 0.07),
//                   child: GestureDetector(
//                     onTap: () {
//                       push(
//                         NamedRoute.dietDetailsScreen,
//                         arguments: DietDetailsArguments(
//                           videoUrl: product.videoUrl,
//                           videoTitle: product.videoTitle,
//                           ingredients: product.ingredients,
//                           steps: product.steps,
//                         ),
//                       );
//                     },
//                     child: Row(
//                       children: [
//                         ClipRRect(
//                           borderRadius: BorderRadius.circular(4),
//                           child: CachedImageWidget(
//                             product.productPictureUrl,
//                             boxFit: BoxFit.cover,
//                             width: dW * 0.15,
//                             height: dW * 0.15,
//                           ),
//                         ),
//                         SizedBox(width: dW * 0.035),
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             SizedBox(
//                               width: dW * 0.5,
//                               child: Text(
//                                 product.name,
//                                 style: textTheme.headline2!.copyWith(
//                                   fontSize: tS * 14,
//                                   color: const Color(0XFF000000),
//                                 ),
//                                 overflow: TextOverflow.ellipsis,
//                               ),
//                             ),
//                             SizedBox(height: dW * 0.03),
//                             Row(
//                               children: [
//                                 Text(
//                                   '${product.weight.toString()} gm',
//                                   style: textTheme.headline3!.copyWith(
//                                     fontSize: tS * 10,
//                                     color: const Color(0XFF515259),
//                                   ),
//                                 ),
//                                 Container(
//                                   height: dW * 0.03,
//                                   width: dW * 0.002,
//                                   margin: EdgeInsets.only(
//                                       left: dW * 0.01, right: dW * 0.01),
//                                   color: const Color(0XFF515259),
//                                 ),
//                                 Text(
//                                   '${product.calories.toString()} kcals',
//                                   style: textTheme.headline3!.copyWith(
//                                     fontSize: tS * 10,
//                                     color: const Color(0XFF515259),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                         const Spacer(),
//                         Padding(
//                           padding: EdgeInsets.only(right: dW * 0.015),
//                           child: const Icon(Icons.arrow_forward_ios, size: 15),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
// }

// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';

class DietWidget extends StatefulWidget {
  final Diet diet;
  final int? selectedTrimester;
  const DietWidget({super.key, required this.diet, this.selectedTrimester});

  @override
  State<DietWidget> createState() => _DietWidgetState();
}

class _DietWidgetState extends State<DietWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    language['unlockPremiumFeatures'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    language['toUnlock'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          pop();
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    final diet =
        Provider.of<DailyActivityProvider>(context, listen: false).diet;

    return Padding(
      padding: EdgeInsets.only(left: dW * 0.04, right: dW * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              if (widget.diet.video.isNotEmpty) {
                push(
                  NamedRoute.dietDetailsScreen,
                  arguments: DietDetailsArguments(
                    trimester: widget.selectedTrimester.toString(),
                    diet: diet,
                    selectedDiet: widget.diet,
                  ),
                );
              } else {
                buySubscriptionDialog();
              }
            },
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: CachedImageWidget(
                    widget.diet.thumbnail,
                    boxFit: BoxFit.cover,
                    width: dW,
                    height: dW * 0.5,
                  ),
                ),
                Positioned(
                  top: dW * 0.175,
                  left: 0,
                  right: dW * 0.03,
                  child: Column(
                    children: [
                      const AssetSvgIcon('play_in_circle_yoga'),
                      SizedBox(height: dW * 0.02),
                      Text(
                        language['start'],
                        style: textTheme.headline2!.copyWith(
                          fontSize: tS * 12,
                          color: const Color(0XFFFFFFFF),
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.diet.video.isEmpty)
                  Positioned(
                    top: dW * 0.03,
                    right: dW * 0.03,
                    child: const AssetSvgIcon(
                      's_lock',
                    ),
                  ),
              ],
            ),
          ),
          SizedBox(height: dW * 0.04),
          Text(
            widget.diet.name,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 14,
              color: const Color(0XFF1D1E22),
            ),
          ),
          SizedBox(height: dW * 0.025),
          Text(
            widget.diet.duration,
            style: textTheme.headline2!.copyWith(
              fontSize: tS * 12,
              color: const Color(0XFF6B6C75),
            ),
          ),
          SizedBox(height: dW * 0.07),
        ],
      ),
    );
  }
}
