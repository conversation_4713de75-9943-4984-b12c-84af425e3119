// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../authModule/providers/auth_provider.dart';
import '../common_functions.dart';
import '../navigation/navigators.dart';
import '../navigation/routes.dart';

class PolicyTextWidget extends StatelessWidget {
  PolicyTextWidget({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              language['byContinueAgree'],
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontFamily: 'Inter',
                fontSize: tS * 10,
                color: const Color(0xff6B6C75),
              ),
            ),
            GestureDetector(
              onTap: () {
                push(NamedRoute.privacyPolicyOrTermsAndConditionsScreen,
                    arguments: PrivacyPolicyOrTermsAndConditionsArguments(
                        title: language['tnc'],
                        contentType: 'TERMSANDCONDITIONS'));
              },
              child: Text(
                '${language['tos']}',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontFamily: 'Inter',
                  color: const Color(0xff515259),
                  fontSize: tS * 10,
                ),
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(top: dW * 0.025),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                ' ${language['and']} ',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                  fontSize: tS * 10,
                  color: const Color(0xff6B6C75),
                ),
              ),
              GestureDetector(
                onTap: () {
                  push(NamedRoute.privacyPolicyOrTermsAndConditionsScreen,
                      arguments: PrivacyPolicyOrTermsAndConditionsArguments(
                          title: language['privacyPolicy'],
                          contentType: 'PRIVACYPOLICY'));
                },
                child: Text(
                  language['privacyPolicy'],
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Inter',
                    color: const Color(0xff515259),
                    fontSize: tS * 10,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
