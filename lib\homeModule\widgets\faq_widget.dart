// ignore_for_file: must_be_immutable, library_private_types_in_public_api, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../models/article_model.dart';

class FaqWidget extends StatelessWidget {
  final List<Faq> faqs;
  FaqWidget({Key? key, required this.faqs}) : super(key: key);

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      color: const Color(0XFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: dW * 0.07,
                bottom: dW * 0.035,
                left: dW * 0.04,
                right: dW * 0.04),
            child: Text(
              language['faqs'],
              style: Theme.of(context).textTheme.headline1!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0XFF1D1E22),
                  ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
                bottom: dW * 0.05, left: dW * 0.04, right: dW * 0.04),
            child: Column(
              children: faqs.map((e) {
                return FaqItem(question: e.question, answer: e.answer);
              }).toList(),
            ),
          ),
          SizedBox(height: dW * 0.25),
        ],
      ),
    );
  }
}

class FaqItem extends StatefulWidget {
  final String question;
  final String answer;
  const FaqItem({super.key, required this.question, required this.answer});

  @override
  _FaqItemState createState() => _FaqItemState();
}

class _FaqItemState extends State<FaqItem> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: const Color(0XFFF7F8FC),
          borderRadius: BorderRadius.circular(4)),
      margin: EdgeInsets.all(MediaQuery.of(context).size.width * 0.015),
      padding: isExpanded == true
          ? EdgeInsets.only(bottom: MediaQuery.of(context).size.width * 0.05)
          : const EdgeInsets.only(bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            onFocusChange: (expanded) {
              setState(() {
                isExpanded = expanded;
              });
            },
            title: Padding(
              padding: EdgeInsets.only(
                  left: MediaQuery.of(context).size.width * 0.01),
              child: isExpanded == true
                  ? GradientWidget(
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xffB56AEB),
                          Color(0xffE38C7C),
                        ],
                      ),
                      child: Text(
                        widget.question,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Inter',
                        ),
                      ),
                    )
                  : Text(
                      widget.question,
                      style: Theme.of(context).textTheme.headline2!.copyWith(
                            fontSize: 11,
                            color: const Color(0XFF37383F),
                          ),
                    ),
            ),
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            trailing: Icon(
              isExpanded ? Icons.close : Icons.add,
              color: const Color(0XFF975EFF),
              size: 18,
            ),
          ),
          if (isExpanded)
            Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: MediaQuery.of(context).size.width * 0.06),
              child: Text(
                widget.answer,
                style: Theme.of(context).textTheme.headline3!.copyWith(
                    fontSize: 10, color: const Color(0XFF515259), height: 1.75),
              ),
            ),
        ],
      ),
    );
  }
}
