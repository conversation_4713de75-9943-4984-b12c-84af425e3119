// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';

import '../colors.dart';
import '../common_functions.dart';
import 'gradient_widget.dart';

class CustomDialogBox extends StatelessWidget {
  final String title;
  final Widget firstButton;
  final Widget? secondButton;
  final MainAxisAlignment buttonsAxisAligment;

  const CustomDialogBox({
    Key? key,
    required this.title,
    required this.firstButton,
    this.secondButton,
    this.buttonsAxisAligment = MainAxisAlignment.end,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double dW = MediaQuery.of(context).size.width;
    final double tS = MediaQuery.of(context).textScaleFactor;

    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
      contentPadding: EdgeInsets.symmetric(
        horizontal: dW * 0.1,
        vertical: dW * 0.05,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      content: SizedBox(
        width: dW,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
                alignment: Alignment.centerLeft,
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.headline2!.copyWith(
                        fontSize: tS * 14,
                        height: 1.6,
                        color: lightBlack,
                      ),
                )),
            SizedBox(height: dW * 0.07),
            Row(
              mainAxisAlignment: buttonsAxisAligment,
              children: [
                firstButton,
                if (secondButton != null) ...[
                  SizedBox(width: dW * 0.04),
                  secondButton!,
                ]
              ],
            )
          ],
        ),
      ),
    );
  }
}

class DialogTextButton extends StatelessWidget {
  final Function onPressed;
  final String text;
  const DialogTextButton(
      {super.key, required this.onPressed, required this.text});

  @override
  Widget build(BuildContext context) {
    final double dW = MediaQuery.of(context).size.width;
    final double tS = MediaQuery.of(context).textScaleFactor;
    return GestureDetector(
      onTap: () => onPressed(),
      child: Container(
        padding:
            EdgeInsets.symmetric(horizontal: dW * 0.04, vertical: dW * 0.02),
        color: Colors.white,
        child: GradientWidget(
          gradient: linearGradient,
          child: Text(
            text,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: tS * 12,
            ),
          ),
        ),
      ),
    );
  }
}

class FilledDialogButton extends StatelessWidget {
  final Function onPressed;
  final String text;
  const FilledDialogButton(
      {super.key, required this.onPressed, required this.text});

  @override
  Widget build(BuildContext context) {
    final double dW = MediaQuery.of(context).size.width;
    final double tS = MediaQuery.of(context).textScaleFactor;
    final textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: () => onPressed(),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: dW * 0.05,
          vertical: dW * 0.035,
        ),
        decoration: BoxDecoration(
          // color: Theme.of(context).primaryColor,
          gradient: linearGradient,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          text,
          style: textTheme.headline2!.copyWith(
            color: white,
            fontSize: tS * 12,
          ),
        ),
      ),
    );
  }
}
