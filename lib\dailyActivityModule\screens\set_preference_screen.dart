// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/dailyActivityModule/providers/daily_activity_provider.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../model/daily_activity_model.dart';
import '../widgets/set_preference_widget.dart';

class SetPreferenceScreen extends StatefulWidget {
  final SetPreferenceArguments args;
  const SetPreferenceScreen({super.key, required this.args});

  @override
  State<SetPreferenceScreen> createState() => _SetPreferenceScreenState();
}

class _SetPreferenceScreenState extends State<SetPreferenceScreen> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;
  bool isLoading = false;

  List<Preferences> preferences = [];

  fetchPreference() async {
    setState(() => isLoading = true);
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchPreferences(
      accessToken: user.accessToken,
    );
    if (!response['success']) {
      showSnackbar(response['message']);
    }
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchPreference();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    preferences = Provider.of<DailyActivityProvider>(context).preference;

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(title: widget.args.title, dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    final newPreferences = preferences
        .where((preference) =>
            preference.dailyActivity.title != 'Early Morning Snack' &&
            preference.dailyActivity.title != 'Breakfast' &&
            preference.dailyActivity.title != 'Lunch' &&
            preference.dailyActivity.title != 'Evening snack' &&
            preference.dailyActivity.title != 'Dinner')
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: newPreferences.length,
            physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            itemBuilder: (context, i) => SetPreferenceWidget(
              preference: newPreferences[i],
              preferences: newPreferences,
            ),
          ),
        ),
      ],
    );
  }
}
