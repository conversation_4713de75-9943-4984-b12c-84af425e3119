// ignore_for_file: deprecated_member_use, avoid_print
import 'dart:io';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/navigation/navigators.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/diary_background.dart';
import '../../common_functions.dart';
import '../providers/diary_provider.dart';

class PlayAudioScreen extends StatefulWidget {
  final PlayAudioArguments args;
  const PlayAudioScreen({super.key, required this.args});

  @override
  State<PlayAudioScreen> createState() => _PlayAudioScreenState();
}

class _PlayAudioScreenState extends State<PlayAudioScreen>
    with TickerProviderStateMixin {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  Duration currentPos = Duration.zero;
  Duration duration = Duration.zero;
  final player = AudioPlayer();
  bool isPlaying = false;
  bool isPaused = false;
  int currentIndex = 0;

  seekToDuration(double d) async {
    await player.seek(Duration(milliseconds: d.toInt()));
    currentPos = Duration(milliseconds: d.toInt());
    setState(() {});
  }

  play() async {
    try {
      final recordings =
          Provider.of<DiaryProvider>(context, listen: false).recordings;

      await player.play(UrlSource(recordings[currentIndex].recording));
      isPaused = false;
      setState(() {});
    } catch (e) {
      print(e);
      showSnackbar(language['failedPlay'], Colors.red);
    }
  }

  pause() async {
    await player.pause();
    isPaused = true;
    setState(() {});
  }

  resume() async {
    await player.resume();
    isPaused = false;
    setState(() {});
  }

  initAudioPlayer() {
    if (Platform.isIOS) {
      final AudioContext audioContext = AudioContext(
        iOS: AudioContextIOS(
          defaultToSpeaker: true,
          category: AVAudioSessionCategory.playback,
          options: [
            AVAudioSessionOptions.defaultToSpeaker,
            AVAudioSessionOptions.mixWithOthers,
            AVAudioSessionOptions.allowBluetoothA2DP,
            AVAudioSessionOptions.allowBluetooth,
          ],
        ),
        android: AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.music,
          usageType: AndroidUsageType.assistanceSonification,
          audioFocus: AndroidAudioFocus.gain,
        ),
      );
      AudioPlayer.global.setGlobalAudioContext(audioContext);
    }

    player.onPlayerStateChanged.listen((state) {
      setState(() {
        isPlaying = state == PlayerState.playing;
      });
    });

    player.onDurationChanged.listen((newDuration) {
      setState(() {
        duration = newDuration;
      });
    });

    player.onPositionChanged.listen((newPos) {
      setState(() {
        currentPos = newPos;
      });
    });

    player.onPlayerComplete.listen((event) {
      setState(() {
        isPlaying = false;
        currentPos = Duration.zero;
      });
    });
  }

  releasePlayer() async {
    await player.release();
  }

  @override
  void initState() {
    super.initState();
    // user = Provider.of<AuthProvider>(context, listen: false).user;
    currentIndex = widget.args.currentIndex;
    play();
    initAudioPlayer();
  }

  @override
  void dispose() {
    super.dispose();
    // if (_timer != null) _timer!.cancel();
    releasePlayer();
    player.onPlayerStateChanged.drain();
    player.onDurationChanged.drain();
    player.onPositionChanged.drain();
    player.onPlayerComplete.drain();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    int currentMinutes = currentPos.inMinutes;
    int currentSeconds = currentPos.inSeconds % 60;

    int durationMinutes = duration.inMinutes;
    int durationSeconds = duration.inSeconds % 60;

    final recordings = Provider.of<DiaryProvider>(context).recordings;

    final recording = widget.args.recordings[currentIndex];

    return Stack(
      children: [
        DiaryBackground(),
        Scaffold(
            backgroundColor: Colors.transparent,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.topCenter,
                  color: const Color(0xff8EBEF4).withOpacity(0.4),
                  width: dW,
                  height: dW * 0.25,
                  padding:
                      EdgeInsets.only(left: 16, top: dW * 0.065, right: 16),
                  child: SafeArea(
                    child: Row(
                      children: [
                        Text(
                          DateFormat('d MMM, y').format(recording.createdAt),
                          style: textTheme.headline1!.copyWith(
                            fontSize: tS * 12,
                            color: const Color(0XFF363636),
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () {
                            pop();
                          },
                          child: const AssetSvgIcon(
                            'close_cross',
                            color: Color(0XFF363636),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: dW * 0.05),
                Container(
                  height: dW * 1.1,
                  width: dW,
                  margin: const EdgeInsets.only(left: 8, right: 8),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                    color: const Color(0xffFFFFFF).withOpacity(0.6),
                  ),
                  child: SingleChildScrollView(
                    physics: const ScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                              left: 21, right: 21, top: dW * 0.055),
                          child: Text(
                            recording.name,
                            style: textTheme.headline1!.copyWith(
                              fontSize: tS * 16,
                              color: const Color(0XFF363636),
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(
                              left: 21, right: 21, top: dW * 0.045),
                          height: 1,
                          width: dW,
                          color: const Color(0xffDBDBE3),
                        ),
                        // Padding(
                        //   padding: EdgeInsets.only(
                        //       left: 21,
                        //       right: 21,
                        //       top: dW * 0.06,
                        //       bottom: dW * 0.09),
                        //   child: Text(
                        //     widget.args.lyrics,
                        //     style: textTheme.headline3!.copyWith(
                        //       fontSize: tS * 14,
                        //       color: const Color(0XFFFFFFFF),
                        //       height: 2,
                        //     ),
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                ),
                Container(
                  height: dW * 0.55,
                  width: dW,
                  margin: const EdgeInsets.only(left: 8, right: 8),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                    border: Border.all(
                      width: 1,
                      color: const Color(0xffFFFFFF).withOpacity(0.5),
                    ),
                    color: const Color(0xffFFFFFF).withOpacity(0.6),
                  ),
                  child: Column(
                    children: [
                      SizedBox(height: dW * 0.05),
                      Slider(
                        activeColor: const Color(0xff975EFF),
                        inactiveColor: const Color(0xffF4F4F4),
                        value: currentPos.inMilliseconds.toDouble(),
                        onChanged: seekToDuration,
                        max: duration.inMilliseconds.toDouble(),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 25, right: 25),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '$currentMinutes:${currentSeconds.toString().padLeft(2, '0')}',
                              style: textTheme.headline3!.copyWith(
                                fontSize: tS * 14,
                                color: const Color(0XFF363636),
                              ),
                            ),
                            Text(
                              '$durationMinutes:${durationSeconds.toString().padLeft(2, '0')}',
                              style: textTheme.headline3!.copyWith(
                                fontSize: tS * 14,
                                color: const Color(0XFF363636),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dW * 0.06),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          GestureDetector(
                              onTap: () {
                                if (currentIndex > 0) {
                                  currentIndex--;
                                  play();
                                }
                              },
                              child: const AssetSvgIcon('previous')),
                          Container(
                            height: dW * 0.17,
                            width: dW * 0.17,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xffFFFFFF),
                              borderRadius: BorderRadius.circular(108),
                            ),
                            child: GestureDetector(
                                onTap: () => isPlaying
                                    ? pause()
                                    : isPaused
                                        ? resume()
                                        : play(),
                                child: AssetSvgIcon(
                                  isPlaying ? 'pause' : 'play',
                                )),
                          ),
                          GestureDetector(
                              onTap: () {
                                if (currentIndex < recordings.length - 1) {
                                  currentIndex++;
                                  play();
                                }
                              },
                              child: const AssetSvgIcon('next')),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            )),
      ],
    );
  }
}
