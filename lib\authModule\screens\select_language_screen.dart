// ignore_for_file: use_build_context_synchronously, deprecated_member_use, must_be_immutable
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:provider/provider.dart';
import '../../colors.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class SelectLanguageScreen extends StatefulWidget {
  final SelectLanguageScreenArguments args;
  const SelectLanguageScreen({Key? key, required this.args}) : super(key: key);

  @override
  SelectLanguageScreenState createState() => SelectLanguageScreenState();
}

class SelectLanguageScreenState extends State<SelectLanguageScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  bool isLoading = false;

  Map language = {};
  String selectedLanguageString = '';
  String currentLanguageString = '';

  bool isSetting = false;
  final LocalStorage storage = LocalStorage('9&BEYOND');

  List availableLanguages = [];

  fetchAndSetSelectedLanguageData() async {
    try {
      setState(() {
        isSetting = true;
      });

      final response = await Provider.of<AuthProvider>(context, listen: false)
          .getAppConfig(['user-$selectedLanguageString']);

      if (response['success']) {
        Provider.of<AuthProvider>(context, listen: false)
            .setLanguageInStorage(selectedLanguageString);

        currentLanguageString = selectedLanguageString;

        if (widget.args.fromOnboarding) {
          pushAndRemoveUntil(NamedRoute.onBoardingScreen);
        } else {
          pop();
        }
      } else {
        showSnackbar('Failed to set language');
      }
      //
    } finally {
      setState(() {
        isSetting = false;
      });
    }
  }

  fetchAvailableLanguages() async {
    try {
      setState(() {
        isLoading = true;
      });

      final response = await Provider.of<AuthProvider>(context, listen: false)
          .getAppConfig(['user_availableLanguages']);
      if (response['success'] &&
          response['result'] != null &&
          response['result'].isNotEmpty) {
        availableLanguages = response['result'][0]['value'];
        if (availableLanguages.isEmpty) {
          selectedLanguageString = 'english';
          fetchAndSetSelectedLanguageData();
        }
        // else if (availableLanguages.length == 1) {
        //   selectedLanguageString = availableLanguages[0];
        //   fetchAndSetSelectedLanguageData();
        // }
      } else {
        selectedLanguageString = 'english';
        fetchAndSetSelectedLanguageData();
      }
//
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  getCurrentLanguage() async {
    await storage.ready;
    var languageMap = storage.getItem('language');

    if (languageMap != null) {
      languageMap = json.decode(languageMap);
      selectedLanguageString = currentLanguageString = languageMap['language'];
    } else {
      selectedLanguageString = currentLanguageString = 'english';
    }
  }

  selectLang(String lang) => setState(() => selectedLanguageString = lang);

  @override
  void initState() {
    super.initState();
    fetchAvailableLanguages();
    getCurrentLanguage();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
      appBar: CustomAppBar(
        dW: dW,
        title: language['chooseYourLanguage'],
        leading: widget.args.fromOnboarding ? const SizedBox.shrink() : null,
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: screenHorizontalPadding(dW),
                    child: Column(
                      children: [
                        // SizedBox(height: dW * (iOSCondition(dH) ? 0.2 : 0.05)),
                        // Text(
                        //   language['chooseYourLanguage'],
                        //   textAlign: TextAlign.center,
                        //   style: Theme.of(context)
                        //       .textTheme
                        //       .headline1!
                        //       .copyWith(
                        //           fontSize: tS * 20, color: appBarTitleColor),
                        // ),
                        SizedBox(height: dW * 0.02),
                        Text(
                          language['chooseLanguageSub'],
                          textAlign: TextAlign.center,
                          style: Theme.of(context)
                              .textTheme
                              .headline3!
                              .copyWith(fontSize: tS * 14, color: lightGray),
                        ),
                        SizedBox(height: dW * 0.2),
                        if (availableLanguages.contains('hindi'))
                          LanguageWidget(
                            thisLanguage: 'hindi',
                            isSelected: selectedLanguageString == 'hindi',
                            selectLang: () => selectLang('hindi'),
                          ),
                        if (availableLanguages.contains('marathi'))
                          LanguageWidget(
                            thisLanguage: 'marathi',
                            isSelected: selectedLanguageString == 'marathi',
                            selectLang: () => selectLang('marathi'),
                          ),
                        if (availableLanguages.contains('english'))
                          LanguageWidget(
                            thisLanguage: 'english',
                            isSelected: selectedLanguageString == 'english',
                            selectLang: () => selectLang('english'),
                          ),
                      ],
                    ),
                  ),
                ),
                BottomAlignedWidget(
                    dW: dW,
                    dH: dH,
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.02),
                      child: GradientButton(
                        isLoading: isSetting,
                        buttonText: language['continue'],
                        onPressed: fetchAndSetSelectedLanguageData,
                      ),
                    )),
              ],
            ),
    );
  }
}

class LanguageWidget extends StatelessWidget {
  final String thisLanguage;
  final bool isSelected;
  final Function selectLang;

  LanguageWidget({
    super.key,
    required this.thisLanguage,
    required this.isSelected,
    required this.selectLang,
  });

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  Color cardBg = const Color(0xFFF6F6F6);

  Text get textWidget => Text(
        language[thisLanguage],
        style: TextStyle(
          fontSize: tS * 26,
          fontWeight:
              thisLanguage == 'english' ? FontWeight.w500 : FontWeight.w600,
          color: lightGray,
          letterSpacing: .5,
        ),
      );

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return GestureDetector(
      onTap: () => selectLang(),
      child: Container(
        margin: EdgeInsets.only(bottom: dW * 0.08),
        padding: const EdgeInsets.all(3),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(74),
          gradient: isSelected ? linearGradient : null,
          color: !isSelected ? cardBg : null,
        ),
        child: Container(
          width: dW * 0.69,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(74),
            color: cardBg,
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: isSelected ? linearGradient : null,
                  shape: BoxShape.circle,
                  color: !isSelected ? const Color(0xFFD9D9D9) : null,
                ),
                child: Center(child: AssetSvgIcon(thisLanguage)),
              ),
              Padding(
                padding: EdgeInsets.only(left: dW * 0.135),
                child: isSelected
                    ? GradientWidget(
                        gradient: linearGradient,
                        child: textWidget,
                      )
                    : textWidget,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
