// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';

import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/dotted_seperator.dart';
import '../../common_functions.dart';

class AskAnythingWidget extends StatelessWidget {
  AskAnythingWidget({super.key});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      margin: EdgeInsets.all(dW * 0.04),
      decoration: BoxDecoration(
        color: const Color(0XFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          width: 1,
          color: const Color(0XFF000000).withOpacity(0.03),
        ),
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 2),
            blurRadius: 15,
            spreadRadius: 0,
            color: const Color(0XFF000000).withOpacity(0.03),
          ),
        ],
      ),
      child: Row(children: [
        Container(
          margin: EdgeInsets.all(dW * 0.04),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: const Color(0XFFF7F3FF),
          ),
          child: Padding(
            padding: EdgeInsets.all(dW * 0.03),
            child: Image.asset(
              'assets/images/ask_anything_graphic.png',
              height: dW * 0.1,
            ),
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                language['askAny'],
                style: Theme.of(context).textTheme.headline2!.copyWith(
                    fontSize: tS * 14, color: const Color(0XFF1D1E22)),
              ),
              SizedBox(height: dW * 0.025),
              Padding(
                padding: EdgeInsets.only(right: dW * 0.175),
                child: const DottedSeperator(
                  height: 1,
                  width: 3,
                  color: Color(0XFFDBDBE3),
                ),
              ),
              SizedBox(height: dW * 0.025),
              Text(
                language['getExpert'],
                style: Theme.of(context).textTheme.headline3!.copyWith(
                    fontSize: tS * 12, color: const Color(0XFF747474)),
              ),
            ],
          ),
        ),
      ]),
    );
  }
}
