// ignore_for_file: must_be_immutable, deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../common_functions.dart';
import '../models/article_model.dart';

class GarbhSanskarArticleWidget extends StatelessWidget {
  final Article article;
  GarbhSanskarArticleWidget({super.key, required this.article});

  double dW = 0.0;
  double tS = 0.0;
  Map language = {};
  TextTheme get textTheme => Theme.of(bContext).textTheme;

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Container(
      color: transparentColor,
      margin: EdgeInsets.only(bottom: dW * 0.05),
      width: dW,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: CachedNetworkImage(
              repeat: ImageRepeat.repeat,
              fit: BoxFit.cover,
              width: dW * 0.28,
              height: dW * 0.3,
              imageUrl: article.imageUrl,
              placeholder: (_, __) => Image.asset(
                  'assets/placeholders/placeholder.png',
                  fit: BoxFit.cover),
            ),
          ),
          Container(
            // height: dW * 0.3,
            padding: EdgeInsets.only(
              left: dW * 0.03,
              top: dW * 0.015,
              bottom: dW * 0.015,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (article.videoUrl.isNotEmpty)
                  Text(
                    language['video'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 8,
                      color: const Color(0xFFCE1B69),
                    ),
                  ),
                if (article.videoUrl.isEmpty)
                  Text(
                    language['article'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 8,
                      color: const Color(0xFFCE1B69),
                    ),
                  ),
                Container(
                  margin: EdgeInsets.only(top: dW * 0.03),
                  width: dW * 0.55,
                  child: Text(
                    article.title,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 12,
                      color: lightBlack,
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: dW * 0.02),
                  width: dW * 0.55,
                  child: Text(
                    article.shortDescription,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: textTheme.headline3!.copyWith(
                      height: 1.6,
                      fontSize: tS * 10,
                      color: const Color(0xFF515259),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
