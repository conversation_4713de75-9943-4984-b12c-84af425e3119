// ignore_for_file: deprecated_member_use, unused_element, must_be_immutable

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/custom_dialog_box.dart';
import '../../commonWidgets/dotted_seperator.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../homeModule/widgets/preferences_bottom_sheet.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../model/daily_activity_model.dart';
import 'gradient_start_button.dart';

class DailyActivityWidget extends StatefulWidget {
  final DailyActivity dailyActivity;
  final bool fromGB;
  final List<DailyActivity> dailyActivities;
  const DailyActivityWidget({
    super.key,
    required this.dailyActivity,
    this.fromGB = false,
    required this.dailyActivities,
  });

  @override
  State<DailyActivityWidget> createState() => _DailyActivityWidgetState();
}

class _DailyActivityWidgetState extends State<DailyActivityWidget> {
  //
  Map language = {};
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late Map dailyActivityIds;

  bool isLoading = false;

  showPreferenceSheet() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: white,
      isDismissible: false,
      constraints: BoxConstraints(maxHeight: dH * 0.9),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      builder: (BuildContext context) => const PreferencesBottomSheet(),
    );
  }

  buySubscriptionDialog() async {
    return showDialog(
      context: context,
      builder: ((context) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: dW * 0.05),
            contentPadding: EdgeInsets.symmetric(
              horizontal: dW * 0.05,
              vertical: dW * 0.05,
            ),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  'assets/images/subscription_icon.png',
                  height: dW * 0.3,
                ),
                GradientWidget(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xffCE1B69),
                      const Color(0xffFF328B).withOpacity(0.71),
                    ],
                  ),
                  child: Text(
                    subscriptionDialog['title'],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Text(
                    subscriptionDialog['subTitle'],
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 16,
                      color: const Color(0xFF818181),
                      height: 1.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: dW * 0.03),
                  child: Row(
                    children: [
                      DialogTextButton(
                        onPressed: () {
                          pop();
                        },
                        text: language['close'],
                      ),
                      const Spacer(),
                      FilledDialogButton(
                        onPressed: () {
                          if (widget.fromGB) {
                            push(NamedRoute.bottomNavBarScreen,
                                arguments: BottomNavArgumnets(index: 1));
                          } else {
                            pop();
                          }
                          push(NamedRoute.subscriptionScreen);
                        },
                        text: language['clickHereToSubscribe'],
                      ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  late User user;

  Map subscriptionDialog = {};

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    user = Provider.of<AuthProvider>(context, listen: false).user;
    dailyActivityIds =
        Provider.of<AuthProvider>(context, listen: false).dailyActivityIds;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    subscriptionDialog = Provider.of<AuthProvider>(context).subscriptionDialog;

    final isLastItem = widget.dailyActivity == widget.dailyActivities.last;

    return Padding(
      padding:
          EdgeInsets.only(left: dW * 0.07, right: dW * 0.07, bottom: dW * 0.07),
      child: Column(
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: CachedImageWidget(
                  widget.dailyActivity.icon,
                  boxFit: BoxFit.cover,
                  width: dW * 0.1,
                  height: dW * 0.1,
                ),
              ),
              SizedBox(width: dW * 0.05),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.dailyActivity.title,
                    style: textTheme.headline2!.copyWith(
                      fontSize: tS * 12,
                      color: const Color(0XFF212121),
                    ),
                  ),
                  SizedBox(height: dW * 0.02),
                  Text(
                    widget.dailyActivity.subTitle,
                    style: textTheme.headline3!.copyWith(
                      fontSize: tS * 10,
                      color: const Color(0XFF707070),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              // SizedBox(
              //   width: dW * 0.2,
              //   height: dW * 0.1,
              //   child: GradientStartButton(
              //     icon: (widget.dailyActivity.id == dailyActivityIds['diet'] &&
              //                 user.plan!.content.contains('diet') ||
              //             widget.dailyActivity.id == dailyActivityIds['yoga'] &&
              //                 user.plan!.content.contains('yoga') ||
              //             widget.dailyActivity.id ==
              //                     dailyActivityIds['meditation'] &&
              //                 user.plan!.content.contains('meditation') ||
              //             widget.dailyActivity.id ==
              //                     dailyActivityIds['chants'] &&
              //                 user.plan!.content.contains('chants') ||
              //             widget.dailyActivity.id == dailyActivityIds['raga'] &&
              //                 user.plan!.content.contains('raga') ||
              //             widget.dailyActivity.id ==
              //                     dailyActivityIds['bedtimeStories'] &&
              //                 user.plan!.content.contains('bedtimeStories'))
              //         ? null
              //         : 'subscription_lock',
              //     buttonText: language['start'],
              //     onPressed: () {
              //       // if (widget.fromGB) {
              //       //   push(NamedRoute.bottomNavBarScreen,
              //       //       arguments: BottomNavArgumnets(index: 1));
              //       // }

              //       // late String route;
              //       // late Object arguments;

              //       if (widget.dailyActivity.id == dailyActivityIds['yoga'] &&
              //           widget.subscriptionScreenImage != null) {
              //         // route = NamedRoute.yogaScreen;
              //         // arguments =
              //         //     YogaArguments(title: widget.dailyActivity.title);
              //         buySubscriptionDialog(widget.subscriptionScreenImage!);
              //       } else if (widget.dailyActivity.id ==
              //               dailyActivityIds['chants'] &&
              //           widget.subscriptionScreenImage != null) {
              //         // route = NamedRoute.chantsScreen;
              //         // arguments =
              //         //     ChantsArguments(title: widget.dailyActivity.title);
              //         buySubscriptionDialog(widget.subscriptionScreenImage!);
              //       } else if (widget.dailyActivity.id ==
              //               dailyActivityIds['meditation'] &&
              //           widget.subscriptionScreenImage != null) {
              //         // route = NamedRoute.meditationScreen;
              //         // arguments = MeditationArguments(
              //         //     title: widget.dailyActivity.title);
              //         buySubscriptionDialog(widget.subscriptionScreenImage!);
              //       } else if (widget.dailyActivity.id ==
              //               dailyActivityIds['bedtimeStories'] &&
              //           widget.subscriptionScreenImage != null) {
              //         // route = NamedRoute.bedtimeStoryScreen;
              //         // arguments = BedtimeStoryArguments(
              //         //     title: widget.dailyActivity.title);
              //         buySubscriptionDialog(widget.subscriptionScreenImage!);
              //       } else if (widget.dailyActivity.id ==
              //               dailyActivityIds['raga'] &&
              //           widget.subscriptionScreenImage != null) {
              //         // route = NamedRoute.ragaMusicScreen;
              //         // arguments =
              //         //     RagaMusicArguments(title: widget.dailyActivity.title);
              //         buySubscriptionDialog(widget.subscriptionScreenImage!);
              //       } else if (widget.dailyActivity.id ==
              //               dailyActivityIds['diet'] &&
              //           widget.subscriptionScreenImage != null) {
              //         // route = NamedRoute.dietScreen;
              //         // arguments =
              //         //     DietArguments(title: widget.dailyActivity.title);
              //         buySubscriptionDialog(widget.subscriptionScreenImage!);
              //       }

              //       // push(route, arguments: arguments).then((value) {
              //       //   if (widget.fromGB) {
              //       //     //   showPreferenceSheet();
              //       //     Provider.of<AuthProvider>(context, listen: false)
              //       //         .editProfile(
              //       //       body: {'completedWalkThrough': 'true'},
              //       //       files: {},
              //       //     );
              //       //   }
              //       // });
              //     },
              //   ),
              // ),
              SizedBox(
                width: dW * 0.2,
                height: dW * 0.1,
                child: GradientStartButton(
                  icon: (widget.dailyActivity.id == dailyActivityIds['diet'] &&
                              user.plan != null &&
                              user.plan!.access.contains('diet')) ||
                          (widget.dailyActivity.id ==
                                  dailyActivityIds['yoga'] &&
                              user.plan != null &&
                              user.plan!.access.contains('yoga')) ||
                          (widget.dailyActivity.id ==
                                  dailyActivityIds['meditation'] &&
                              user.plan != null &&
                              user.plan!.access.contains('meditation')) ||
                          (widget.dailyActivity.id ==
                                  dailyActivityIds['chants'] &&
                              user.plan != null &&
                              user.plan!.access.contains('chants')) ||
                          (widget.dailyActivity.id ==
                                  dailyActivityIds['raga'] &&
                              user.plan != null &&
                              user.plan!.access.contains('raga')) ||
                          (widget.dailyActivity.id ==
                                  dailyActivityIds['bedtimeStories'] &&
                              user.plan != null &&
                              user.plan!.access.contains('bedtimeStories'))
                      ? null
                      : 'subscription_lock',
                  buttonText: language['start'],
                  onPressed: () {
                    late String route;
                    late Object arguments;

                    if (widget.dailyActivity.id == dailyActivityIds['yoga']) {
                      if (user.plan != null &&
                          user.plan!.access.contains('yoga')) {
                        route = NamedRoute.yogaScreen;
                        arguments =
                            YogaArguments(title: widget.dailyActivity.title);
                        push(route, arguments: arguments);
                      } else {
                        if (subscriptionDialog['active'] == true) {
                          buySubscriptionDialog();
                        } else {
                          push(NamedRoute.subscriptionScreen);
                        }
                        // buySubscriptionDialog();
                        // push(NamedRoute.subscriptionScreen);
                      }
                    } else if (widget.dailyActivity.id ==
                        dailyActivityIds['chants']) {
                      if (user.plan != null &&
                          user.plan!.access.contains('chants')) {
                        route = NamedRoute.chantsScreen;
                        arguments =
                            ChantsArguments(title: widget.dailyActivity.title);
                        push(route, arguments: arguments);
                      } else {
                        if (subscriptionDialog['active'] == true) {
                          buySubscriptionDialog();
                        } else {
                          push(NamedRoute.subscriptionScreen);
                        }
                        // buySubscriptionDialog(widget.subscriptionScreenImage!);
                      }
                    } else if (widget.dailyActivity.id ==
                        dailyActivityIds['meditation']) {
                      if (user.plan != null &&
                          user.plan!.access.contains('meditation')) {
                        route = NamedRoute.meditationScreen;
                        arguments = MeditationArguments(
                            title: widget.dailyActivity.title);
                        push(route, arguments: arguments);
                      } else {
                        // buySubscriptionDialog(widget.subscriptionScreenImage!);
                        // push(NamedRoute.subscriptionScreen);
                        if (subscriptionDialog['active'] == true) {
                          buySubscriptionDialog();
                        } else {
                          push(NamedRoute.subscriptionScreen);
                        }
                      }
                    } else if (widget.dailyActivity.id ==
                        dailyActivityIds['bedtimeStories']) {
                      if (user.plan != null &&
                          user.plan!.access.contains('bedtimeStories')) {
                        route = NamedRoute.bedtimeStoryScreen;
                        arguments = BedtimeStoryArguments(
                            title: widget.dailyActivity.title);
                        push(route, arguments: arguments);
                      } else {
                        // buySubscriptionDialog(widget.subscriptionScreenImage!);
                        // push(NamedRoute.subscriptionScreen);
                        if (subscriptionDialog['active'] == true) {
                          buySubscriptionDialog();
                        } else {
                          push(NamedRoute.subscriptionScreen);
                        }
                      }
                    } else if (widget.dailyActivity.id ==
                        dailyActivityIds['raga']) {
                      if (user.plan != null &&
                          user.plan!.access.contains('raga')) {
                        route = NamedRoute.ragaMusicScreen;
                        arguments = RagaMusicArguments(
                            title: widget.dailyActivity.title);
                        push(route, arguments: arguments);
                      } else {
                        // buySubscriptionDialog(widget.subscriptionScreenImage!);
                        // push(NamedRoute.subscriptionScreen);
                        if (subscriptionDialog['active'] == true) {
                          buySubscriptionDialog();
                        } else {
                          push(NamedRoute.subscriptionScreen);
                        }
                      }
                    } else if (widget.dailyActivity.id ==
                        dailyActivityIds['diet']) {
                      if (user.plan != null &&
                          user.plan!.access.contains('diet')) {
                        route = NamedRoute.dietScreen;
                        arguments =
                            DietArguments(title: widget.dailyActivity.title);
                        push(route, arguments: arguments);
                      } else {
                        // buySubscriptionDialog(widget.subscriptionScreenImage!);
                        // push(NamedRoute.subscriptionScreen);
                        if (subscriptionDialog['active'] == true) {
                          buySubscriptionDialog();
                        } else {
                          push(NamedRoute.subscriptionScreen);
                        }
                      }
                    }

                    // push(route, arguments: arguments).then((value) {
                    //   if (widget.fromGB) {
                    //     // showPreferenceSheet();
                    //     Provider.of<AuthProvider>(context, listen: false).editProfile(
                    //       body: {'completedWalkThrough': 'true'},
                    //       files: {},
                    //     );
                    //   }
                    // });
                  },
                ),
              ),
            ],
          ),
          if (!isLastItem)
            Padding(
              padding: EdgeInsets.only(top: dW * 0.07),
              child: const DottedSeperator(
                height: 1,
                width: 3,
                color: Color(0XFFDBDBE3),
              ),
            ),
        ],
      ),
    );
  }
}
