PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - better_player (0.0.1):
    - <PERSON><PERSON> (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Cache (6.0.0)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (10.18.0):
    - FirebaseCore (= 10.18.0)
  - Firebase/DynamicLinks (10.18.0):
    - Firebase/CoreOnly
    - FirebaseDynamicLinks (~> 10.18.0)
  - Firebase/Messaging (10.18.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.18.0)
  - firebase_core (2.23.0):
    - Firebase/CoreOnly (= 10.18.0)
    - Flutter
  - firebase_dynamic_links (5.4.5):
    - Firebase/DynamicLinks (= 10.18.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.7.5):
    - Firebase/Messaging (= 10.18.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.18.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.18.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseDynamicLinks (10.18.0):
    - FirebaseCore (~> 10.0)
  - FirebaseInstallations (10.18.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.12.0):
    - GoogleUtilities/Logger
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - open_file_safe (0.0.1):
    - Flutter
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - phonepe_payment_sdk (1.0.4):
    - Flutter
    - PhonePePayment (= 2.6.0)
  - PhonePePayment (2.6.0)
  - PINCache (3.0.3):
    - PINCache/Arc-exception-safe (= 3.0.3)
    - PINCache/Core (= 3.0.3)
  - PINCache/Arc-exception-safe (3.0.3):
    - PINCache/Core
  - PINCache/Core (3.0.3):
    - PINOperation (~> 1.2.1)
  - PINOperation (1.2.2)
  - PromisesObjC (2.3.1)
  - razorpay-pod (1.3.4)
  - razorpay_flutter (1.1.10):
    - Flutter
    - razorpay-pod
  - record_mp3 (0.0.1):
    - Flutter
  - SDWebImage (5.18.5):
    - SDWebImage/Core (= 5.18.5)
  - SDWebImage/Core (5.18.5)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - SwiftyGif (5.4.4)
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - "webview_pro_wkwebview (2.7.1+1)":
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - better_player (from `.symlinks/plugins/better_player/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_dynamic_links (from `.symlinks/plugins/firebase_dynamic_links/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - open_file_safe (from `.symlinks/plugins/open_file_safe/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - phonepe_payment_sdk (from `.symlinks/plugins/phonepe_payment_sdk/ios`)
  - razorpay_flutter (from `.symlinks/plugins/razorpay_flutter/ios`)
  - record_mp3 (from `.symlinks/plugins/record_mp3/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_pro_wkwebview (from `.symlinks/plugins/webview_pro_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Cache
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FMDB
    - GCDWebServer
    - GoogleDataTransport
    - GoogleUtilities
    - HLSCachingReverseProxyServer
    - nanopb
    - OrderedSet
    - PhonePePayment
    - PINCache
    - PINOperation
    - PromisesObjC
    - razorpay-pod
    - SDWebImage
    - SwiftyGif
    - Toast

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  better_player:
    :path: ".symlinks/plugins/better_player/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_dynamic_links:
    :path: ".symlinks/plugins/firebase_dynamic_links/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  open_file_safe:
    :path: ".symlinks/plugins/open_file_safe/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  phonepe_payment_sdk:
    :path: ".symlinks/plugins/phonepe_payment_sdk/ios"
  razorpay_flutter:
    :path: ".symlinks/plugins/razorpay_flutter/ios"
  record_mp3:
    :path: ".symlinks/plugins/record_mp3/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_pro_wkwebview:
    :path: ".symlinks/plugins/webview_pro_wkwebview/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  better_player: 2406bfe8175203c7a46fa15f9d778d73b12e1646
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  device_info_plus: e5c5da33f982a436e103237c0c85f9031142abed
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: 817ab1d8cd2da9d2da412a417162deee3500fc95
  Firebase: 414ad272f8d02dfbf12662a9d43f4bba9bec2a06
  firebase_core: 29d66baf806970cda37c93621b27cd369b27db1b
  firebase_dynamic_links: 13c08739cb1caf4c3a351078fdf3a40222ed959e
  firebase_messaging: 0a39f2514e1f27b0274b0d2fa99048f57856ee7c
  FirebaseCore: 2322423314d92f946219c8791674d2f3345b598f
  FirebaseCoreInternal: 8eb002e564b533bdcf1ba011f33f2b5c10e2ed4a
  FirebaseDynamicLinks: c37307441c53838d66a9650dabca9e0459502527
  FirebaseInstallations: e842042ec6ac1fd2e37d7706363ebe7f662afea4
  FirebaseMessaging: 9bc34a98d2e0237e1b121915120d4d48ddcf301e
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_inappwebview: 3d32228f1304635e7c028b0d4252937730bbc6cf
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  fluttertoast: 31b00dabfa7fb7bacd9e7dbee580d7a2ff4bf265
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  open_file_safe: 84c01ac91378bd9166ab570d7f22f4fb3532141a
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: 6c92f08e1f853dc01228d6f553146438dafcd14e
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  phonepe_payment_sdk: 3565b5e380bae808f9a8d57b7424fd48df521a29
  PhonePePayment: a23a9cec49aa34eaa7f83c89f7381ea76e1de477
  PINCache: 7a8fc1a691173d21dbddbf86cd515de6efa55086
  PINOperation: daa34d4aa1d8449089be7d405b9d974abc4724c6
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  razorpay-pod: df3aa440969489d242c40ec606db9a0c7095743f
  razorpay_flutter: 84b3bfd206ae9c9c2a9ba585524a1b3d8102b6c1
  record_mp3: b842d3900f65a0707e4064d9bcfac6323dea0b4f
  SDWebImage: 7ac2b7ddc5e8484c79aa90fc4e30b149d6a2c88f
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: 68d46cc9766d0c41dbdc884310529557e3cd7a86
  video_player_avfoundation: 81e49bb3d9fb63dccf9fa0f6d877dc3ddbeac126
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_pro_wkwebview: 38019db9fcfb544f91e1b0e46b11af71a42b6d2c

PODFILE CHECKSUM: 4f5204723e5a8105709b573435d1fbcfeaf5d7f2

COCOAPODS: 1.13.0
