// ignore_for_file: library_private_types_in_public_api, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:provider/provider.dart';

import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../providers/auth_provider.dart';

class PersonalizeLoaderScreen extends StatefulWidget {
  const PersonalizeLoaderScreen({super.key});

  @override
  _PersonalizeLoaderScreenState createState() =>
      _PersonalizeLoaderScreenState();
}

class _PersonalizeLoaderScreenState extends State<PersonalizeLoaderScreen>
    with SingleTickerProviderStateMixin {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late AnimationController _controller;
  double _progressValue = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _controller.addListener(() {
      setState(() {
        _progressValue = _controller.value;
      });
    });

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        pushAndRemoveUntil(NamedRoute.bottomNavBarScreen,
            arguments: BottomNavArgumnets());
      }
    });

    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    return Scaffold(
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GradientWidget(
              gradient: linearGradient,
              child: Text(
                '${(_progressValue * 100).toInt()} %',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: 30, right: 30, top: 12, bottom: dW * 0.1),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: LinearProgressIndicator(
                  value: _progressValue,
                  minHeight: 10,
                  color: const Color(0xffFF328B),
                  backgroundColor: const Color(0xffD9D9D9),
                ),
              ),
            ),
            Text(
              language['allDone'],
              style: Theme.of(context).textTheme.headline1!.copyWith(
                    fontSize: tS * 20,
                    color: const Color(0XFF975EFF),
                  ),
            ),
            SizedBox(height: dW * 0.04),
            Text(
              language['customizing'],
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headline2!.copyWith(
                    fontSize: tS * 14,
                    color: const Color(0XFF1D1E22),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
