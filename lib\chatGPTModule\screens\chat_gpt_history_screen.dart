// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nine_and_beyond/chatGPTModule/providers/chat_gpt_provider.dart';
import 'package:nine_and_beyond/commonWidgets/empty_list_widget.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';
import '../models/chat_gpt_session_model.dart';
import '../widgets/chat_gpt_session_widget.dart';

class ChatGptHistoryScreen extends StatefulWidget {
  const ChatGptHistoryScreen({Key? key}) : super(key: key);

  @override
  ChatGptHistoryScreenState createState() => ChatGptHistoryScreenState();
}

class ChatGptHistoryScreenState extends State<ChatGptHistoryScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  bool isLazyLoading = false;
  final ScrollController _scrollController = ScrollController();

  List<ChatGPTSession> sessions = [];

  String sessionDateText(DateTime date) =>
      (isSameDay(DateTime.now(), date)
          ? language['today'] + ' - '
          : isSameDay(DateTime.now().subtract(const Duration(days: 1)), date)
              ? language['yesterday'] + ' - '
              : '') +
      DateFormat('d MMM, yyyy').format(date);

  fetchAllSessions([refresh = false]) async {
    final response = await Provider.of<ChatGPTProvider>(context, listen: false)
        .fetchAllSessions(
      accessToken: user.accessToken,
      refresh: refresh,
    );

    if (!response['success']) {
      showSnackbar(language[response['message']]);
    }
  }

  init() async {
    setState(() => isLoading = true);
    await fetchAllSessions(true);
    setState(() => isLoading = false);
  }

  lazyLoad() async {
    if (_scrollController.position.extentAfter == 0) {
      setState(() {
        isLazyLoading = true;
      });
      await fetchAllSessions();
      setState(() {
        isLazyLoading = false;
      });
    }
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      lazyLoad();
    }
    return false;
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    Provider.of<ChatGPTProvider>(context, listen: false).clearSessions();
    fetchAllSessions();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    sessions = Provider.of<ChatGPTProvider>(context).sessions;

    return Scaffold(
      appBar: CustomAppBar(
        dW: dW,
        title: language['history'],
        dragDown: pop,
        titleWidget: Container(
          margin: EdgeInsets.only(left: dW * 0.025),
          child: Text(
            language['history'],
            style: Theme.of(context).textTheme.headline1!.copyWith(
                  color: lightBlack,
                  fontSize: 13,
                ),
          ),
        ),
        leadingWidth: dW * 0.12,
        leading: GestureDetector(
          onTap: pop,
          child: Container(
            alignment: Alignment.centerRight,
            decoration: BoxDecoration(
              color: transparentColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.clear_rounded,
              size: 24,
              color: blackColor,
            ),
          ),
        ),
        actions: [],
      ),
      backgroundColor: white,
      body: screenBody(),
    );
  }

  screenBody() {
    return Container(
      height: dH,
      width: dW,
      color: const Color(0xFFF7F8FC),
      child: isLoading
          ? const CircularLoader()
          : Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (isLoading)
                  Padding(
                    padding: EdgeInsets.only(top: dH * 0.2),
                    child: const CircularLoader(),
                  ),
                if (!isLoading) ...[
                  Expanded(
                    child: RefreshIndicator(
                      color: Theme.of(context).primaryColor,
                      onRefresh: () => fetchAllSessions(true),
                      child: NotificationListener<ScrollNotification>(
                        onNotification: _handleScrollNotification,
                        child: ListView(
                            controller: _scrollController,
                            physics: const BouncingScrollPhysics(
                                parent: AlwaysScrollableScrollPhysics()),
                            padding: EdgeInsets.zero,
                            children: [
                              if (sessions.isEmpty)
                                EmptyListWidget(
                                  text: language['noHstryAvlble'],
                                  topPadding: 0.2,
                                  textColor: blackColor,
                                ),
                              ListView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: dW * 0.04),
                                  itemCount: sessions.length,
                                  itemBuilder: ((context, i) => Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.stretch,
                                        children: [
                                          if (i == 0 ||
                                              !isSameDay(
                                                  sessions[i - 1].createdAt,
                                                  sessions[i].createdAt))
                                            Container(
                                              margin: EdgeInsets.only(
                                                top: dW * 0.07,
                                                bottom: dW * 0.04,
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                      sessionDateText(
                                                          sessions[i]
                                                              .createdAt),
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .headline3!
                                                          .copyWith(
                                                            fontSize: tS * 10,
                                                            color: lightBlack,
                                                            letterSpacing: .3,
                                                          )),
                                                ],
                                              ),
                                            ),
                                          GestureDetector(
                                            onTap: () => push(
                                              NamedRoute.chatGptScreen,
                                              arguments: ChatGptScreenArguments(
                                                session: sessions[i].id,
                                              ),
                                            ),
                                            child: ChatGPTSessionWidget(
                                              key: ValueKey(sessions[i].id),
                                              session: sessions[i],
                                              isFirst: i == 0 ||
                                                  !isSameDay(
                                                      sessions[i - 1].createdAt,
                                                      sessions[i].createdAt),
                                              isLast: i ==
                                                      sessions.length - 1 ||
                                                  !isSameDay(
                                                      sessions[i + 1].createdAt,
                                                      sessions[i].createdAt),
                                            ),
                                          ),
                                        ],
                                      ))),
                              if (isLazyLoading) lazyLoader(dW),
                              SizedBox(height: dW * 0.25),
                            ]),
                      ),
                    ),
                  ),
                ],
              ],
            ),
    );
  }
}
