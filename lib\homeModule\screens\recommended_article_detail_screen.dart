// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../common_functions.dart';
import '../../navigation/arguments.dart';
import '../../navigation/navigators.dart';

class ArticleDetailScreen extends StatefulWidget {
  final ArticleDetailScreenArguments args;
  const ArticleDetailScreen({Key? key, required this.args}) : super(key: key);

  @override
  ArticleDetailScreenState createState() => ArticleDetailScreenState();
}

class ArticleDetailScreenState extends State<ArticleDetailScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  fetchData() async {
    setState(() => isLoading = true);
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchData();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    return Scaffold(
      backgroundColor: white,
      appBar: CustomAppBar(
        title: '',
        dW: dW,
        leading: const SizedBox.shrink(),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: dW * 0.05),
            child: GestureDetector(
              onTap: pop,
              child: const Icon(
                Icons.clear_rounded,
                color: blackColor,
              ),
            ),
          )
        ],
      ),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: dW * 0.05),
                  Padding(
                    padding: EdgeInsets.only(left: dW * 0.03, right: dW * 0.03),
                    child: Html(data: widget.args.article.htmlContent),
                  ),
                  SizedBox(height: dW * 0.1),
                ],
              ),
            ),
    );
  }
}
