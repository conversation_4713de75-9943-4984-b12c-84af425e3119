// ignore_for_file: avoid_function_literals_in_foreach_calls, depend_on_referenced_packages

import 'dart:convert';
import 'dart:io';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:http/http.dart' as http;
import '../../http_helper.dart';
import '../model/user_model.dart';
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import '../../api.dart';

class AuthProvider with ChangeNotifier {
  final LocalStorage storage = LocalStorage('9&BEYOND');
  List availableLanguages = [];

  bool showGarbhSanskarAlways = false;
  bool blockDailyActivities = false;

  Map dailyActivityIds = {
    "yoga": "64e4717664ff5f20fceb0d64",
    "chants": "64e4719b64ff5f20fceb0d66",
    "meditation": "64e471d364ff5f20fceb0d68",
    "bedtimeStories": "64e4722164ff5f20fceb0d6a",
    "raga": "64e4727364ff5f20fceb0d6c",
    "diet": "64e472c164ff5f20fceb0d6e",
    "earlyMorning": "64f01dc71e2b9a0138ece5d8",
    "breakFast": "64f01de71e2b9a0138ece5da",
    "midMorning": "650e764ddd0d672be030de42",
    "lunch": "64f01df71e2b9a0138ece5dc",
    "eveningSnack": "64f01e091e2b9a0138ece5de",
    "dinner": "64f01e181e2b9a0138ece5e0",
    "bedTime": "650e764ddd0d672be030de43",
  };

  Map get selectedLanguage => {
        "home": "Home",
        "daily": "Daily",
        "chat": "Chat",
        "diary": "Diary",
        "more": "More",
        "9AndBeyond": "9 & Beyond",
        "holisticApproachToPregnancy": "Holistic approach to pregnancy",
        "knowledgePartner": "Knowledge Partner",
        "manashakti": "Manashakti",
        "getStarted": "Get Started",
        "mobileNumber": "Mobile Number",
        "mobileNumberToProceed": "Please enter your mobile number to proceed",
        "enterMobileNumber": "00000 00000",
        "enterPhoneNumber": "Please enter your mobile number",
        "validPhoneNumber": "Please enter your valid mobile number",
        "getOTP": "Get OTP",
        "byContinueAgree": "By continuing you agree to the ",
        "tos": "Terms of services",
        "and": "and",
        "privacyPolicy": "Privacy policy",
        "verification": "OTP Verification",
        "enterCode": "Please enter the 6-digit code sent to you on",
        "emptyOtp": "Please enter otp",
        "invalidOtp": "Please enter valid otp",
        "somethingWentWrong": "Something went wrong",
        "verifyOtp": "Verify OTP",
        "outOfAttempts": "You are out of attempts. Please Try again later",
        "didntGetOtp": "Didn't get it? ",
        "resend": "Resend",
        "in": "in",
        "or": "Or",
        "noAccount": "Don't have an account?",
        "signup": "Sign Up",
        "makeProfile": "Let's make a profile",
        "personalInfo": "Enter your personal Information",
        "fullName": "Full name",
        "*": "*",
        "enterFullName": "Enter full name",
        "pleaseEnterFullName": "Please enter your full name",
        "emailId": "Email ID",
        "enterEmail": "Enter email ID",
        "pleaseEnterEmail": "Please enter email id",
        "pleaseEnterValidEmail": "Please enter a valid email id",
        "saveAndContinue": "Save & Continue",
        "personalizeTitle": "Let's personalize this app for you!",
        "personalizeSubtitle":
            "Would you like to answer a few questions to help us tailor your experience and provide you with relevant content during your pregnancy journey?",
        "letsStart": "Let's Start",
        "skip": "No, I want to skip",
        "areYouPregnant": "Are you pregnant ?",
        "wantSkip": "I want to skip",
        "pregnantWeeks": "How many weeks pregnant are you?",
        "next": "Next",
        "dontRemember": "I don't remember",
        "congratulations": "Congratulations!",
        "continue": "Continue",
        "yesIAm": "Yes, I am",
        "noButWantTo": "No , but I want to be\npregnant",
        "cnntBePregIfMensDate":
            "You cannot be pregnant right now if your last mentrual date was",
        "personalize": "Personalize your app in",
        "1": " 1",
        "step": "/2 step",
        "2": " 2",
        "24": "24%",
        "allDone": "All Done!",
        "customizing": "We are customizing 9 & Beyond\napplication for you",
        "periodDate": "Enter Your last period date",
        "lastPeriodDate": "Last period Date",
        "dateFormat": "dd/mm/yyyy",
        "menstrualCycle": "Length of your menstrual cycle",
        "emptyIssueValidation": "Please select length of your menstrual cycle",
        "lngthOfMenstruCycle": "Length of your menstrual cycle",
        "select": "Select",
        "message": "message",
        "ok": "Ok",
        "featureComingSoon":
            "Sorry, this feature is not available at\nthe moment, but it's coming soon!",
        "week": "Week",
        "youAre": "You are",
        "weeksPregnant": "weeks pregnant.",
        "expctdDDateIs": "Your expected delivery date is",
        "failedToSave": "failed to update profile",

        // Home
        "recommendedArticle": "Recommended Articles",
        "facts": "Facts Of The Day",
        "mythsAbtPregnancy": "Myths About Pregnancy",
        "babyTracker": "Baby Tracker",
        "askAny": "Ask Anything",
        "getExpert": "Get expert advice & tips ->",
        "testimonials": "Testimonials",
        "testimonialsBy": "Testimonials by Manashakti",
        "faqs": "Frequently asked questions",
        "record": "Record",
        "readMore": "Read Now -->",
        "swipeLeft": "Swipe left to see more ->",
        "nextUpcomingActivity": "Next upcoming activity : ",

        // More
        "editProfile": "Edit Profile",
        "shareApp": "Share App",
        "logOut": "Log Out",
        "t&c": "Terms & conditions",
        "switchPref": "Switch your preference",
        "yourPref": "Your Preference is : ",
        "tnc": "Terms and Conditions",
        "switchPreference": "Switch Preference",
        "yourCurrentPref": "Your Current preference: ",
        "goBack": "Go back",
        "thisCan": "This cannot be changed!",
        "updateChanges": "Update Changes",
        "logout": "Are you sure you want to Logout?",
        "yesLogout": "Yes , Logout",

        // Diary
        "noRecording": "No recordings yet!",
        "voiceDiary": "Voice Diary",
        "embrace": "Embrace the unwritten day, start\nrecording now.",
        "welcomeVoiceDiary":
            "Welcome to your voice diary, where you can record\nthe beautiful moments and emotions of your\npregnancy journey.",
        "startRecording": "Start\nRecording",
        "pastRecordings": "Past Recordings",
        "filter": "Filter",
        "startAgain": "Start Again",
        "play": "Play",
        "save": "Save",
        "stopSave": "Stop & Save",
        "applyFilter": "Apply Filter",
        "reset": "Reset",
        "rename": "Rename",
        "delete": "Delete",
        "cancel": "Cancel",
        "yesDelete": "Yes, Delete",
        "renameAudio": "Rename Audio",
        "enterAudioName": "Enter audio name",
        "areYouSure": "Are you sure you want to Delete recording",
        "pleaseEnterAudioName": "Please enter audio name",
        "failedPlay": "Failed to play recording",
        "minC": "Min",
        "discardRecording": "Save Your recording or discard it?",
        "discard": "Discard",

        // Daily Activities
        "dailyActivities": "Daily Activities",
        "setPreference": "Set preference",
        "yoga": "Yoga",
        "flexibility": "Flexibility",
        "start": "Start",
        "chants": "Chants",
        "tranquilityFocus": "Tranquility & Focus",
        "meditation": "Meditation",
        "relaxation": "Relaxation",
        "bedtimeStories": "Bedtime Stories",
        "bondingBaby": "Bonding with baby",
        "ragaMusic": "Raga Music",
        "soothingHarmony": "Soothing & Harmony",
        "diet": "Diet",
        "healthyPregnancy": "Healthy Pregnancy",
        "min": "min",
        "views": "views",
        "1stTrimester": "1st Trimester",
        "2ndTrimester": "2nd Trimester",
        "3rdTrimester": "3rd Trimester",
        "1st": "1st",
        "2nd": "2nd",
        "3rd": "3rd",
        "4th": "4th",
        "5th": "5th",
        "6th": "6th",
        "7th": "7th",
        "8th": "8th",
        "9th": "9th",
        "share": "Share",
        "moreYoga": "More yoga for",
        "soothingChants": "Soothing chants Recommended for you",
        "setCounter": "Set Counter",
        "setTimer": "Set Timer",
        "startMeditation": "Start Meditation",
        "bravery": "Bravery",
        "humbleness": "Humbleness",
        "emotional": "Emotional",
        "warriors": "Warriors",
        "recommendedStories": "Recommended stories for you",
        "back": "Back",
        "readingTime": "Reading time:",
        "minS": "min",
        "changeSpeed": "Change Speed",
        "audioStop": "Audio stops in",
        "listenSimilar": "Similar stories to listen",
        "recommendedArticles": "More Recommended articles",
        "recommendedContent": "Recommended content for you",
        "ragaMusicRec": "Raga music recommended for you",
        "day": "Day",
        "trimester": "Trimester",
        "month": "month",
        "ingredients": "Ingredients",
        "steps": "Steps",
        "youtube": "Youtube.com",
        "selectTrimester": "Select Trimester",
        "edit": "Edit",
        "skipPreference": "Skip",
        "preferred": "Preferred time for",
        "addMore": "Add more",
        "wordsBy": "Words by:",
        "like": "Like",
        "shareNow": "Share",
        "startReading": "Start Reading",
        "likedStory": "Liked Story",
        "savePreference": "Save Preference",
        "activate": "Activate",
        "remove": "- Remove",
        "pref": "Preffered",
        "time": "time",
        "continueRead": "Continue Reading ->",
        "setCustomCounter": "Set Custom Counter",
        "enterCounter": "Enter Counter",
        "enterValue": "Please enter value for counter",
        "masterClass": "Master Class",

        // Chat GPT
        "askAnything": "Ask anything...",
        "fAQs:": "Frequently asked questions:",
        "isItSafeToExercise": "“Is it safe to exercise during pregnancy?”",
        "whatFoodsToAvoid": "“What foods should I avoid during pregnancy??”",
        "howMuchWeightToGain":
            "“How much weight should I gain during my pregnancy??”",
        "areCounterMedsSafe":
            "“Are over-the-counter medications safe to take during pregnancy?”",
        "newChat": "New Chat",
        "chatHistory": "Chat History",
        "today": "Today",
        "yesterday": "Yesterday",
        "confirmNewChat": "Continue Previous Chat or Start New Chat?",
        "startNew": "Start new",
        "history": "History",
        "responseReceived": "Response received",
        "failedToGetResponse": "Failed to get response",
        "failedToGetSession": "Failed to get session",
        "failedToGetSessions": "Failed to get sessions",
        "credits": "Credits",
        "insfBalInWal": "Insufficient Balance in Your Wallet",
        "noBalDesc":
            "Your chat service has been temporarily paused due to insufficient balance in your wallet. Please top up your wallet now to continue chatting.",
        "addBalance": "Add Balance",
        "addMoreCredToWal": "Add more credit to wallet",
        "note": "Note",
        "question": "question",
        "entrAmtToRchrge": "Enter amount to recharge",
        "plEntrMinAmt": "Please enter minimum",
        "toRchrge": "to recharge.",
        "yourBal": "Your balance",
        "rchrgeAmt": "Recharge Amount",
        "proceed": "Proceed",
        "plsEnterAmt": "Please enter an amount",
        "subtotal": "Subtotal",
        "tax": "Tax",
        "discount": "Discount",
        "grandTotal": "Grand Total",
        "pay": "Pay",
        "purchaseFailed": "Purchase failed",
        "insfcntBal": "You have Insufficient Wallet Credits",
        "wallet": "Wallet",
        "avlblBal": "Available Balance",
        "creditHistory": "Credit History",
        "balAdToWal": "Balance Added To Wallet",
        "qnsAskd": "Questions Asked",
        "failedToGetCredHist": "Failed to get Credit history",
        "noHstryAvlble": "No history available",

        // Home
        "video": "Video",
        "welcomeBack": "Welcome Back",
        "knwAbtGrbSnskr": "Know About Garbh Sanskar",
        "wtIsGrbSnskr": "What is Garbh Sanskar?",
        "grbSnskrIsScitfclyProvnCncpt":
            "Garbh Sanskar, a scientifically proven concept.",
        "prvideYrDeets": "Provide your details.",
        "completed": "Completed",
        "watchWalkThruVid": "Watch walkthrough video.",
        "toGetBestOfApp": "To get the best out of this App",
        "startNow": "Start Now",
        "knowMore": "Know More",
        "grbSnskr": "Garbh Sanskar",
        "wtIsGrbSnskrNdWhyImp": "What is Garbh Sanskar & why it's important?",
        "readArtclToKnowGB":
            "Read these articles to know more about garbh sanskar",
        "failedToGetArticles": "Failed to get articles",
        "failedToGetRecommendedArticles": "Failed to get recommended articles",
        "article": "Article",
        "appWalkThrough": "App Walkthrough",
        "wlcmToYrGrbSnskrJrny": "Welcome to Your Garbhsanskar Journey!",
        "wlcmToJrnySubTxt":
            "Embrace blissful pregnancy with our holistic app. Let's\nnurture life together! 🌟🤰🎶📚",
        "slctOneToBgnJrny":
            "Select any one from below to begin your journey with what we offer.",
        "timePrefForDA": "Time preference for daily activities",
        "continueWithThis": "Continue with this",
        "editPref": "Edit Preference",
        "sound": "Sound",
        "toGetStarted":
            "To get started with daily activities complete the\nabove steps",
        "started":
            "To get started with daily activities complete the above steps",
        "toGet":
            "To get started with daily activities complete\nthe above steps",
        "dailyStructured": "Daily Structured Activities for you",
        "goToStories": "Go to Stories",
        "tapHeart": "Tap heart button to start saving your favorit\nStories",
        "favEmpty": "Your Favorites is empty.",
        "likedStories": "Liked stories",
        "story": "story",

        // Baby Tracker
        "failedToGetBabyData": "Failed to get baby data",
        "bYouAre": "You are",
        "weeks": "Weeks",
        "pregnant": "Pregnant",
        "seeBabyGrowth": "See Your baby's growth -->",
        "yourBabySizeLike": "Your baby's size is like ",
        "noDataAvailble": "No data available",

        //Delete account
        "yes": "Yes",
        "no": "No",

        //Subscription
        "unlockPremiumFeatures": "Unlock Premium Features",
        "toUnlock":
            "To unlock premium features, daily structured activities along with our live pregnancy & Garbh Sanskar Course.",
        "clickHereToSubscribe": "Click here to subscribe",
        "close": "Close",
        "9&Beyond": "9&Beyond",
        "discover":
            "Discover 9 & Beyond the Ultimate Pregnancy Companion App 📱 And Get Our Pregnancy & Garbh Sanskar Course Free",
        "youWillGet":
            "You Will Get Access To Our Premium Features Like. Weekly Baby Tracker, Voice Dairy, CHATGPT And Also Enjoy These Exclusive Daily Structured Activities:",
        "whatYouAll":
            "What You'll Gain from Our Pregnancy & Garbh Sanskar Course",
        "locked": "Locked",
      };

  late User user;
  String razorpayId = 'rzp_live_SADgg4dpiG8ML8';
  String banner = '';
  String subscriptionScreenImage = '';
  Map plan = {};
  Map subscriptionDialog = {};
  Map masterClass = {};

  String androidVersion = '1';
  String iOSVersion = '1';
  Map? deleteFeature;
  num questionRate = 1;
  num minimumRecharge = 50;

  updateCredits(num gptCredits) {
    user.gptCredits = gptCredits;
    notifyListeners();
  }

  updateMasterClassCount(String masterClassCount) {
    user.masterClassCount = masterClassCount;
    notifyListeners();
  }

  List chatFAQs = [
    'isItSafeToExercise',
    'whatFoodsToAvoid',
    'howMuchWeightToGain',
    'areCounterMedsSafe',
  ];

  Map garbhSanskarVideos = {};

  sendOTPtoUser(String mobileNo, {bool business = false}) async {
    final url = '${webApi['domain']}${endPoint['sendOTPtoUser']}';
    Map body = {
      'mobileNo': mobileNo,
    };
    try {
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, body: body);

      return response;
    } catch (error) {
      return {'success': false, 'login': false};
    }
  }

  resendOTPtoUser(String mobileNo, String type) async {
    final url = '${webApi['domain']}${endPoint['resendOTPtoUser']}';
    Map body = {
      'mobileNo': mobileNo,
      "type": type,
    };
    try {
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, body: body);

      return response['result']['type'];
    } catch (error) {
      return {'success': false, 'login': false};
    }
  }

  verifyOTPofUser(String mobileNo, String otp) async {
    final url = '${webApi['domain']}${endPoint['verifyOTPofUser']}';
    Map body = {
      'mobileNo': mobileNo,
      "otp": otp,
    };
    try {
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, body: body);

      return response['result']['type'];
    } catch (error) {
      return {'success': false, 'login': false};
    }
  }

// get app config from DBDB
  getAppConfig(List<String> types) async {
    final url = '${webApi['domain']}${endPoint['getAppConfigs']}';
    try {
      final response = await RemoteServices.httpRequest(
          method: 'POST', url: url, body: {"types": types});
      if (response['success']) {
        (response['result'] as List).forEach((config) {
          if (config['type'] == 'showGarbhSanskarAlways') {
            showGarbhSanskarAlways = config['value'] ?? false;
          }
          if (config['type'] == 'Banner') {
            banner = config['value'] ?? '';
          }
          if (config['type'] == 'SubscriptionScreenImage') {
            subscriptionScreenImage = config['value'] ?? '';
          }
          if (config['type'] == 'SubscriptionDialog' &&
              config['value'] is Map) {
            subscriptionDialog = config['value'] ?? {};
          }
          if (config['type'] == 'Plan' && config['value'] is Map) {
            plan = config['value'] ?? {};
          }
          if (config['type'] == 'blockDailyActivities') {
            blockDailyActivities = config['value'] ?? false;
          }
          if (config['type'] == 'gptQuestionCharge') {
            questionRate = config['value'] ?? false;
          }
          if (config['type'] == 'delete_feature') {
            deleteFeature = Platform.isAndroid
                ? config['value']['android']
                : config['value']['iOS'];
          }
          if (config['type'] == 'Razorpay') {
            razorpayId = config['value'] ?? razorpayId;
          }
          if (config['type'] == 'GarbhSanskarVideos' &&
              config['value'] is Map) {
            garbhSanskarVideos = config['value'] ?? {};
          }
          if (config['type'] == 'MasterClass' && config['value'] is Map) {
            masterClass = config['value'] ?? '';
          }
        });
      }
      return response;
//
    } catch (error) {
      return {'success': false};
    }
  }

  setLanguageInStorage(String language) async {
    await storage.ready;
    storage.setItem('language', json.encode({"language": language}));
    notifyListeners();
  }

  Future login({required String query}) async {
    //  String? fcmToken = await FirebaseMessaging.instance.getToken();
    // if (fcmToken != null && fcmToken != '') {
    //   query += '&fcmToken=$fcmToken';
    // }

    try {
      final url = '${webApi['domain']}${endPoint['login']}$query';
      final response =
          await RemoteServices.httpRequest(method: 'GET', url: url);

      if (response['success'] && response['login']) {
        user = User.jsonToUser(
          response['result'],
          accessToken: response['accessToken'],
        );

        //  user.fcmToken = fcmToken ?? '';

        await storage.ready;
        await storage.setItem(
            'accessToken',
            json.encode({
              "token": user.accessToken,
              "phone": user.phone,
            }));
      }
      notifyListeners();
      return response;
    } catch (error) {
      return {'success': false, 'login': false};
    }
  }

  Future register(
      {required Map<String, String> body,
      required Map<String, String> files}) async {
    // String? fcmToken = await FirebaseMessaging.instance.getToken();
    // if (fcmToken != null && fcmToken != '') {
    //   body['fcmToken'] = fcmToken;
    // }

    try {
      final url = '${webApi['domain']}${endPoint['register']}';
      final response = await RemoteServices.formDataRequest(
        method: 'POST',
        url: url,
        body: body,
        files: files,
      );

      if (response['success']) {
        user = User.jsonToUser(
          response['result'],
          accessToken: response['accessToken'],
        );

        // user.fcmToken = fcmToken ?? '';

        await storage.ready;
        await storage.setItem(
            'accessToken',
            json.encode({
              "token": user.accessToken,
              "phone": user.phone,
            }));
      }
      notifyListeners();
      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToRegister'};
    }
  }

  Future editProfile(
      {required Map<String, String> body,
      required Map<String, String> files}) async {
    try {
      final url = '${webApi['domain']}${endPoint['editProfile']}';
      final response = await RemoteServices.formDataRequest(
        method: 'PUT',
        url: url,
        body: body,
        files: files,
        accessToken: user.accessToken,
      );

      if (response['success']) {
        if (body['completedWalkThrough'] != null) {
          user.completedWalkThrough =
              response['result']['completedWalkThrough'] ?? false;
        } else {
          user = User.jsonToUser(
            response['result'],
            accessToken: user.accessToken,
          );
        }
      }
      notifyListeners();
      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToSave'};
    }
  }

  logout() async {
    // user = null;
    // await deleteFCMToken();
    await storage.clear();
    notifyListeners();
    return true;
  }

  deleteFCMToken() async {
    Map<String, String> body = {'fcmToken': user.fcmToken};

    final String url = '${webApi['domain']}${endPoint['deleteFCMToken']}';
    try {
      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        body: body,
        accessToken: user.accessToken,
      );

      if (!response['success']) {
      } else {
        notifyListeners();
        return;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  fetchPolicy(String type) async {
    final url = '${webApi['domain']}${endPoint['getAppConfigs']}';
    try {
      final response =
          await RemoteServices.httpRequest(method: 'POST', url: url, body: {
        "types": [type]
      });
      if (response['success'] && response['result'] != null) {
        return response['result'][0];
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }

  deleteAccount() async {
    final String url = '${webApi['domain']}${endPoint['deleteAccount']}';
    try {
      final response = await RemoteServices.httpRequest(
        method: 'PUT',
        url: url,
        accessToken: user.accessToken,
      );

      if (!response['success']) {
      } else {}

      notifyListeners();
      return response;
    } catch (e) {
      return {'success': false, 'message': 'deleteAccountFail'};
    }
  }

  refreshUser() async {
    final String url = '${webApi['domain']}${endPoint['refreshUser']}';
    try {
      final response = await RemoteServices.httpRequest(
        method: 'GET',
        url: url,
        accessToken: user.accessToken,
      );

      if (response['success']) {
        user =
            User.jsonToUser(response['result'], accessToken: user.accessToken);

        notifyListeners();
      }

      // notifyListeners();
      return response;
    } catch (e) {
      return {'success': false, 'message': 'failedToRefresh'};
    }
  }

  Future getAwsSignedUrl({
    required String fileName,
    required String filePath,
    // required Map<String, String> files,
    // required Map<String, String> body,
  }) async {
    //  String? fcmToken = await FirebaseMessaging.instance.getToken();
    // if (fcmToken != null && fcmToken != '') {
    //   query += '&fcmToken=$fcmToken';
    // }

    try {
      final url = '${webApi['domain']}${endPoint['getAwsSignedUrl']}/$fileName';
      final response =
          await RemoteServices.httpRequest(method: 'GET', url: url);

      if (response['success']) {
        final s3Response = await http.put(
          Uri.parse(response['result']),
          body: File(filePath).readAsBytesSync(),
        );

        if (s3Response != null) {
          showSnackbar(s3Response.toString());
        }
      }
      notifyListeners();
      return response;
    } catch (error) {
      return {'success': false, 'message': 'failedToGetSignedUrl'};
    }
  }
}
