// ignore_for_file: deprecated_member_use
import 'package:flutter/material.dart';
import 'package:nine_and_beyond/colors.dart';
import 'package:provider/provider.dart';
import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../commonWidgets/asset_svg_icon.dart';
import '../../commonWidgets/cached_image_widget.dart';
import '../../commonWidgets/gradient_widget.dart';
import '../../navigation/navigators.dart';
import '../../navigation/routes.dart';

class ProfileWidget extends StatefulWidget {
  const ProfileWidget({super.key});

  @override
  State<ProfileWidget> createState() => _ProfileWidgetState();
}

class _ProfileWidgetState extends State<ProfileWidget> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;

  late User user;

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;
    user = Provider.of<AuthProvider>(context).user;
    return Container(
      padding: EdgeInsets.only(
          top: dW * 0.04, bottom: dW * 0.04, left: dW * 0.05, right: dW * 0.05),
      margin: EdgeInsets.only(
          left: dW * 0.04, right: dW * 0.04, top: dW * 0.06, bottom: dW * 0.06),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: const Color(0XFFFFFFFF)),
      child: Row(
        children: [
          user.avatar.isNotEmpty
              ? Container(
                  decoration: const BoxDecoration(
                    color: Color(0XFF975EFF),
                    shape: BoxShape.circle,
                  ),
                  padding: const EdgeInsets.all(2),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(100),
                    child: CachedImageWidget(
                      user.avatar,
                      boxFit: BoxFit.cover,
                      width: dW * 0.2,
                      height: dW * 0.2,
                    ),
                  ),
                )
              : CircleAvatar(
                  backgroundImage:
                      const AssetImage('assets/images/hp_lady.png'),
                  backgroundColor: const Color(0XFF975EFF),
                  radius: dW * 0.11,
                ),
          SizedBox(width: dW * 0.05),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 5),
                child: Text(
                  user.fullName,
                  style: textTheme.headline2!.copyWith(
                      fontSize: tS * 14, color: const Color(0XFF1D1E22)),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 5),
                child: Row(
                  children: [
                    Text(
                      '+91',
                      style: textTheme.headline2!.copyWith(
                          fontSize: tS * 10, color: const Color(0XFF84858E)),
                    ),
                    SizedBox(width: dW * 0.007),
                    Text(
                      user.phone.substring(0, 5),
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 10,
                        color: const Color(0XFF84858E),
                      ),
                    ),
                    SizedBox(width: dW * 0.007),
                    Text(
                      user.phone.substring(5),
                      style: textTheme.headline2!.copyWith(
                        fontSize: tS * 10,
                        color: const Color(0XFF84858E),
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () {
                  push(NamedRoute.editProfileScreen);
                },
                child: Container(
                  color: transparentColor,
                  margin: const EdgeInsets.symmetric(vertical: 5),
                  child: Row(
                    children: [
                      GradientWidget(
                        gradient: const LinearGradient(
                          colors: [
                            Color(0xffCE1B69),
                            Color(0xffFF328B),
                          ],
                        ),
                        child: Text(
                          language['editProfile'],
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Inter',
                          ),
                        ),
                      ),
                      SizedBox(width: dW * 0.01),
                      const AssetSvgIcon(
                        'editProfile_Icon',
                        color: Color(0XFFFF328B),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
