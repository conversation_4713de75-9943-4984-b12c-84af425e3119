// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/commonWidgets/circular_loader.dart';
import 'package:nine_and_beyond/commonWidgets/custom_button.dart';
import 'package:nine_and_beyond/commonWidgets/gradient_button.dart';
import 'package:nine_and_beyond/common_functions.dart';
import 'package:nine_and_beyond/navigation/arguments.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../dailyActivityModule/model/daily_activity_model.dart';
import '../../dailyActivityModule/providers/daily_activity_provider.dart';
import '../../navigation/navigators.dart';
import 'default_preference_widget.dart';

class PreferencesBottomSheet extends StatefulWidget {
  const PreferencesBottomSheet({super.key});

  @override
  State<PreferencesBottomSheet> createState() => _PreferencesBottomSheetState();
}

class _PreferencesBottomSheetState extends State<PreferencesBottomSheet> {
  //
  Map language = {};
  double dW = 0.0;
  double tS = 0.0;
  TextTheme get textTheme => Theme.of(context).textTheme;
  late User user;

  bool isLoading = false;

  List<Preferences> preferences = [];

  fetchPreference() async {
    setState(() => isLoading = true);
    final response =
        await Provider.of<DailyActivityProvider>(context, listen: false)
            .fetchPreferences(
      accessToken: user.accessToken,
    );

    setState(() => isLoading = false);

    if (!response['success']) {
      showSnackbar(response['message']);
    }
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchPreference();
  }

  @override
  Widget build(BuildContext context) {
    dW = MediaQuery.of(context).size.width;
    final dH = MediaQuery.of(context).size.height;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    preferences = Provider.of<DailyActivityProvider>(context).preference;

    user = Provider.of<AuthProvider>(context).user;

    return Container(
      decoration: const BoxDecoration(
          color: white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          )),
      width: dW,
      height: dH * 0.9,
      padding: EdgeInsets.only(top: dW * 0.1, bottom: dW * 0.05),
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(language['timePrefForDA'],
                      style: textTheme.headline1!.copyWith(
                        fontSize: tS * 14,
                        color: lightBlack,
                      )),
                  isLoading
                      ? Padding(
                          padding: EdgeInsets.only(top: dH * 0.2),
                          child: const CircularLoader(),
                        )
                      : ListView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.only(
                            top: dW * 0.08,
                            left: dW * 0.07,
                            right: dW * 0.07,
                          ),
                          shrinkWrap: true,
                          itemCount: preferences.length,
                          itemBuilder: ((context, i) => Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  if (i == 0 ||
                                      preferences[i].dailyActivity.isDiet !=
                                          preferences[i - 1]
                                              .dailyActivity
                                              .isDiet)
                                    Padding(
                                      padding: EdgeInsets.only(
                                        bottom: dW * 0.03,
                                        top: dW * 0.01,
                                      ),
                                      child: Text(
                                        language[
                                            preferences[i].dailyActivity.isDiet
                                                ? 'diet'
                                                : 'sound'
                                                    ''],
                                        style: textTheme.headline3!.copyWith(
                                          fontSize: tS * 10,
                                          color: const Color(0xFF707070),
                                        ),
                                      ),
                                    ),
                                  DefaultPreferenceWidget(
                                    key: ValueKey(preferences[i].id),
                                    preference: preferences[i],
                                  ),
                                ],
                              )))
                ],
              ),
            ),
          ),
          SizedBox(
            width: dW * 0.85,
            child: GradientButton(
              onPressed: pop,
              buttonText: language['continueWithThis'],
            ),
          ),
          SizedBox(height: dW * 0.04),
          GestureDetector(
            onTap: () {
              pop();
              push(NamedRoute.setPreferenceScreen,
                  arguments:
                      SetPreferenceArguments(title: language['setPreference']));
            },
            child: Text(
              language['editPref'],
              style: textTheme.headline2!.copyWith(
                fontSize: tS * 14,
                color: const Color(0xFF84858E),
              ),
            ),
          ),
          SizedBox(height: dW * (iOSCondition(dH) ? 0.05 : 0.04)),
        ],
      ),
    );
  }
}
