// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:nine_and_beyond/chatGPTModule/providers/chat_gpt_provider.dart';
import 'package:nine_and_beyond/navigation/routes.dart';
import 'package:provider/provider.dart';

import '../../authModule/model/user_model.dart';
import '../../authModule/providers/auth_provider.dart';
import '../../colors.dart';
import '../../commonWidgets/bottom_aligned_widget.dart';
import '../../commonWidgets/circular_loader.dart';
import '../../commonWidgets/custom_app_bar.dart';
import '../../commonWidgets/dotted_seperator.dart';
import '../../commonWidgets/gradient_button.dart';
import '../../common_functions.dart';
import '../../navigation/navigators.dart';
import '../models/credit_transaction_model.dart';
import '../widgets/credit_transaction_widget.dart';
import '../widgets/question_charge_widget.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({Key? key}) : super(key: key);

  @override
  WalletScreenState createState() => WalletScreenState();
}

class WalletScreenState extends State<WalletScreen> {
  double dH = 0.0;
  double dW = 0.0;
  double tS = 0.0;
  late User user;
  Map language = {};
  bool isLoading = false;
  TextTheme get textTheme => Theme.of(context).textTheme;

  List<CreditTransaction> transactions = [];

  fetchCreditHistory() async {
    setState(() => isLoading = true);
    final response = await Provider.of<ChatGPTProvider>(context, listen: false)
        .fetchCreditHistory(
      accessToken: user.accessToken,
    );
    setState(() => isLoading = false);
  }

  @override
  void initState() {
    super.initState();

    user = Provider.of<AuthProvider>(context, listen: false).user;
    fetchCreditHistory();
  }

  @override
  Widget build(BuildContext context) {
    dH = MediaQuery.of(context).size.height;
    dW = MediaQuery.of(context).size.width;
    tS = MediaQuery.of(context).textScaleFactor;
    language = Provider.of<AuthProvider>(context).selectedLanguage;

    user = Provider.of<AuthProvider>(context).user;
    transactions = Provider.of<ChatGPTProvider>(context).creditTransactions;

    return Scaffold(
      appBar: CustomAppBar(title: language['wallet'], dW: dW),
      body: iOSCondition(dH) ? screenBody() : SafeArea(child: screenBody()),
    );
  }

  screenBody() {
    return SizedBox(
      height: dH,
      width: dW,
      child: isLoading
          ? const Center(child: CircularLoader())
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    padding: screenHorizontalPadding(dW),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        SizedBox(height: dW * 0.05),
                        QuestionChargeWidget(),
                        Container(
                          margin: EdgeInsets.only(top: dW * 0.035),
                          padding: EdgeInsets.symmetric(
                            horizontal: dW * 0.05,
                            vertical: dW * 0.06,
                          ),
                          decoration: BoxDecoration(
                            color: white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: shadow,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                language['avlblBal'],
                                style: Theme.of(context)
                                    .textTheme
                                    .headline3!
                                    .copyWith(
                                      fontSize: tS * 12,
                                      color: const Color(0xFF6B6C75),
                                    ),
                              ),
                              SizedBox(height: dW * 0.04),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Image.asset(
                                    'assets/images/credit.png',
                                    width: 32,
                                    height: 32,
                                  ),
                                  SizedBox(width: dW * 0.03),
                                  Text(
                                    '\u20b9${convertAmountString(user.gptCredits.toDouble())}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headline1!
                                        .copyWith(
                                          fontSize: tS * 28,
                                          color: lightBlack,
                                        ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                            margin: EdgeInsets.symmetric(vertical: dW * 0.06),
                            child: const DottedSeperator(
                                color: Color(0xFF8EBEF4))),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              language['creditHistory'],
                              style: Theme.of(context)
                                  .textTheme
                                  .headline2!
                                  .copyWith(
                                    fontSize: tS * 12,
                                    color: const Color(0xFF84858E),
                                  ),
                            ),
                          ],
                        ),
                        ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.only(top: dW * 0.05),
                            shrinkWrap: true,
                            itemCount: transactions.length,
                            itemBuilder: ((context, i) =>
                                CreditTransactionWidget(
                                  key: ValueKey(transactions[i].id),
                                  transaction: transactions[i],
                                )))
                      ],
                    ),
                  ),
                ),
                BottomAlignedWidget(
                    dW: dW,
                    dH: dH,
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: dW * 0.02),
                      child: SizedBox(
                        height: dW * 0.14,
                        child: CustomGradientButton(
                          isLoading: false,
                          buttonText: language['addBalance'],
                          onPressed: () => push(NamedRoute.rechargeWalletScreen)
                              .then((value) {
                            if (value) {
                              fetchCreditHistory();
                            }
                          }),
                        ),
                      ),
                    ))
              ],
            ),
    );
  }
}
